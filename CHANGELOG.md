# Changelog

- Dates are in Singapore time zone (UTC+8)
- Doesn't include small & quick fixes

## December 15, 2022

📢 https://mastodon.social/@cheeaun/109515757404630583

- Initial launch

## December 16, 2022

📢 https://mastodon.social/@cheeaun/109540685870427959

- 📖 "Read more" for long-form posts
- 🤘 Profile metadata in Account sheet
- 💬 "Only mentions" filter in Notifications
- 🛗 Nested comments + pagination + collapsible comments

## December 24, 2022

📢 https://mastodon.social/@cheeaun/109569389041297558

- 🍰 :shortcode: expander in Compose field
- ⏲️ character count indicator same as <PERSON><PERSON><PERSON>'s front-end (including twitter-text's URL regex)
- 🙈 "Invisible Ink" effect for spoilers

## December 28, 2022

📢 https://mastodon.social/@cheeaun/109591442248622453

- 🔠 Language selector in Compose field
- 🐛 Bug fixes

## January 1, 2023

📢 https://mastodon.social/@cheeaun/109613657018189874

- ⌨️ Small list of keyboard shortcuts (j, k, o, meta+enter, esc)
- 🐛 Bug fixes

## January 6, 2023

📢 https://mastodon.social/@cheeaun/109642981521972938

- 🙈 Auto-hide header
- 💅 Small UI changes
- 🐛 Bug fixes

## January 17, 2023

📢 https://mastodon.social/users/cheeaun/statuses/109704010841850370

- 🔢 Thread count indicator
- 🔐 Handle accept/reject follow requests for locked accounts
- 📬 Unsent draft recovery
- 🎠 Boosts Carousel™️
- 🐛 Bug fixes

## February 1, 2023

📢 https://mastodon.social/@cheeaun/109785026971372208

- 🔰 ALT badge inside media modals
- 👀 "Show all sensitive content" menu option for threads
- 🧵 Improved Threads UI; smarter collapsing, expand/collapse button with avatars, nesting up to level 4
- 🪟 Media modal appears beside the Detail page for large screens
- 🐛 Bug fixes

## February 19, 2023

📢 https://mastodon.social/@cheeaun/109892084130563955

- 🍰 New menu + pages: Lists, Followed Hashtags, Bookmarks, Favourites, Search, Local, Federated
- 📝 Support edit media description for posted statuses (Mastodon v4.1.0)
- ⚡ Shortcuts™️ (beta) — quick jumping between configured shortcuts
- 🏛️ Experimental multi-column mode, derived from Shortcuts™️
- 🐛 Bug fixes

## March 2, 2023

📢 https://mastodon.social/@cheeaun/109954214422731984

- 🔗 Handle mastodon post links
- #️⃣ Multi-hashtag timeline (5 max)
- 🥞 Context menu for posts in timeline (tap on timestamp)
- 📑 New tab/menu-bar UI for Shortcuts™️
- 🐛 Bug fixes

## March 15, 2023

📢 https://mastodon.social/@cheeaun/110027507724432132

- 👥 Grouped favourites AND boosts notifications
- 🔤 Configurable text size
- 💱 Translation
  - Works for spoiler/CW text, post content, poll text AND image description.
  - Changeable "source" language selector for intricate cases like multilingual sentences.
- ✨ Beautiful profile sheets and pages
- 🚸 Additional prompt when posting or boosting statuses with media that has no description.
- 🐛 Bug fixes

## March 31, 2023

📢 https://mastodon.social/@cheeaun/110118589616753266

- ❌ Delete post
- 🙅 Mention, Mute, Block account
- 🪣 (Partial) Filtered posts UI
- 👥 Posts context grouping in timelines
- 👥 Boosts deduplication (beta)
- 😉 Custom emoji picker
- 🤏 Pinch-zoom for images. Ctrl/Cmd+scroll for pointers.
- 🙈 Setting to hide "Translate" button for specific languages
- 💱 Translate bio, including profile metadata
- 🎚️ Comments thread UI now nest to ∞ levels
- 🐛 Bug fixes

## April 7, 2023

📢 https://mastodon.social/@cheeaun/110157897526115895

- 🪣 Filter bar for profile page statuses with these filters:
  - Include replies
  - Exclude boosts
  - Media only
  - Featured hashtags
- 🛒 Lists CRUD and Add/Remove accounts in Lists
- 📑 Sort Followed hashtags
- 📈 New "Trending" timeline page
- ＠ New "Mentions" page
- ✉️ Show "Private mention" badge
- ⚛️ Boosted/favourited by… accounts list
- 🐛 Bug fixes

## April 25, 2023

📢 https://mastodon.social/@cheeaun/110260144082803624

- #️⃣ Allow hashtag timeline from other instances
- ✏️ Edit Shortcuts
- 🔇 Mute conversation
- 👀 "Full" & "Side peek" views for Status page
- 📶 Slight redesign for multi-column UI
- 🔗 Handle quick post URLs e.g. phanpy.social/#/https://[POST_URL] - a feature from #Elk
- 🧸 Experimental hashtag stuffing collapsing
- 🚀 Quote posts rendering
- 🪄 Experimental cloak mode
- 🐛 Bug fixes

New matrix chat room: https://matrix.to/#/%23phanpy:matrix.org

## May 11, 2023

📢 https://mastodon.social/@cheeaun/110350569225129255

- 🔔 Notification popover, for larger viewports
- ✨ New experiment: auto-refresh timeline posts
- 🔐 "Private" tab, for Private Mentions, in Mentions page
- 🫴 "Follow requests" section in Notifications page
- 📢 "Announcements" section in Notifications page
- 🐛 Bug fixes

## October 2, 2023

📢 https://mastodon.social/@cheeaun/111166122069800214

- 🔔 Web Push notifications (still beta! feedback welcomed 🙏)
- ⌨️ More keyboard shortcuts
- 👀 Profile followers and following list
- 📊 Profile posting stats, shown for accounts that you're not following
- 💁‍♂️ Alt badge — but will conditionally show as inline captions
- 👤 Handle memorial and moved accounts
- 📜 Muted and blocked users list
- 🐛 Bug fixes

## October 26, 2023

📢 https://mastodon.social/@cheeaun/111301339529679119

- 🗓️ Experimental month filter for posts on profile page
- 🗞️ Trending news carousel
- 🫰 Replace "Favourite" with "Like"
- 🐛 Bug fixes

## November 6, 2023

📢 https://mastodon.social/@cheeaun/111363831401633676

- 🏟️ Media gallery layout for hashtag page and profile page (media-only filter)
- 🫰 Small UI touches
- 🐛 Bug fixes

## December 22, 2023

📢 https://mastodon.social/@cheeaun/111624848853096639

- #️⃣ Followed hashtag indicators for non-following posts
- ↔️ Following/followed-by/mutual states in accounts list
- 🔙 Adopt `CloseWatcher` for handling back gesture/button on Chrome Android v120+ https://chromestatus.com/feature/****************
- 🗣️ "Speak" menu item for supported browsers
- 🔎 Allow add "Search" in Shortcuts/Columns
- 🛰️ Community deployments https://github.com/cheeaun/phanpy?tab=readme-ov-file#community-deployments
- 🐛 Bug fixes

## February 25, 2024

📢 https://mastodon.social/@cheeaun/111992491212397964

- 👀 Show lists containing account in the menu
- 🍽️ Redesigned context menu
- 🐛 Bug fixes

## March 6, 2024

📢 https://mastodon.social/@cheeaun/112048317932964165

- ⏱️ Catch-up (beta) ✨🆕✨
- 🚩 Report post/account
- ⛏️ Static HTML embed code for posts
- 🐛 Bug fixes

## March 26, 2024

📢 https://mastodon.social/@cheeaun/112162530547628273

- 🌪️ Keyword filters UI
- 📝 Allow 'Lists' in Shortcuts (except multi-column view)
- 🖋️ Edit Profile (only name, bio & extra fields)
- 🐛 Bug fixes

For upcoming Mastodon v4.3+:
- 🪣 Filtered notifications https://github.com/mastodon/mastodon/pull/29433
- 💔 Handle "severed relationships" notifications https://github.com/mastodon/mastodon/pull/27511 + https://github.com/mastodon/mastodon/pull/29706

For non-Mastodon servers:
- 😉 Best-effort render emoji reactions and notifications (not the reacting part)

## April 17, 2024

📢 https://mastodon.social/@cheeaun/112286757479031305

- 📸 Media-first UI experience
  - Only for @Pixelfed accounts, for now.
  - Still buggy, so need more feedback.
- 🎁 GIF picker (disabled by default)
- 🐛 Bug fixes

## May 6, 2024

📢 https://mastodon.social/@cheeaun/112394549895936563

- 👾 Custom emoji search. Hoverable & keyboard-accessible tooltips of shortcodes, with fuzzy search.
- 🐛 Bug fixes

## June 3, 2024

📢 https://mastodon.social/@cheeaun/112552481684496462

- ⬇️ Allow minimize composer https://mastodon.social/@cheeaun/112502500959822353
- ⚠️ Subtle warning if media file size or dimensions are too large https://mastodon.social/@cheeaun/112512838876542558
- 🗣️ Experimental client-side language detection for unspecified-language posts and composer. 80 to ~95% accuracy, 24 languages, trained dataset from Tatoeba & UDHR, 34KB gzip, powered by TinyLD (light) https://github.com/komodojp/tinyld
- 🐛 Bug fixes

## June 23, 2024

📢 https://mastodon.social/@cheeaun/112665474700277373

- 🏞️ Non-grid layout for media on post pages https://mastodon.social/@cheeaun/112592781655285124
- 🔗 Posts timeline for trending link (upcoming Mastodon v4.3) https://mastodon.social/@cheeaun/112621183860142350
- 🐛 Bug fixes

## July 22, 2024

📢 https://mastodon.social/@cheeaun/112830006096769732

- 🔔 Experimental opt-in server-side grouped notifications (for upcoming Mastodon v4.3, announced here https://oisaur.com/@renchap/112812306472121018) — it's a bit of a "downgrade" but worth testing. Described as "Potentially improved grouping window but basic grouping logic".
- 🪣 Grouped filtered posts inside boosts carousel
- 🐛 Bug fixes

## August 31, 2024

📢 https://mastodon.social/@cheeaun/113056969576068031

- 💬 Internationalization https://mastodon.social/@cheeaun/112932794480750449
  - 15 languages with >50% translated: Basque, Catalan, Chinese (Simplified), Czech, Finnish, French, Galician, German, Kabyle, Korean, Persian, Portuguese, Portuguese (Brazil), Russian, Spanish
  - Volunteer translations: https://crowdin.com/project/phanpy
- 🔔 V2 Notifications policy API https://mastodon.social/@cheeaun/112960426880369332
- 🔐 Initial OAuth PKCE implementation https://mastodon.social/@cheeaun/113032467744616699
- 🐛 Bug fixes

## October 9, 2024

📢 https://mastodon.social/@cheeaun/113272683452140213

- 💬 5 new languages: Dutch, Esperanto, Galician, Italian, Japanese
- 🐛 Bug fixes

Recap: Mastodon v4.3 features (https://github.com/mastodon/mastodon/releases/tag/v4.3.0) already supported on Phanpy:
- Server-side notification grouping (opt-in)
- Filtered notifications
- Severed relationships notifications
- Timeline of public posts about a trending link
- Author highlight for news articles

## November 13, 2024

📢 https://mastodon.social/@cheeaun/113475224009500738

- 💬 3 new languages: Lithuanian, Norwegian Bokmål, Ukrainian
- 🐛 Bug fixes

## March 12, 2025

📢 https://mastodon.social/@cheeaun/114149642985452634

- 🎏 Change profile header and avatar
- 🗓️ Scheduled posts
- 🗣️ Experimental implementation of Translator & Language Detector APIs (only Chrome 131 - 137) https://webmachinelearning.github.io/translation-api/
  - The code: https://github.com/cheeaun/phanpy/blob/0759e6431f4572a1d0f10b8eeda0b46b9f14bc0b/src/utils/browser-translator.js
- 🧵 'Add to thread' menu when right-clicking/long-pressing compose button
- 📸 Camera button on composer, if supported (Mobile Safari opens camera, Chrome Android requires one extra step)
- 🐛 Bug fixes

## April 26, 2025

📢 https://mastodon.social/@cheeaun/114404023673367245

- 🗣️ Migrated to a new API for translations.
  - Improved support from 133 to 249 languages
- ⌨️ Keyboard-layout-dependent shortcuts
- 🙈 Handle Mastodon's upcoming `blur` filter https://mastodon.social/@cheeaun/114301571477875063
- 🐛 Bug fixes

## June 8, 2025

📢 https://mastodon.social/@cheeaun/114647753143423772

- 🌟 Featured profiles (for upcoming Mastodon v4.4)
- 🔑 Revoke access token when logging out
- 📸 Respect server's media description character limit
- 🪣 Add indicator for exclusive lists
- 🧪 Sandbox https://mastodon.social/@cheeaun/114494995294330803
- 🐛 Bug fixes

## July 18, 2025

📢 https://mastodon.social/@cheeaun/114874317126202331

- 💬 Better display support for Mastodon v4.4's native quote posts
- 🧮 Math formatting for LaTeX
- 🐛 Bug fixes

<!--

## Next

- 🐛 Bug fixes

-->