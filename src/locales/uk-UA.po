msgid ""
msgstr ""
"POT-Creation-Date: 2025-05-03 21:08+0800\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: uk\n"
"Project-Id-Version: phanpy\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-07-15 09:07\n"
"Last-Translator: \n"
"Language-Team: Ukrainian\n"
"Plural-Forms: nplurals=4; plural=((n%10==1 && n%100!=11) ? 0 : ((n%10 >= 2 && n%10 <=4 && (n%100 < 12 || n%100 > 14)) ? 1 : ((n%10 == 0 || (n%10 >= 5 && n%10 <=9)) || (n%100 >= 11 && n%100 <= 14)) ? 2 : 3));\n"
"X-Crowdin-Project: phanpy\n"
"X-Crowdin-Project-ID: 703337\n"
"X-Crowdin-Language: uk\n"
"X-Crowdin-File: /main/src/locales/en.po\n"
"X-Crowdin-File-ID: 18\n"

#: src/components/account-block.jsx:137
msgid "Locked"
msgstr "Закритий профіль"

#. placeholder {0}: shortenNumber(statusesCount)
#: src/components/account-block.jsx:143
msgid "Posts: {0}"
msgstr "Дописи: {0}"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-block.jsx:148
msgid "Last posted: {0}"
msgstr "Останній допис: {0}"

#: src/components/account-block.jsx:163
#: src/components/account-info.jsx:710
msgid "Automated"
msgstr "Автоматизовано"

#: src/components/account-block.jsx:170
#: src/components/account-info.jsx:715
#: src/components/status.jsx:665
msgid "Group"
msgstr "Група"

#: src/components/account-block.jsx:180
msgid "Mutual"
msgstr "Взаємно"

#: src/components/account-block.jsx:184
#: src/components/account-info.jsx:1875
msgid "Requested"
msgstr "Зроблено запит"

#: src/components/account-block.jsx:188
#: src/components/account-info.jsx:1866
msgid "Following"
msgstr "Ви підписані"

#: src/components/account-block.jsx:192
#: src/components/account-info.jsx:1180
msgid "Follows you"
msgstr "Підписаний(-на) на вас"

#: src/components/account-block.jsx:200
msgid "{followersCount, plural, one {# follower} other {# followers}}"
msgstr "{followersCount, plural, one {# підписник} many {# підписників} other {# підписники}}"

#: src/components/account-block.jsx:209
#: src/components/account-info.jsx:758
msgid "Verified"
msgstr "Верифіковано"

#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#: src/components/account-block.jsx:224
#: src/components/account-info.jsx:860
msgid "Joined <0>{0}</0>"
msgstr "Приєднав(-ла)ся <0>{0}</0>"

#: src/components/account-info.jsx:63
msgid "Forever"
msgstr "Назавжди"

#: src/components/account-info.jsx:403
msgid "Unable to load account."
msgstr "Не вдалося завантажити обліковий запис."

#: src/components/account-info.jsx:418
msgid "Go to account page"
msgstr "Перейти на сторінку облікового запису"

#: src/components/account-info.jsx:447
#: src/components/account-info.jsx:780
#: src/components/account-info.jsx:810
msgid "Followers"
msgstr "Підписники"

#. js-lingui-explicit-id
#: src/components/account-info.jsx:451
#: src/components/account-info.jsx:820
#: src/components/account-info.jsx:839
msgid "following.stats"
msgstr "Підписок"

#: src/components/account-info.jsx:454
#: src/components/account-info.jsx:856
#: src/pages/account-statuses.jsx:487
#: src/pages/search.jsx:344
#: src/pages/search.jsx:491
msgid "Posts"
msgstr "Дописів"

#: src/components/account-info.jsx:462
#: src/components/account-info.jsx:1236
#: src/components/compose.jsx:2792
#: src/components/media-alt-modal.jsx:55
#: src/components/media-modal.jsx:363
#: src/components/status.jsx:1915
#: src/components/status.jsx:1932
#: src/components/status.jsx:2057
#: src/components/status.jsx:2686
#: src/components/status.jsx:2689
#: src/pages/account-statuses.jsx:531
#: src/pages/accounts.jsx:118
#: src/pages/hashtag.jsx:203
#: src/pages/list.jsx:171
#: src/pages/public.jsx:116
#: src/pages/scheduled-posts.jsx:89
#: src/pages/status.jsx:1305
#: src/pages/trending.jsx:474
msgid "More"
msgstr "Більше"

#: src/components/account-info.jsx:474
msgid "<0>{displayName}</0> has indicated that their new account is now:"
msgstr "<0>{displayName}</0> вказав(-ла), що новий обліковий запис зараз:"

#: src/components/account-info.jsx:619
#: src/components/account-info.jsx:1464
msgid "Handle copied"
msgstr "Ідентифікатор скопійовано"

#: src/components/account-info.jsx:622
#: src/components/account-info.jsx:1467
msgid "Unable to copy handle"
msgstr "Не вдалося скопіювати ідентифікатор"

#: src/components/account-info.jsx:628
#: src/components/account-info.jsx:1473
msgid "Copy handle"
msgstr "Скопіювати ідентифікатор"

#: src/components/account-info.jsx:634
msgid "Go to original profile page"
msgstr "Перейти на сторінку на сервері"

#: src/components/account-info.jsx:652
msgid "View profile image"
msgstr "Переглянути зображення профілю"

#: src/components/account-info.jsx:670
msgid "View profile header"
msgstr "Переглянути шапку профілю"

#: src/components/account-info.jsx:686
#: src/components/account-info.jsx:1774
#: src/components/account-info.jsx:2300
msgid "Edit profile"
msgstr "Редагувати профіль"

#: src/components/account-info.jsx:705
msgid "In Memoriam"
msgstr "Вічна пам'ять"

#: src/components/account-info.jsx:787
#: src/components/account-info.jsx:830
msgid "This user has chosen to not make this information available."
msgstr "Цей користувач вирішив не розкривати цю інформацію."

#. placeholder {0}: ( postingStats.originals / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {1}: ( postingStats.replies / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {2}: ( postingStats.boosts / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#: src/components/account-info.jsx:885
msgid "{0} original posts, {1} replies, {2} boosts"
msgstr "{0} дописи, {1} відповіді, {2} поширення"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.daysSinceLastPost
#. placeholder {2}: postingStats.daysSinceLastPost
#. placeholder {3}: postingStats.daysSinceLastPost
#. placeholder {4}: postingStats.total
#. placeholder {5}: postingStats.total
#. placeholder {6}: postingStats.daysSinceLastPost
#: src/components/account-info.jsx:901
msgid "{0, plural, one {{1, plural, one {Last 1 post in the past 1 day} other {Last 1 post in the past {2} days}}} other {{3, plural, one {Last {4} posts in the past 1 day} other {Last {5} posts in the past {6} days}}}}"
msgstr "{0, plural, one {{1, plural, one {1 останній допис за 1 останній день} other {1 останній допис за {2} останніх днів}}} other {{3, plural, one {Останні {4} дописів за 1 останній день} other {Останні {5} дописів за {6} останніх днів}}}}"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.total
#: src/components/account-info.jsx:917
msgid "{0, plural, one {Last 1 post in the past year(s)} other {Last {1} posts in the past year(s)}}"
msgstr "{0, plural, one {{1}  допис} few {{1} дописи} many {{1} дописів} other {{1} дописи}} за останній рік чи роки"

#: src/components/account-info.jsx:942
#: src/pages/catchup.jsx:70
msgid "Original"
msgstr "Оригінал"

#: src/components/account-info.jsx:946
#: src/components/status.jsx:2470
#: src/pages/catchup.jsx:71
#: src/pages/catchup.jsx:1448
#: src/pages/catchup.jsx:2061
#: src/pages/status.jsx:1024
#: src/pages/status.jsx:1651
msgid "Replies"
msgstr "Відповіді"

#: src/components/account-info.jsx:950
#: src/pages/catchup.jsx:72
#: src/pages/catchup.jsx:1450
#: src/pages/catchup.jsx:2073
#: src/pages/settings.jsx:1198
msgid "Boosts"
msgstr "Поширення"

#: src/components/account-info.jsx:956
msgid "Post stats unavailable."
msgstr "Статистика дописів недоступна."

#: src/components/account-info.jsx:987
msgid "View post stats"
msgstr "Переглянути статистику дописів"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-info.jsx:1184
msgid "Last post: <0>{0}</0>"
msgstr "Останній допис: <0>{0}</0>"

#: src/components/account-info.jsx:1198
msgid "Muted"
msgstr "Заглушений(-а)"

#: src/components/account-info.jsx:1203
msgid "Blocked"
msgstr "Заблокований(-а)"

#: src/components/account-info.jsx:1212
msgid "Private note"
msgstr "Приватна нотатка"

#: src/components/account-info.jsx:1269
msgid "Mention <0>@{username}</0>"
msgstr "Згадати <0>@{username}</0>"

#: src/components/account-info.jsx:1281
msgid "Translate bio"
msgstr "Перекласти біографію"

#: src/components/account-info.jsx:1292
msgid "Edit private note"
msgstr "Редагувати приватну нотатку"

#: src/components/account-info.jsx:1292
msgid "Add private note"
msgstr "Додати приватну нотатку"

#: src/components/account-info.jsx:1312
msgid "Notifications enabled for @{username}'s posts."
msgstr "Сповіщення для постів @{username} увімкнено."

#: src/components/account-info.jsx:1313
msgid " Notifications disabled for @{username}'s posts."
msgstr " Сповіщення для постів @{username} вимкнено."

#: src/components/account-info.jsx:1325
msgid "Disable notifications"
msgstr "Вимкнути сповіщення"

#: src/components/account-info.jsx:1326
msgid "Enable notifications"
msgstr "Увімкнути сповіщення"

#: src/components/account-info.jsx:1343
msgid "Boosts from @{username} enabled."
msgstr "Поширення від @{username} увімкнено."

#: src/components/account-info.jsx:1344
msgid "Boosts from @{username} disabled."
msgstr "Поширення від @{username} вимкнено."

#: src/components/account-info.jsx:1355
msgid "Disable boosts"
msgstr "Вимкнути поширення"

#: src/components/account-info.jsx:1355
msgid "Enable boosts"
msgstr "Увімкнути поширення"

#: src/components/account-info.jsx:1374
msgid "@{username} is no longer featured on your profile."
msgstr "@{username} більше не рекомендується у вашому профілі."

#: src/components/account-info.jsx:1384
msgid "@{username} is now featured on your profile."
msgstr "Тепер @{username} рекомендується у вашому профілі."

#: src/components/account-info.jsx:1392
msgid "Unable to unfeature @{username} on your profile."
msgstr "Не вдалося скасувати рекомендацію @{username} у вашому профілі."

#: src/components/account-info.jsx:1396
msgid "Unable to feature @{username} on your profile."
msgstr "Не вдалося порекомендувати @{username} у вашому профілі."

#: src/components/account-info.jsx:1405
msgid "Don't feature on profile"
msgstr "Не рекомендувати в профілі"

#: src/components/account-info.jsx:1406
#: src/pages/hashtag.jsx:333
msgid "Feature on profile"
msgstr "Рекомендувати в профілі"

#: src/components/account-info.jsx:1415
msgid "Show featured profiles"
msgstr "Показати рекомендовані профілі"

#: src/components/account-info.jsx:1430
#: src/components/account-info.jsx:1440
#: src/components/account-info.jsx:2046
msgid "Add/Remove from Lists"
msgstr "Додати/видалити зі Списків"

#: src/components/account-info.jsx:1490
#: src/components/status.jsx:1337
msgid "Link copied"
msgstr "Посилання скопійовано"

#: src/components/account-info.jsx:1493
#: src/components/status.jsx:1340
msgid "Unable to copy link"
msgstr "Не вдалося скопіювати посилання"

#: src/components/account-info.jsx:1499
#: src/components/shortcuts-settings.jsx:1059
#: src/components/status.jsx:1346
#: src/components/status.jsx:3464
msgid "Copy"
msgstr "Скопіювати"

#: src/components/account-info.jsx:1514
#: src/components/shortcuts-settings.jsx:1077
#: src/components/status.jsx:1362
msgid "Sharing doesn't seem to work."
msgstr "Здається, поширення не працює."

#: src/components/account-info.jsx:1520
#: src/components/status.jsx:1368
msgid "Share…"
msgstr "Поширити…"

#: src/components/account-info.jsx:1540
msgid "Unmuted @{username}"
msgstr "Розглушено @{username}"

#: src/components/account-info.jsx:1552
msgid "Unmute <0>@{username}</0>"
msgstr "Не глушити <0>@{username}</0>"

#: src/components/account-info.jsx:1568
msgid "Mute <0>@{username}</0>…"
msgstr "Заглушити <0>@{username}</0>"

#. placeholder {0}: typeof MUTE_DURATIONS_LABELS[duration] === 'function' ? MUTE_DURATIONS_LABELS[duration]() : _(MUTE_DURATIONS_LABELS[duration])
#: src/components/account-info.jsx:1600
msgid "Muted @{username} for {0}"
msgstr "Заглушено @{username} на {0}"

#: src/components/account-info.jsx:1612
msgid "Unable to mute @{username}"
msgstr "Не вдалося заглушити @{username}"

#: src/components/account-info.jsx:1633
msgid "Remove <0>@{username}</0> from followers?"
msgstr "Відписати <0>@{username}</0>?"

#: src/components/account-info.jsx:1653
msgid "@{username} removed from followers"
msgstr "@{username} видалено з підписників"

#: src/components/account-info.jsx:1665
msgid "Remove follower…"
msgstr "Відписати…"

#: src/components/account-info.jsx:1676
msgid "Block <0>@{username}</0>?"
msgstr "Заблокувати <0>@{username}</0>?"

#: src/components/account-info.jsx:1700
msgid "Unblocked @{username}"
msgstr "Розблоковано @{username}"

#: src/components/account-info.jsx:1708
msgid "Blocked @{username}"
msgstr "Заблоковано @{username}"

#: src/components/account-info.jsx:1716
msgid "Unable to unblock @{username}"
msgstr "Не вдалося розблокувати @{username}"

#: src/components/account-info.jsx:1718
msgid "Unable to block @{username}"
msgstr "Не вдалося заблокувати @{username}"

#: src/components/account-info.jsx:1728
msgid "Unblock <0>@{username}</0>"
msgstr "Розблокувати <0>@{username}</0>"

#: src/components/account-info.jsx:1737
msgid "Block <0>@{username}</0>…"
msgstr "Заблокувати <0>@{username}</0>…"

#: src/components/account-info.jsx:1754
msgid "Report <0>@{username}</0>…"
msgstr "Поскаржитися на <0>@{username}</0>…"

#: src/components/account-info.jsx:1810
msgid "Withdraw follow request?"
msgstr "Відкликати запит на підписку?"

#. placeholder {0}: info.acct || info.username
#: src/components/account-info.jsx:1811
msgid "Unfollow @{0}?"
msgstr "Відписатися від @{0}?"

#: src/components/account-info.jsx:1869
msgid "Unfollow…"
msgstr "Відписатися…"

#: src/components/account-info.jsx:1878
msgid "Withdraw…"
msgstr "Відкликати…"

#: src/components/account-info.jsx:1885
#: src/components/account-info.jsx:1889
#: src/pages/hashtag.jsx:265
msgid "Follow"
msgstr "Підписатися"

#: src/components/account-info.jsx:1986
#: src/components/account-info.jsx:2041
#: src/components/account-info.jsx:2175
#: src/components/account-info.jsx:2295
#: src/components/account-sheet.jsx:38
#: src/components/compose.jsx:891
#: src/components/compose.jsx:2748
#: src/components/compose.jsx:3228
#: src/components/compose.jsx:3437
#: src/components/compose.jsx:3667
#: src/components/drafts.jsx:59
#: src/components/embed-modal.jsx:13
#: src/components/generic-accounts.jsx:151
#: src/components/keyboard-shortcuts-help.jsx:43
#: src/components/list-add-edit.jsx:37
#: src/components/media-alt-modal.jsx:43
#: src/components/media-modal.jsx:327
#: src/components/notification-service.jsx:157
#: src/components/report-modal.jsx:118
#: src/components/shortcuts-settings.jsx:230
#: src/components/shortcuts-settings.jsx:583
#: src/components/shortcuts-settings.jsx:783
#: src/components/status.jsx:3188
#: src/components/status.jsx:3428
#: src/components/status.jsx:3937
#: src/pages/accounts.jsx:45
#: src/pages/catchup.jsx:1584
#: src/pages/filters.jsx:225
#: src/pages/list.jsx:302
#: src/pages/notifications.jsx:942
#: src/pages/scheduled-posts.jsx:259
#: src/pages/settings.jsx:90
#: src/pages/status.jsx:1392
msgid "Close"
msgstr "Закрити"

#: src/components/account-info.jsx:1991
msgid "Translated Bio"
msgstr "Перекладена біографія"

#: src/components/account-info.jsx:2086
msgid "Unable to remove from list."
msgstr "Не вдалося видалити зі списку."

#: src/components/account-info.jsx:2087
msgid "Unable to add to list."
msgstr "Не вдалося додати до списку."

#: src/components/account-info.jsx:2106
#: src/pages/lists.jsx:131
msgid "Unable to load lists."
msgstr "Не вдалося завантажити список"

#: src/components/account-info.jsx:2110
msgid "No lists."
msgstr "Списки відсутні."

#: src/components/account-info.jsx:2121
#: src/components/list-add-edit.jsx:41
#: src/pages/lists.jsx:62
msgid "New list"
msgstr "Новий список"

#. placeholder {0}: account?.username || account?.acct
#: src/components/account-info.jsx:2180
msgid "Private note about <0>@{0}</0>"
msgstr "Приватна нотатка про <0>@{0}</0>"

#: src/components/account-info.jsx:2210
msgid "Unable to update private note."
msgstr "Не вдалося оновити приватну нотатку."

#: src/components/account-info.jsx:2233
#: src/components/account-info.jsx:2531
msgid "Cancel"
msgstr "Скасувати"

#: src/components/account-info.jsx:2238
msgid "Save & close"
msgstr "Зберегти і закрити"

#: src/components/account-info.jsx:2355
msgid "Unable to update profile."
msgstr "Не вдалося оновити профіль."

#: src/components/account-info.jsx:2362
msgid "Header picture"
msgstr "Зображення шапки"

#: src/components/account-info.jsx:2414
msgid "Profile picture"
msgstr "Зображення профілю"

#: src/components/account-info.jsx:2466
#: src/components/list-add-edit.jsx:106
msgid "Name"
msgstr "Назва"

#: src/components/account-info.jsx:2479
msgid "Bio"
msgstr "Біографія"

#: src/components/account-info.jsx:2492
msgid "Extra fields"
msgstr "Додаткові поля"

#: src/components/account-info.jsx:2498
msgid "Label"
msgstr "Заголовок"

#: src/components/account-info.jsx:2501
msgid "Content"
msgstr "Вміст"

#: src/components/account-info.jsx:2534
#: src/components/list-add-edit.jsx:152
#: src/components/shortcuts-settings.jsx:715
#: src/pages/filters.jsx:570
#: src/pages/notifications.jsx:1008
msgid "Save"
msgstr "Зберегти"

#: src/components/account-info.jsx:2588
msgid "username"
msgstr "ім'я користувача"

#: src/components/account-info.jsx:2592
msgid "server domain name"
msgstr "доменне ім'я сервера"

#. placeholder {0}: info.username
#: src/components/account-info.jsx:2658
msgid "Profiles featured by @{0}"
msgstr "Профілі, рекомендовані @{0}"

#: src/components/account-info.jsx:2684
msgid "No featured profiles."
msgstr "Немає рекомендованих профілів."

#: src/components/background-service.jsx:158
msgid "Cloak mode disabled"
msgstr "Режим маскування вимкнено"

#: src/components/background-service.jsx:158
msgid "Cloak mode enabled"
msgstr "Режим маскування увімкнено"

#: src/components/columns.jsx:27
#: src/components/nav-menu.jsx:181
#: src/components/shortcuts-settings.jsx:139
#: src/components/timeline.jsx:469
#: src/pages/catchup.jsx:883
#: src/pages/filters.jsx:90
#: src/pages/followed-hashtags.jsx:41
#: src/pages/home.jsx:54
#: src/pages/notifications.jsx:587
#: src/pages/scheduled-posts.jsx:74
msgid "Home"
msgstr "Домашня"

#: src/components/compose-button.jsx:150
#: src/compose.jsx:38
msgid "Compose"
msgstr "Написати"

#: src/components/compose-button.jsx:177
#: src/components/nav-menu.jsx:265
#: src/pages/scheduled-posts.jsx:31
#: src/pages/scheduled-posts.jsx:78
msgid "Scheduled Posts"
msgstr "Заплановані пости"

#: src/components/compose-button.jsx:190
msgid "Add to thread"
msgstr ""

#: src/components/compose.jsx:211
msgid "Take photo or video"
msgstr ""

#: src/components/compose.jsx:212
msgid "Add media"
msgstr ""

#: src/components/compose.jsx:213
msgid "Add custom emoji"
msgstr "Додати користувацькі емодзі"

#: src/components/compose.jsx:214
msgid "Add GIF"
msgstr ""

#: src/components/compose.jsx:215
msgid "Add poll"
msgstr "Додати опитування"

#: src/components/compose.jsx:216
msgid "Schedule post"
msgstr ""

#: src/components/compose.jsx:416
msgid "You have unsaved changes. Discard this post?"
msgstr "Є незбережені зміни. Скасувати цей допис?"

#. placeholder {0}: unsupportedFiles.length
#. placeholder {1}: unsupportedFiles[0].name
#. placeholder {2}: lf.format( unsupportedFiles.map((f) => f.name), )
#: src/components/compose.jsx:654
msgid "{0, plural, one {File {1} is not supported.} other {Files {2} are not supported.}}"
msgstr "{0, plural, one {Файл {1} не підтримується.} other {Файли {2} не підтримуються.}}"

#: src/components/compose.jsx:664
#: src/components/compose.jsx:682
#: src/components/compose.jsx:1794
#: src/components/compose.jsx:1919
msgid "{maxMediaAttachments, plural, one {You can only attach up to 1 file.} other {You can only attach up to # files.}}"
msgstr "{maxMediaAttachments, plural, one {Ви можете прикріпити до 1 файлу.} other {Ви можете прикріпити до # файлів.}}"

#: src/components/compose.jsx:872
msgid "Pop out"
msgstr "В окремому вікні"

#: src/components/compose.jsx:879
msgid "Minimize"
msgstr "Згорнути"

#: src/components/compose.jsx:915
msgid "Looks like you closed the parent window."
msgstr "Здається, ви закрили батьківське вікно."

#: src/components/compose.jsx:922
msgid "Looks like you already have a compose field open in the parent window and currently publishing. Please wait for it to be done and try again later."
msgstr "Здається, ви вже публікуєте допис в батьківському вікні. Дочекайтеся публікації та повторіть спробу."

#: src/components/compose.jsx:927
msgid "Looks like you already have a compose field open in the parent window. Popping in this window will discard the changes you made in the parent window. Continue?"
msgstr "Здається, ви вже друкуєте або редагуєте допис в батьківському вікні. Повернення в це вікно скасує зміни, що зроблені в батьківському вікні. Продовжити?"

#: src/components/compose.jsx:970
msgid "Pop in"
msgstr "Повернути в батьківське вікно"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#. placeholder {1}: rtf.format(-replyToStatusMonthsAgo, 'month')
#: src/components/compose.jsx:980
msgid "Replying to @{0}’s post (<0>{1}</0>)"
msgstr "Відповідаємо на допис @{0} (<0>{1}</0>)"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#: src/components/compose.jsx:990
msgid "Replying to @{0}’s post"
msgstr "Відповідаємо на допис @{0}"

#: src/components/compose.jsx:1003
msgid "Editing source post"
msgstr "Редагування вихідного допису"

#: src/components/compose.jsx:1056
msgid "Poll must have at least 2 options"
msgstr "Опитування повинно мати не більше 2 опцій"

#: src/components/compose.jsx:1060
msgid "Some poll choices are empty"
msgstr "Деякі варіанти опитування порожні"

#: src/components/compose.jsx:1073
msgid "Some media have no descriptions. Continue?"
msgstr "Деякі медіа не мають альтернативного тексту. Продовжити?"

#: src/components/compose.jsx:1125
msgid "Attachment #{i} failed"
msgstr "Помилка вкладення #{i}"

#: src/components/compose.jsx:1221
#: src/components/status.jsx:2245
#: src/components/timeline.jsx:1023
msgid "Content warning"
msgstr "Попередження про вміст"

#: src/components/compose.jsx:1237
msgid "Content warning or sensitive media"
msgstr "Попередження про вміст або чутливе медіа"

#: src/components/compose.jsx:1273
#: src/components/status.jsx:99
#: src/pages/settings.jsx:318
msgid "Public"
msgstr "Для всіх"

#: src/components/compose.jsx:1278
#: src/components/nav-menu.jsx:349
#: src/components/shortcuts-settings.jsx:165
#: src/components/status.jsx:100
msgid "Local"
msgstr "Локальна"

#: src/components/compose.jsx:1282
#: src/components/status.jsx:101
#: src/pages/settings.jsx:321
msgid "Unlisted"
msgstr "Поза списком"

#: src/components/compose.jsx:1285
#: src/components/status.jsx:102
#: src/pages/settings.jsx:324
msgid "Followers only"
msgstr "Лише підписники"

#: src/components/compose.jsx:1288
#: src/components/status.jsx:103
#: src/components/status.jsx:2121
msgid "Private mention"
msgstr "Лише згадані"

#: src/components/compose.jsx:1297
msgid "Post your reply"
msgstr "Опублікувати відповідь"

#: src/components/compose.jsx:1299
msgid "Edit your post"
msgstr "Редагувати допис"

#: src/components/compose.jsx:1300
msgid "What are you doing?"
msgstr "Що ви робите?"

#: src/components/compose.jsx:1379
msgid "Mark media as sensitive"
msgstr "Позначити медіа як чутливе"

#: src/components/compose.jsx:1416
msgid "Posting on <0/>"
msgstr ""

#: src/components/compose.jsx:1447
#: src/components/compose.jsx:3286
#: src/components/shortcuts-settings.jsx:715
#: src/pages/list.jsx:388
msgid "Add"
msgstr "Додати"

#: src/components/compose.jsx:1675
msgid "Schedule"
msgstr ""

#: src/components/compose.jsx:1677
#: src/components/keyboard-shortcuts-help.jsx:155
#: src/components/status.jsx:1109
#: src/components/status.jsx:1895
#: src/components/status.jsx:1896
#: src/components/status.jsx:2590
msgid "Reply"
msgstr "Відповісти"

#: src/components/compose.jsx:1679
msgid "Update"
msgstr "Оновити"

#: src/components/compose.jsx:1680
msgctxt "Submit button in composer"
msgid "Post"
msgstr "Опублікувати"

#: src/components/compose.jsx:1806
msgid "Downloading GIF…"
msgstr "Завантаження GIF…"

#: src/components/compose.jsx:1834
msgid "Failed to download GIF"
msgstr "Не вдалося завантажити GIF"

#: src/components/compose.jsx:2049
#: src/components/compose.jsx:2126
#: src/components/nav-menu.jsx:244
msgid "More…"
msgstr "Більше…"

#: src/components/compose.jsx:2562
msgid "Uploaded"
msgstr "Вивантажено"

#: src/components/compose.jsx:2575
msgid "Image description"
msgstr "Опис зображення"

#: src/components/compose.jsx:2576
msgid "Video description"
msgstr "Опис відео"

#: src/components/compose.jsx:2577
msgid "Audio description"
msgstr "Опис аудіо"

#. placeholder {0}: prettyBytes( imageSize, )
#. placeholder {0}: prettyBytes( videoSize, )
#. placeholder {1}: prettyBytes(imageSizeLimit)
#. placeholder {1}: prettyBytes(videoSizeLimit)
#: src/components/compose.jsx:2612
#: src/components/compose.jsx:2632
msgid "File size too large. Uploading might encounter issues. Try reduce the file size from {0} to {1} or lower."
msgstr "Розмір файлу завеликий. Можуть виникнути проблеми з вивантаженням файлу. Спробуйте зменшити його розмір з {0} до {1} або менше."

#. placeholder {0}: i18n.number( width, )
#. placeholder {1}: i18n.number(height)
#. placeholder {2}: i18n.number(newWidth)
#. placeholder {3}: i18n.number( newHeight, )
#: src/components/compose.jsx:2624
#: src/components/compose.jsx:2644
msgid "Dimension too large. Uploading might encounter issues. Try reduce dimension from {0}×{1}px to {2}×{3}px."
msgstr ""

#: src/components/compose.jsx:2652
msgid "Frame rate too high. Uploading might encounter issues."
msgstr "Частота кадрів зависока. Можуть виникнути проблеми із вивантаженням. "

#: src/components/compose.jsx:2712
#: src/components/compose.jsx:2962
#: src/components/shortcuts-settings.jsx:726
#: src/pages/catchup.jsx:1081
#: src/pages/filters.jsx:413
msgid "Remove"
msgstr "Вилучити"

#: src/components/compose.jsx:2729
#: src/compose.jsx:84
msgid "Error"
msgstr "Помилка"

#: src/components/compose.jsx:2754
msgid "Edit image description"
msgstr "Редагувати опис зображення"

#: src/components/compose.jsx:2755
msgid "Edit video description"
msgstr "Редагувати опис відео"

#: src/components/compose.jsx:2756
msgid "Edit audio description"
msgstr "Редагувати опис аудіо"

#: src/components/compose.jsx:2801
#: src/components/compose.jsx:2850
msgid "Generating description. Please wait…"
msgstr "Генерується опис. Будь ласка, зачекайте…"

#. placeholder {0}: e.message
#: src/components/compose.jsx:2821
msgid "Failed to generate description: {0}"
msgstr ""

#: src/components/compose.jsx:2822
msgid "Failed to generate description"
msgstr "Помилка генерації опису"

#: src/components/compose.jsx:2834
#: src/components/compose.jsx:2840
#: src/components/compose.jsx:2886
msgid "Generate description…"
msgstr "Згенерувати опис…"

#. placeholder {0}: e?.message ? `: ${e.message}` : ''
#: src/components/compose.jsx:2873
msgid "Failed to generate description{0}"
msgstr ""

#. placeholder {0}: localeCode2Text(lang)
#: src/components/compose.jsx:2888
msgid "({0}) <0>— experimental</0>"
msgstr "({0}) <0>— експериментально</0>"

#: src/components/compose.jsx:2907
msgid "Done"
msgstr "Готово"

#. placeholder {0}: i + 1
#: src/components/compose.jsx:2943
msgid "Choice {0}"
msgstr ""

#: src/components/compose.jsx:2990
msgid "Multiple choices"
msgstr "Кілька варіантів відповідей"

#: src/components/compose.jsx:2993
msgid "Duration"
msgstr "Тривалість"

#: src/components/compose.jsx:3024
msgid "Remove poll"
msgstr "Прибрати опитування"

#: src/components/compose.jsx:3245
msgid "Search accounts"
msgstr ""

#: src/components/compose.jsx:3299
#: src/components/generic-accounts.jsx:236
msgid "Error loading accounts"
msgstr "Помилка завантаження облікових записів"

#: src/components/compose.jsx:3443
msgid "Custom emojis"
msgstr "Користувацькі емодзі"

#: src/components/compose.jsx:3463
msgid "Search emoji"
msgstr ""

#: src/components/compose.jsx:3494
msgid "Error loading custom emojis"
msgstr "Помилка завантаження емодзі"

#: src/components/compose.jsx:3505
msgid "Recently used"
msgstr ""

#: src/components/compose.jsx:3506
msgid "Others"
msgstr ""

#. placeholder {0}: i18n.number(emojis.length - max)
#: src/components/compose.jsx:3544
msgid "{0} more…"
msgstr "{0} більше…"

#: src/components/compose.jsx:3682
msgid "Search GIFs"
msgstr ""

#: src/components/compose.jsx:3697
msgid "Powered by GIPHY"
msgstr ""

#: src/components/compose.jsx:3705
msgid "Type to search GIFs"
msgstr "Почніть набирати для пошуку GIF-ок"

#: src/components/compose.jsx:3803
#: src/components/media-modal.jsx:469
#: src/components/timeline.jsx:927
msgid "Previous"
msgstr "Попередні"

#: src/components/compose.jsx:3821
#: src/components/media-modal.jsx:488
#: src/components/timeline.jsx:944
msgid "Next"
msgstr "Наступні"

#: src/components/compose.jsx:3838
msgid "Error loading GIFs"
msgstr "Помилка завантаження GIF-ок"

#: src/components/drafts.jsx:64
#: src/pages/settings.jsx:702
msgid "Unsent drafts"
msgstr "Невідправлені чернетки"

#: src/components/drafts.jsx:69
msgid "Looks like you have unsent drafts. Let's continue where you left off."
msgstr "Схоже, у вас є невідправлені чернетки. Ви можете відновити роботу з ними."

#: src/components/drafts.jsx:103
msgid "Delete this draft?"
msgstr "Видалити цю чернетку?"

#: src/components/drafts.jsx:118
msgid "Error deleting draft! Please try again."
msgstr "Помилка видалення чернетки! Будь ласка, спробуйте ще раз."

#: src/components/drafts.jsx:128
#: src/components/list-add-edit.jsx:188
#: src/components/status.jsx:1512
#: src/pages/filters.jsx:603
#: src/pages/scheduled-posts.jsx:369
msgid "Delete…"
msgstr "Видалити…"

#: src/components/drafts.jsx:147
msgid "Error fetching reply-to status!"
msgstr "Не вдалося отримати допис для відповіді"

#: src/components/drafts.jsx:172
msgid "Delete all drafts?"
msgstr "Видалити всі чернетки?"

#: src/components/drafts.jsx:190
msgid "Error deleting drafts! Please try again."
msgstr "Помилка видалення чернеток! Будь ласка, спробуйте ще раз."

#: src/components/drafts.jsx:202
msgid "Delete all…"
msgstr "Видалити всі…"

#: src/components/drafts.jsx:210
msgid "No drafts found."
msgstr "Поки що жодної чернетки"

#: src/components/drafts.jsx:247
#: src/pages/catchup.jsx:1932
msgid "Poll"
msgstr "Опитування"

#: src/components/drafts.jsx:250
#: src/pages/account-statuses.jsx:367
msgid "Media"
msgstr "Медіа"

#: src/components/embed-modal.jsx:18
msgid "Open in new window"
msgstr "Відкрити у новому вікні"

#: src/components/follow-request-buttons.jsx:43
#: src/pages/notifications.jsx:992
msgid "Accept"
msgstr "Погодитись"

#: src/components/follow-request-buttons.jsx:69
msgid "Reject"
msgstr "Відхилити"

#: src/components/follow-request-buttons.jsx:76
#: src/pages/notifications.jsx:1276
msgid "Accepted"
msgstr "Погоджено"

#: src/components/follow-request-buttons.jsx:80
msgid "Rejected"
msgstr "Відхилено"

#: src/components/generic-accounts.jsx:154
#: src/components/notification.jsx:455
#: src/pages/accounts.jsx:50
#: src/pages/search.jsx:334
#: src/pages/search.jsx:367
msgid "Accounts"
msgstr "Облікові записи"

#: src/components/generic-accounts.jsx:214
#: src/components/timeline.jsx:551
#: src/pages/list.jsx:321
#: src/pages/notifications.jsx:922
#: src/pages/search.jsx:561
#: src/pages/status.jsx:1425
msgid "Show more…"
msgstr "Показати більше…"

#: src/components/generic-accounts.jsx:219
#: src/components/timeline.jsx:556
#: src/pages/search.jsx:566
msgid "The end."
msgstr "Кінець."

#: src/components/generic-accounts.jsx:240
msgid "Nothing to show"
msgstr "Нічого показати"

#: src/components/keyboard-shortcuts-help.jsx:47
#: src/components/nav-menu.jsx:368
#: src/pages/catchup.jsx:1622
msgid "Keyboard shortcuts"
msgstr "Комбінації клавіш"

#: src/components/keyboard-shortcuts-help.jsx:55
msgid "Keyboard shortcuts help"
msgstr "Довідка з комбінацій клавіш"

#: src/components/keyboard-shortcuts-help.jsx:59
#: src/pages/catchup.jsx:1647
msgid "Next post"
msgstr "Наступний допис"

#: src/components/keyboard-shortcuts-help.jsx:63
#: src/pages/catchup.jsx:1655
msgid "Previous post"
msgstr "Попередній допис"

#: src/components/keyboard-shortcuts-help.jsx:67
msgid "Skip carousel to next post"
msgstr "Наступний допис (пропустити карусель)"

#: src/components/keyboard-shortcuts-help.jsx:69
msgid "<0>Shift</0> + <1>j</1>"
msgstr "<0>Shift</0> + <1>j</1>"

#: src/components/keyboard-shortcuts-help.jsx:75
msgid "Skip carousel to previous post"
msgstr "Попередній допис (пропустити карусель)"

#: src/components/keyboard-shortcuts-help.jsx:77
msgid "<0>Shift</0> + <1>k</1>"
msgstr "<0>Shift</0> + <1>k</1>"

#: src/components/keyboard-shortcuts-help.jsx:83
msgid "Load new posts"
msgstr "Завантажити нові дописи"

#: src/components/keyboard-shortcuts-help.jsx:87
#: src/pages/catchup.jsx:1679
msgid "Open post details"
msgstr "Відкрити подробиці допису"

#: src/components/keyboard-shortcuts-help.jsx:89
msgid "<0>Enter</0> or <1>o</1>"
msgstr "<0>Enter</0> або <1>o</1>"

#: src/components/keyboard-shortcuts-help.jsx:96
msgid "Expand content warning or<0/>toggle expanded/collapsed thread"
msgstr "Розгорнути попередження про вміст або<0/>перемкнути розгорнуту/згорнуту нитку"

#: src/components/keyboard-shortcuts-help.jsx:105
msgid "Close post or dialogs"
msgstr "Закрити допис або діалоги"

#: src/components/keyboard-shortcuts-help.jsx:107
msgid "<0>Esc</0> or <1>Backspace</1>"
msgstr "<0>Esc</0> або <1>Backspace</1>"

#: src/components/keyboard-shortcuts-help.jsx:113
msgid "Focus column in multi-column mode"
msgstr "Фокус стовпця в режимі декількох стовпців"

#: src/components/keyboard-shortcuts-help.jsx:115
msgid "<0>1</0> to <1>9</1>"
msgstr "<0>1</0> по <1>9</1>"

#: src/components/keyboard-shortcuts-help.jsx:121
msgid "Focus next column in multi-column mode"
msgstr "Перемістити фокус на наступний стовпець в режимі декількох стовпців"

#: src/components/keyboard-shortcuts-help.jsx:125
msgid "Focus previous column in multi-column mode"
msgstr "Перемістити фокус на попередній стовпець в режимі декількох стовпців"

#: src/components/keyboard-shortcuts-help.jsx:129
msgid "Compose new post"
msgstr "Створити новий допис"

#: src/components/keyboard-shortcuts-help.jsx:133
msgid "Compose new post (new window)"
msgstr "Створити новий допис (нове вікно)"

#: src/components/keyboard-shortcuts-help.jsx:136
msgid "<0>Shift</0> + <1>c</1>"
msgstr "<0>Shift</0> + <1>c</1>"

#: src/components/keyboard-shortcuts-help.jsx:142
msgid "Send post"
msgstr "Опублікувати допис"

#: src/components/keyboard-shortcuts-help.jsx:144
msgid "<0>Ctrl</0> + <1>Enter</1> or <2>⌘</2> + <3>Enter</3>"
msgstr "<0>Ctrl</0> + <1>Enter</1> або<2>⌘</2> + <3>Enter</3>"

#: src/components/keyboard-shortcuts-help.jsx:151
#: src/components/nav-menu.jsx:337
#: src/components/search-form.jsx:73
#: src/components/shortcuts-settings.jsx:52
#: src/components/shortcuts-settings.jsx:179
#: src/pages/search.jsx:46
#: src/pages/search.jsx:316
msgid "Search"
msgstr "Пошук"

#: src/components/keyboard-shortcuts-help.jsx:159
msgid "Reply (new window)"
msgstr "Відповісти (нове вікно)"

#: src/components/keyboard-shortcuts-help.jsx:162
msgid "<0>Shift</0> + <1>r</1>"
msgstr "<0>Shift</0> + <1>r</1>"

#: src/components/keyboard-shortcuts-help.jsx:168
msgid "Like (favourite)"
msgstr "Вподобати (обране)"

#: src/components/keyboard-shortcuts-help.jsx:170
msgid "<0>l</0> or <1>f</1>"
msgstr "<0>l</0> або<1>f</1>"

#: src/components/keyboard-shortcuts-help.jsx:176
#: src/components/status.jsx:1117
#: src/components/status.jsx:2617
#: src/components/status.jsx:2640
#: src/components/status.jsx:2641
msgid "Boost"
msgstr "Поширити"

#: src/components/keyboard-shortcuts-help.jsx:178
msgid "<0>Shift</0> + <1>b</1>"
msgstr "<0>Shift</0> + <1>b</1>"

#: src/components/keyboard-shortcuts-help.jsx:184
#: src/components/status.jsx:1180
#: src/components/status.jsx:2665
#: src/components/status.jsx:2666
msgid "Bookmark"
msgstr "Додати в закладки"

#: src/components/keyboard-shortcuts-help.jsx:188
msgid "Toggle Cloak mode"
msgstr "Перемкнути режим маскування"

#: src/components/keyboard-shortcuts-help.jsx:190
msgid "<0>Shift</0> + <1>Alt</1> + <2>k</2>"
msgstr "<0>Shift</0> + <1>Alt</1> + <2>k</2>"

#: src/components/list-add-edit.jsx:41
msgid "Edit list"
msgstr "Редагувати список"

#: src/components/list-add-edit.jsx:97
msgid "Unable to edit list."
msgstr "Не вдалось відредагувати список."

#: src/components/list-add-edit.jsx:98
msgid "Unable to create list."
msgstr "Не вдалося створити список."

#: src/components/list-add-edit.jsx:126
msgid "Show replies to list members"
msgstr "Показувати відповіді учасникам списку"

#: src/components/list-add-edit.jsx:129
msgid "Show replies to people I follow"
msgstr "Показувати відповіді на людей, на яких я підписан(ий/а)"

#: src/components/list-add-edit.jsx:132
msgid "Don't show replies"
msgstr "Не показувати відповіді"

#: src/components/list-add-edit.jsx:146
msgid "Hide posts on this list from Home/Following"
msgstr "Приховати дописи цього списку з Головної/Підписок"

#: src/components/list-add-edit.jsx:152
#: src/pages/filters.jsx:570
msgid "Create"
msgstr "Створити"

#: src/components/list-add-edit.jsx:159
msgid "Delete this list?"
msgstr "Видалити цей список?"

#: src/components/list-add-edit.jsx:178
msgid "Unable to delete list."
msgstr "Не вдалося видалити список."

#: src/components/list-exclusive-badge.jsx:14
#: src/pages/list.jsx:179
#: src/pages/lists.jsx:107
msgid "Posts on this list are hidden from Home/Following"
msgstr ""

#: src/components/media-alt-modal.jsx:48
#: src/components/media.jsx:51
msgid "Media description"
msgstr "Опис медіа"

#: src/components/media-alt-modal.jsx:67
#: src/components/status.jsx:1223
#: src/components/status.jsx:1232
#: src/components/translation-block.jsx:239
msgid "Translate"
msgstr "Перекласти"

#: src/components/media-alt-modal.jsx:78
#: src/components/status.jsx:1251
msgid "Speak"
msgstr "Вимовити"

#: src/components/media-modal.jsx:374
msgid "Open original media in new window"
msgstr "Відкрити оригінальне медіа у новому вікні"

#: src/components/media-modal.jsx:378
msgid "Open original media"
msgstr "Відкрити оригінальне медіа"

#: src/components/media-modal.jsx:394
msgid "Attempting to describe image. Please wait…"
msgstr "Пробуємо описати зображення. Будь ласка, зачекайте…"

#: src/components/media-modal.jsx:409
msgid "Failed to describe image"
msgstr "Не вдалося описати зображення"

#: src/components/media-modal.jsx:419
msgid "Describe image…"
msgstr "Опишіть зображення…"

#: src/components/media-modal.jsx:443
msgid "View post"
msgstr "Переглянути допис"

#: src/components/media-post.jsx:127
msgid "Sensitive media"
msgstr "Чутливе медіа"

#: src/components/media-post.jsx:132
msgid "Filtered: {filterTitleStr}"
msgstr "Відфільтровано: {filterTitleStr}"

#: src/components/media-post.jsx:133
#: src/components/status.jsx:3767
#: src/components/status.jsx:3863
#: src/components/status.jsx:3941
#: src/components/timeline.jsx:1012
#: src/pages/catchup.jsx:75
#: src/pages/catchup.jsx:1880
msgid "Filtered"
msgstr "Відфільтровано"

#: src/components/media.jsx:477
msgid "Open file"
msgstr ""

#: src/components/modals.jsx:75
msgid "Post scheduled"
msgstr ""

#: src/components/modals.jsx:76
msgid "Post published. Check it out."
msgstr "Допис опубліковано. Перевірте це."

#: src/components/modals.jsx:78
msgid "Reply scheduled"
msgstr ""

#: src/components/modals.jsx:79
msgid "Reply posted. Check it out."
msgstr "Відповідь опубліковано. Перевірте це."

#: src/components/modals.jsx:80
msgid "Post updated. Check it out."
msgstr "Допис оновлено. Перевірте це."

#: src/components/nav-menu.jsx:123
msgid "Menu"
msgstr "Меню"

#: src/components/nav-menu.jsx:159
msgid "Reload page now to update?"
msgstr "Перезавантажити сторінку для оновлення?"

#: src/components/nav-menu.jsx:171
msgid "New update available…"
msgstr "Доступне нове оновлення…"

#. js-lingui-explicit-id
#: src/components/nav-menu.jsx:190
#: src/components/shortcuts-settings.jsx:140
#: src/pages/following.jsx:23
#: src/pages/following.jsx:145
msgid "following.title"
msgstr "Підписки"

#: src/components/nav-menu.jsx:197
#: src/pages/catchup.jsx:878
msgid "Catch-up"
msgstr "Ретроспектива"

#: src/components/nav-menu.jsx:204
#: src/components/shortcuts-settings.jsx:58
#: src/components/shortcuts-settings.jsx:146
#: src/pages/home.jsx:240
#: src/pages/mentions.jsx:21
#: src/pages/mentions.jsx:174
#: src/pages/settings.jsx:1190
#: src/pages/trending.jsx:384
msgid "Mentions"
msgstr "Згадки"

#: src/components/nav-menu.jsx:211
#: src/components/shortcuts-settings.jsx:49
#: src/components/shortcuts-settings.jsx:152
#: src/pages/filters.jsx:24
#: src/pages/home.jsx:86
#: src/pages/home.jsx:198
#: src/pages/notifications.jsx:117
#: src/pages/notifications.jsx:591
msgid "Notifications"
msgstr "Сповіщення"

#: src/components/nav-menu.jsx:214
msgid "New"
msgstr "Нове"

#: src/components/nav-menu.jsx:225
msgid "Profile"
msgstr "Профіль"

#: src/components/nav-menu.jsx:233
#: src/components/shortcuts-settings.jsx:54
#: src/components/shortcuts-settings.jsx:195
#: src/pages/bookmarks.jsx:12
#: src/pages/bookmarks.jsx:26
msgid "Bookmarks"
msgstr "Закладки"

#: src/components/nav-menu.jsx:253
#: src/components/shortcuts-settings.jsx:55
#: src/components/shortcuts-settings.jsx:201
#: src/pages/catchup.jsx:1449
#: src/pages/catchup.jsx:2067
#: src/pages/favourites.jsx:12
#: src/pages/favourites.jsx:26
#: src/pages/settings.jsx:1194
msgid "Likes"
msgstr "Вподобане"

#: src/components/nav-menu.jsx:259
#: src/pages/followed-hashtags.jsx:15
#: src/pages/followed-hashtags.jsx:45
msgid "Followed Hashtags"
msgstr "Відстежувані хештеґи"

#: src/components/nav-menu.jsx:273
#: src/pages/account-statuses.jsx:333
#: src/pages/filters.jsx:55
#: src/pages/filters.jsx:94
#: src/pages/hashtag.jsx:344
msgid "Filters"
msgstr "Фільтри"

#: src/components/nav-menu.jsx:281
msgid "Muted users"
msgstr "Заглушені користувачі"

#: src/components/nav-menu.jsx:289
msgid "Muted users…"
msgstr "Заглушені користувачі…"

#: src/components/nav-menu.jsx:296
msgid "Blocked users"
msgstr "Заблоковані користувачі"

#: src/components/nav-menu.jsx:304
msgid "Blocked users…"
msgstr "Заблоковані користувачі…"

#: src/components/nav-menu.jsx:316
msgid "Accounts…"
msgstr "Облікові записи…"

#: src/components/nav-menu.jsx:326
#: src/pages/login.jsx:32
#: src/pages/login.jsx:199
#: src/pages/status.jsx:924
#: src/pages/welcome.jsx:65
msgid "Log in"
msgstr "Увійти"

#: src/components/nav-menu.jsx:343
#: src/components/shortcuts-settings.jsx:57
#: src/components/shortcuts-settings.jsx:172
#: src/pages/trending.jsx:444
msgid "Trending"
msgstr "Популярне"

#: src/components/nav-menu.jsx:355
#: src/components/shortcuts-settings.jsx:165
msgid "Federated"
msgstr "Федеративна"

#: src/components/nav-menu.jsx:378
msgid "Shortcuts / Columns…"
msgstr "Ярлики / Стовпці…"

#: src/components/nav-menu.jsx:388
#: src/components/nav-menu.jsx:402
msgid "Settings…"
msgstr "Налаштування…"

#: src/components/nav-menu.jsx:432
#: src/components/nav-menu.jsx:467
#: src/components/shortcuts-settings.jsx:50
#: src/components/shortcuts-settings.jsx:158
#: src/pages/list.jsx:127
#: src/pages/lists.jsx:18
#: src/pages/lists.jsx:54
msgid "Lists"
msgstr "Списки"

#: src/components/nav-menu.jsx:440
#: src/components/shortcuts.jsx:241
#: src/pages/list.jsx:139
msgid "All Lists"
msgstr "Всі списки"

#: src/components/notification-service.jsx:161
msgid "Notification"
msgstr "Сповіщення"

#: src/components/notification-service.jsx:167
msgid "This notification is from your other account."
msgstr "Це сповіщення з вашого іншого облікового запису."

#: src/components/notification-service.jsx:196
msgid "View all notifications"
msgstr "Переглянути всі сповіщення"

#: src/components/notification.jsx:71
msgid "{account} reacted to your post with {emojiObject}"
msgstr "{account} відреагував {emojiObject} на ваш допис "

#: src/components/notification.jsx:78
msgid "{account} published a post."
msgstr "{account} опублікував допис"

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:86
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted your reply.} other {{account} boosted your post.}}} other {{account} boosted {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted your reply.} other {<2><3>{1}</3> people</2> boosted your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} пошири(в/ла) вашу відповідь.} other {{account} пошири(в/ла) ваш допис.}}} few {{postType, select, reply {{account} пошири(в/ла) {postsCount} ваші відповіді.} other {{account} пошири(в/ла) {postsCount} ваші дописи.}}} other {{postType, select, reply {{account} пошири(в/ла) {postsCount} ваших відповідей.} other {{account} пошири(в/ла) {postsCount} ваших дописів.}}}}} few {{postsCount, plural, =1 {{postType, select, reply {<0><1>{0}</1> користувачі</0> поширили вашу відповідь.} other {<0><1>{0}</1> користувачі</0> поширили ваш допис.}}} few {{postType, select, reply {<0><1>{0}</1> користувачі</0> поширили {postsCount} ваші відповіді.} other {<0><1>{0}</1> користувачі</0> поширили {postsCount} ваші дописи.}}} other {{postType, select, reply {<0><1>{0}</1> користувачі</0> поширили {postsCount} ваших відповідей.} other {<0><1>{0}</1> користувачі</0> поширили {postsCount} ваших дописів.}}}}} other {{postsCount, plural, =1 {{postType, select, reply {<0><1>{0}</1> користувачів</0> поширили вашу відповідь.} other {<0><1>{0}</1> користувачів</0> поширили ваш допис.}}} few {{postType, select, reply {<0><1>{0}</1> користувачів</0> поширили {postsCount} ваші відповіді.} other {<0><1>{0}</1> користувачів</0> поширили {postsCount} ваші дописи.}}} other {{postType, select, reply {<0><1>{0}</1> користувачів</0> поширили {postsCount} ваших відповідей.} other {<0><1>{0}</1> користувачів</0> поширили {postsCount} ваших дописів.}}}}}}"

#. placeholder {0}: shortenNumber(count)
#: src/components/notification.jsx:129
msgid "{count, plural, =1 {{account} followed you.} other {<0><1>{0}</1> people</0> followed you.}}"
msgstr "{count, plural, =1 {{account} підписа(вся/лась) на вас.} few {<0><1>{0}</1> користувачі</0> підписалися на вас.} other {<0><1>{0}</1> користувачів</0> підписалися на вас.}}"

#: src/components/notification.jsx:143
msgid "{account} requested to follow you."
msgstr "{account} подав запит на підписку."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:152
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} liked your reply.} other {{account} liked your post.}}} other {{account} liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> liked your reply.} other {<2><3>{1}</3> people</2> liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} вподоба(в/ла) вашу відповідь.} other {{account} вподоба(в/ла) ваш допис.}}} few {{postType, select, reply {{account} вподоба(в/ла) {postsCount} ваші відповіді.} other {{account} вподоба(в/ла) {postsCount} ваші дописи.}}} other {{postType, select, reply {{account} вподоба(в/ла) {postsCount} ваших відповідей.} other {{account} вподоба(в/ла) {postsCount} ваших дописів.}}}}} few {{postsCount, plural, =1 {{postType, select, reply {<0><1>{0}</1> користувачі</0> вподобали вашу відповідь.} other {<0><1>{0}</1> користувачі</0> вподобали ваш допис.}}} few {{postType, select, reply {<0><1>{0}</1> користувачі</0> вподобали {postsCount} ваші відповіді.} other {<0><1>{0}</1> користувачі</0> вподобали {postsCount} ваші дописи.}}} other {{postType, select, reply {<0><1>{0}</1> користувачі</0> вподобали {postsCount} ваших відповідей.} other {<0><1>{0}</1> користувачі</0> вподобали {postsCount} ваших дописів.}}}}} other {{postsCount, plural, =1 {{postType, select, reply {<0><1>{0}</1> користувачів</0> вподобали вашу відповідь.} other {<0><1>{0}</1> користувачів</0> вподобали ваш допис.}}} few {{postType, select, reply {<0><1>{0}</1> користувачів</0> вподобали {postsCount} ваші відповіді.} other {<0><1>{0}</1> користувачів</0> вподобали {postsCount} ваші дописи.}}} other {{postType, select, reply {<0><1>{0}</1> користувачів</0> вподобали {postsCount} ваших відповідей.} other {<0><1>{0}</1> користувачів</0> вподобали {postsCount} ваших дописів.}}}}}}"

#: src/components/notification.jsx:194
msgid "A poll you have voted in or created has ended."
msgstr "Опитування, в якому ви проголосували або яке створили, закінчилося."

#: src/components/notification.jsx:195
msgid "A poll you have created has ended."
msgstr "Опитування, яке ви створили, завершено."

#: src/components/notification.jsx:196
msgid "A poll you have voted in has ended."
msgstr "Опитування, в якому ви проголосували, закінчилося"

#: src/components/notification.jsx:197
msgid "A post you interacted with has been edited."
msgstr "Допис, з яким ви взаємодіяли, був відредагований."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:205
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted & liked your reply.} other {{account} boosted & liked your post.}}} other {{account} boosted & liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted & liked your reply.} other {<2><3>{1}</3> people</2> boosted & liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} пошири(в/ла) і вподоба(в/ла) вашу відповідь.} other {{account} пошири(в/ла) і вподоба(в/ла) ваш допис.}}} few {{postType, select, reply {{account} пошири(в/ла) і вподоба(в/ла) {postsCount} ваші відповіді.} other {{account} пошири(в/ла) і вподоба(в/ла) {postsCount} ваші дописи.}}} other {{postType, select, reply {{account} пошири(в/ла) і вподоба(в/ла) {postsCount} ваших відповідей.} other {{account} пошири(в/ла) і вподоба(в/ла) {postsCount} ваших дописів.}}}}} few {{postsCount, plural, =1 {{postType, select, reply {<0><1>{0}</1> користувачі</0> поширили і вподобали вашу відповідь.} other {<0><1>{0}</1> користувачі</0> поширили і вподобали ваш допис.}}} few {{postType, select, reply {<0><1>{0}</1> користувачі</0> поширили і вподобали {postsCount} ваші відповіді.} other {<0><1>{0}</1> користувачі</0> поширили і вподобали {postsCount} ваші дописи.}}} other {{postType, select, reply {<0><1>{0}</1> користувачі</0> поширили і вподобали {postsCount} ваших відповідей.} other {<0><1>{0}</1> користувачі</0> поширили і вподобали {postsCount} ваших дописів.}}}}} other {{postsCount, plural, =1 {{postType, select, reply {<0><1>{0}</1> користувачів</0> поширили і вподобали вашу відповідь.} other {<0><1>{0}</1> користувачів</0> поширили і вподобали ваш допис.}}} few {{postType, select, reply {<0><1>{0}</1> користувачів</0> поширили і вподобали {postsCount} ваші відповіді.} other {<0><1>{0}</1> користувачів</0> поширили і вподобали {postsCount} ваші дописи.}}} other {{postType, select, reply {<0><1>{0}</1> користувачів</0> поширили і вподобали {postsCount} ваших відповідей.} other {<0><1>{0}</1> користувачів</0> поширили і вподобали {postsCount} ваших дописів.}}}}}}"

#: src/components/notification.jsx:247
msgid "{account} signed up."
msgstr "{account} зареєструвався(-лась)"

#: src/components/notification.jsx:249
msgid "{account} reported {targetAccount}"
msgstr "{account} поскаржився(-лась) на {targetAccount}"

#: src/components/notification.jsx:254
msgid "Lost connections with <0>{name}</0>."
msgstr "Втрачено з'єднання з <0>{name}</0>."

#: src/components/notification.jsx:260
msgid "Moderation warning"
msgstr "Попередження про модерацію"

#: src/components/notification.jsx:265
msgid "Your {year} #Wrapstodon is here!"
msgstr ""

#: src/components/notification.jsx:271
msgid "An admin from <0>{from}</0> has suspended <1>{targetName}</1>, which means you can no longer receive updates from them or interact with them."
msgstr "Адміністратор з <0>{from}</0> призупинив <1>{targetName}</1>, що означає, що ви більше не зможете отримувати оновлення від них або взаємодіяти з ними."

#: src/components/notification.jsx:277
msgid "An admin from <0>{from}</0> has blocked <1>{targetName}</1>. Affected followers: {followersCount}, followings: {followingCount}."
msgstr "Адміністратор з <0>{from}</0> заблокував <1>{targetName}</1>. Торкнулося підписників: {followersCount}, підписки: {followingCount}."

#: src/components/notification.jsx:283
msgid "You have blocked <0>{targetName}</0>. Removed followers: {followersCount}, followings: {followingCount}."
msgstr "Ви заблокували <0>{targetName}</0>. Видалені підписники: {followersCount}, підписки: {followingCount}."

#: src/components/notification.jsx:291
msgid "Your account has received a moderation warning."
msgstr "Ваш обліковий запис отримав попередження про модерацію."

#: src/components/notification.jsx:292
msgid "Your account has been disabled."
msgstr "Ваш обліковий запис було вимкнено."

#: src/components/notification.jsx:293
msgid "Some of your posts have been marked as sensitive."
msgstr "Деякі з ваших дописів було позначено як чутливі."

#: src/components/notification.jsx:294
msgid "Some of your posts have been deleted."
msgstr "Деякі з ваших дописів було видалено."

#: src/components/notification.jsx:295
msgid "Your posts will be marked as sensitive from now on."
msgstr "Відтепер ваші дописи будуть позначатися як чутливі."

#: src/components/notification.jsx:296
msgid "Your account has been limited."
msgstr "Ваш обліковий запис було обмежено."

#: src/components/notification.jsx:297
msgid "Your account has been suspended."
msgstr "Ваш обліковий запис було призупинено."

#: src/components/notification.jsx:374
msgid "[Unknown notification type: {type}]"
msgstr "[Невідомий тип сповіщення: {type}]"

#: src/components/notification.jsx:451
#: src/components/status.jsx:1194
#: src/components/status.jsx:1204
msgid "Boosted/Liked by…"
msgstr "Поширили/Вподобали…"

#: src/components/notification.jsx:452
msgid "Liked by…"
msgstr "Вподобали…"

#: src/components/notification.jsx:453
msgid "Boosted by…"
msgstr "Поширили…"

#: src/components/notification.jsx:454
msgid "Followed by…"
msgstr "Підписалися…"

#: src/components/notification.jsx:535
#: src/components/notification.jsx:551
msgid "Learn more <0/>"
msgstr "Дізнайтеся більше <0/>"

#: src/components/notification.jsx:560
msgid "View #Wrapstodon"
msgstr ""

#: src/components/notification.jsx:801
#: src/components/status.jsx:403
msgid "Read more →"
msgstr "Читати більше →"

#: src/components/poll.jsx:113
msgid "Voted"
msgstr "Проголосував(-ла)"

#: src/components/poll.jsx:119
msgid "{optionVotesCount, plural, one {# vote} other {# votes}}"
msgstr "{optionVotesCount, plural, one {# голос} few { # голоси} other {# голосів}}"

#: src/components/poll.jsx:139
#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Hide results"
msgstr "Приховати результати"

#: src/components/poll.jsx:188
msgid "Vote"
msgstr "Проголосувати"

#: src/components/poll.jsx:208
#: src/components/poll.jsx:210
#: src/pages/scheduled-posts.jsx:100
#: src/pages/status.jsx:1294
#: src/pages/status.jsx:1317
msgid "Refresh"
msgstr "Оновити"

#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Show results"
msgstr "Показати результати"

#. placeholder {0}: shortenNumber(votesCount)
#. placeholder {1}: shortenNumber(votesCount)
#: src/components/poll.jsx:231
msgid "{votesCount, plural, one {<0>{0}</0> vote} other {<1>{1}</1> votes}}"
msgstr "{votesCount, plural, one {<0>{0}</0> проголосува(в/ла)} other {<1>{1}</1> проголосували}}"

#. placeholder {0}: shortenNumber(votersCount)
#. placeholder {1}: shortenNumber(votersCount)
#: src/components/poll.jsx:248
msgid "{votersCount, plural, one {<0>{0}</0> voter} other {<1>{1}</1> voters}}"
msgstr "{votersCount, plural, one {<0>{0}</0> учасник} few {<1>{1}</1> учасники} other {<1>{1}</1> учасників}} опитування"

#: src/components/poll.jsx:268
msgid "Ended <0/>"
msgstr "Завершено <0/>"

#: src/components/poll.jsx:272
msgid "Ended"
msgstr "Завершено"

#: src/components/poll.jsx:275
msgid "Ending <0/>"
msgstr "Завершення <0/>"

#: src/components/poll.jsx:279
msgid "Ending"
msgstr "Завершення"

#. Relative time in seconds, as short as possible
#. placeholder {0}: seconds < 1 ? 1 : Math.floor(seconds)
#: src/components/relative-time.jsx:61
msgid "{0}s"
msgstr "{0}с"

#. Relative time in minutes, as short as possible
#. placeholder {0}: Math.floor(seconds / minute)
#: src/components/relative-time.jsx:66
msgid "{0}m"
msgstr "{0}хв"

#. Relative time in hours, as short as possible
#. placeholder {0}: Math.floor(seconds / hour)
#: src/components/relative-time.jsx:71
msgid "{0}h"
msgstr "{0}г"

#: src/components/report-modal.jsx:30
msgid "Spam"
msgstr "Спам"

#: src/components/report-modal.jsx:31
msgid "Malicious links, fake engagement, or repetitive replies"
msgstr "Шкідливі посилання, фальшиві взаємодії, або відповіді, що повторюються"

#: src/components/report-modal.jsx:34
msgid "Illegal"
msgstr "Нелегал"

#: src/components/report-modal.jsx:35
msgid "Violates the law of your or the server's country"
msgstr "Порушує закон вашої країни чи країни серверу"

#: src/components/report-modal.jsx:38
msgid "Server rule violation"
msgstr "Порушення правил серверу"

#: src/components/report-modal.jsx:39
msgid "Breaks specific server rules"
msgstr "Порушує певні правила серверу"

#: src/components/report-modal.jsx:40
msgid "Violation"
msgstr "Порушення"

#: src/components/report-modal.jsx:43
msgid "Other"
msgstr "Інше"

#: src/components/report-modal.jsx:44
msgid "Issue doesn't fit other categories"
msgstr "Проблема не відповідає іншим категоріям"

#: src/components/report-modal.jsx:111
msgid "Report Post"
msgstr "Поскаржитися на допис"

#: src/components/report-modal.jsx:111
msgid "Report @{username}"
msgstr "Поскаржитися на @{username}"

#: src/components/report-modal.jsx:147
msgid "Pending review"
msgstr "Очікує перевірки"

#: src/components/report-modal.jsx:189
msgid "Post reported"
msgstr "Скарга на допис відправлена"

#: src/components/report-modal.jsx:189
msgid "Profile reported"
msgstr "Скарга на обліковий запис відправлена"

#: src/components/report-modal.jsx:197
msgid "Unable to report post"
msgstr "Не вдалося поскаржитися на допис"

#: src/components/report-modal.jsx:198
msgid "Unable to report profile"
msgstr "Не вдалося поскаржитися на обліковий запис"

#: src/components/report-modal.jsx:206
msgid "What's the issue with this post?"
msgstr "Яка проблема з цим дописом?"

#: src/components/report-modal.jsx:207
msgid "What's the issue with this profile?"
msgstr "Яка проблема з цим обліковим записом?"

#: src/components/report-modal.jsx:276
msgid "Additional info"
msgstr "Додаткова інформація"

#: src/components/report-modal.jsx:299
msgid "Forward to <0>{domain}</0>"
msgstr "Переслати до <0>{domain}</0>"

#: src/components/report-modal.jsx:309
msgid "Send Report"
msgstr "Надіслати скаргу"

#: src/components/report-modal.jsx:318
msgid "Muted {username}"
msgstr "{username} заглушено"

#: src/components/report-modal.jsx:321
msgid "Unable to mute {username}"
msgstr "Неможливо заглушити {username}"

#: src/components/report-modal.jsx:326
msgid "Send Report <0>+ Mute profile</0>"
msgstr "Надіслати скаргу <0>+ Заглушити користувача</0>"

#: src/components/report-modal.jsx:337
msgid "Blocked {username}"
msgstr "{username} заблоковано"

#: src/components/report-modal.jsx:340
msgid "Unable to block {username}"
msgstr "Не вдалося заблокувати {username}"

#: src/components/report-modal.jsx:345
msgid "Send Report <0>+ Block profile</0>"
msgstr "Надіслати скаргу <0>+ Заблокувати користувача</0>"

#: src/components/search-form.jsx:203
msgid "{query} <0>‒ accounts, hashtags & posts</0>"
msgstr "{query} <0>‒ обліківки, хештеґи та дописи</0>"

#: src/components/search-form.jsx:216
msgid "Posts with <0>{query}</0>"
msgstr "Дописи з <0>{query}</0>"

#. placeholder {0}: query.replace(/^#/, '')
#: src/components/search-form.jsx:228
msgid "Posts tagged with <0>#{0}</0>"
msgstr "Дописи, що помічені <0>#{0}</0>"

#: src/components/search-form.jsx:242
msgid "Look up <0>{query}</0>"
msgstr "Переглянути <0>{query}</0>"

#: src/components/search-form.jsx:253
msgid "Accounts with <0>{query}</0>"
msgstr "Облікові записи з <0>{query}</0>"

#: src/components/shortcuts-settings.jsx:48
msgid "Home / Following"
msgstr "Домашня / Підписки"

#: src/components/shortcuts-settings.jsx:51
msgid "Public (Local / Federated)"
msgstr "Публічна (Локальна / Федеративна)"

#: src/components/shortcuts-settings.jsx:53
msgid "Account"
msgstr "Обліковий запис"

#: src/components/shortcuts-settings.jsx:56
msgid "Hashtag"
msgstr "Хештеґ"

#: src/components/shortcuts-settings.jsx:63
msgid "List ID"
msgstr "ID списку"

#: src/components/shortcuts-settings.jsx:70
msgid "Local only"
msgstr "Лише локальна"

#: src/components/shortcuts-settings.jsx:75
#: src/components/shortcuts-settings.jsx:84
#: src/components/shortcuts-settings.jsx:122
#: src/pages/login.jsx:203
msgid "Instance"
msgstr "Інстанс"

#: src/components/shortcuts-settings.jsx:78
#: src/components/shortcuts-settings.jsx:87
#: src/components/shortcuts-settings.jsx:125
msgid "Optional, e.g. mastodon.social"
msgstr "Необов'язково, напр. \"twiukraine.com\""

#: src/components/shortcuts-settings.jsx:93
msgid "Search term"
msgstr "Пошуковий запит"

#: src/components/shortcuts-settings.jsx:96
msgid "Optional, unless for multi-column mode"
msgstr "Необов'язково для режиму з одним стовпцем"

#: src/components/shortcuts-settings.jsx:113
msgid "e.g. PixelArt (Max 5, space-separated)"
msgstr "напр. PixelArt (Макс 5, розділені пробілом)"

#: src/components/shortcuts-settings.jsx:117
#: src/pages/hashtag.jsx:360
msgid "Media only"
msgstr "Лише медіа"

#: src/components/shortcuts-settings.jsx:235
#: src/components/shortcuts.jsx:218
msgid "Shortcuts"
msgstr "Ярлики"

#: src/components/shortcuts-settings.jsx:243
msgid "beta"
msgstr "бета"

#: src/components/shortcuts-settings.jsx:249
msgid "Specify a list of shortcuts that'll appear as:"
msgstr "Вкажіть список ярликів, які будуть виглядати як:"

#: src/components/shortcuts-settings.jsx:255
msgid "Floating button"
msgstr "Кнопка, що плаває"

#: src/components/shortcuts-settings.jsx:260
msgid "Tab/Menu bar"
msgstr "Панель Вкладки/Меню"

#: src/components/shortcuts-settings.jsx:265
msgid "Multi-column"
msgstr "Декілька стовпців"

#: src/components/shortcuts-settings.jsx:332
msgid "Not available in current view mode"
msgstr "Недоступно в поточному режимі перегляду"

#: src/components/shortcuts-settings.jsx:351
#: src/pages/accounts.jsx:188
msgid "Move up"
msgstr "Перемістити вгору"

#: src/components/shortcuts-settings.jsx:367
#: src/pages/accounts.jsx:203
msgid "Move down"
msgstr "Пересунути вниз"

#: src/components/shortcuts-settings.jsx:379
#: src/components/status.jsx:1474
#: src/pages/list.jsx:195
msgid "Edit"
msgstr "Редагувати"

#: src/components/shortcuts-settings.jsx:400
msgid "Add more than one shortcut/column to make this work."
msgstr "Додайте кілька ярликів/стовпців, щоб це працювало."

#: src/components/shortcuts-settings.jsx:411
msgid "No columns yet. Tap on the Add column button."
msgstr "Ще немає стовпців. Натисніть кнопку Додати стовпчик."

#: src/components/shortcuts-settings.jsx:412
msgid "No shortcuts yet. Tap on the Add shortcut button."
msgstr "Ще немає ярликів. Натисніть кнопку Додати ярлик."

#: src/components/shortcuts-settings.jsx:415
msgid "Not sure what to add?<0/>Try adding <1>Home / Following and Notifications</1> first."
msgstr "Не знаєте що додати?<0/> Радимо почати з <1>Додому / Підписки</1> та <1>Сповіщення</1>."

#: src/components/shortcuts-settings.jsx:443
msgid "Max {SHORTCUTS_LIMIT} columns"
msgstr "Максимум {SHORTCUTS_LIMIT} стовпців"

#: src/components/shortcuts-settings.jsx:444
msgid "Max {SHORTCUTS_LIMIT} shortcuts"
msgstr "Максимум {SHORTCUTS_LIMIT} ярликів"

#: src/components/shortcuts-settings.jsx:458
msgid "Import/export"
msgstr "Імпорт/Експорт"

#: src/components/shortcuts-settings.jsx:468
msgid "Add column…"
msgstr "Додати стовпець…"

#: src/components/shortcuts-settings.jsx:469
msgid "Add shortcut…"
msgstr "Додати ярлик…"

#: src/components/shortcuts-settings.jsx:516
msgid "Specific list is optional. For multi-column mode, list is required, else the column will not be shown."
msgstr "Вказаний список не є обов'язковим. Для режиму з кількома стовпцями список необхідний, інакше стовпець не буде показано."

#: src/components/shortcuts-settings.jsx:517
msgid "For multi-column mode, search term is required, else the column will not be shown."
msgstr "Для режиму з кількома стовпцями пошуковий запит необхідний, інакше стовпець не буде показано."

#: src/components/shortcuts-settings.jsx:518
msgid "Multiple hashtags are supported. Space-separated."
msgstr "Підтримуються декілька гештеґів. Розділені пробілами."

#: src/components/shortcuts-settings.jsx:587
msgid "Edit shortcut"
msgstr "Редагувати ярлик"

#: src/components/shortcuts-settings.jsx:587
msgid "Add shortcut"
msgstr "Додати ярлик"

#: src/components/shortcuts-settings.jsx:623
msgid "Timeline"
msgstr "Стрічка"

#: src/components/shortcuts-settings.jsx:649
msgid "List"
msgstr "Список"

#: src/components/shortcuts-settings.jsx:788
msgid "Import/Export <0>Shortcuts</0>"
msgstr "Імпорт/Експорт <0>Ярликів</0>"

#: src/components/shortcuts-settings.jsx:798
msgid "Import"
msgstr "Імпорт"

#: src/components/shortcuts-settings.jsx:806
msgid "Paste shortcuts here"
msgstr "Вставити ярлики сюди"

#: src/components/shortcuts-settings.jsx:822
msgid "Downloading saved shortcuts from instance server…"
msgstr "Завантаження збережених ярликів з сервера інстансу…"

#: src/components/shortcuts-settings.jsx:851
msgid "Unable to download shortcuts"
msgstr "Не вдалося завантажити ярлики"

#: src/components/shortcuts-settings.jsx:854
msgid "Download shortcuts from instance server"
msgstr "Завантажити ярлики з сервера інстансу"

#: src/components/shortcuts-settings.jsx:912
msgid "* Exists in current shortcuts"
msgstr "* Існує в поточних ярликах"

#: src/components/shortcuts-settings.jsx:917
msgid "List may not work if it's from a different account."
msgstr "Список може не працювати, якщо він з іншого облікового запису."

#: src/components/shortcuts-settings.jsx:927
msgid "Invalid settings format"
msgstr "Неприпустимий формат налаштувань"

#: src/components/shortcuts-settings.jsx:935
msgid "Append to current shortcuts?"
msgstr "Додати до поточних ярликів?"

#: src/components/shortcuts-settings.jsx:938
msgid "Only shortcuts that don’t exist in current shortcuts will be appended."
msgstr "Будуть додані лише ті ярлики, яких немає серед поточних ярликів."

#: src/components/shortcuts-settings.jsx:960
msgid "No new shortcuts to import"
msgstr "Немає нових ярликів для імпорту"

#: src/components/shortcuts-settings.jsx:975
msgid "Shortcuts imported. Exceeded max {SHORTCUTS_LIMIT}, so the rest are not imported."
msgstr "Ярлики імпортовано. Перевищено ліміт {SHORTCUTS_LIMIT}, тому інші не імпортуються."

#: src/components/shortcuts-settings.jsx:976
#: src/components/shortcuts-settings.jsx:1000
msgid "Shortcuts imported"
msgstr "Ярлики імпортовано"

#: src/components/shortcuts-settings.jsx:986
msgid "Import & append…"
msgstr "Імпортувати й додати…"

#: src/components/shortcuts-settings.jsx:994
msgid "Override current shortcuts?"
msgstr "Замінити поточні ярлики?"

#: src/components/shortcuts-settings.jsx:995
msgid "Import shortcuts?"
msgstr "Імпортувати ярлики?"

#: src/components/shortcuts-settings.jsx:1009
msgid "or override…"
msgstr "або замінити…"

#: src/components/shortcuts-settings.jsx:1009
msgid "Import…"
msgstr "Імпортувати…"

#: src/components/shortcuts-settings.jsx:1018
msgid "Export"
msgstr "Експорт"

#: src/components/shortcuts-settings.jsx:1033
msgid "Shortcuts copied"
msgstr "Ярлики скопійовано"

#: src/components/shortcuts-settings.jsx:1036
msgid "Unable to copy shortcuts"
msgstr "Не вдалося скопіювати ярлики"

#: src/components/shortcuts-settings.jsx:1050
msgid "Shortcut settings copied"
msgstr "Налаштування ярлика скопійовано"

#: src/components/shortcuts-settings.jsx:1053
msgid "Unable to copy shortcut settings"
msgstr "Не вдалося скопіювати налаштування ярлику"

#: src/components/shortcuts-settings.jsx:1083
msgid "Share"
msgstr "Поширити"

#: src/components/shortcuts-settings.jsx:1122
msgid "Saving shortcuts to instance server…"
msgstr "Збереження ярликів до сервера інстансу…"

#: src/components/shortcuts-settings.jsx:1129
msgid "Shortcuts saved"
msgstr "Ярлики збережено"

#: src/components/shortcuts-settings.jsx:1134
msgid "Unable to save shortcuts"
msgstr "Не вдалося зберегти ярлики"

#: src/components/shortcuts-settings.jsx:1137
msgid "Sync to instance server"
msgstr "Синхронізувати з сервером інстансу"

#. placeholder {0}: shortcutsStr.length
#: src/components/shortcuts-settings.jsx:1145
msgid "{0, plural, one {# character} other {# characters}}"
msgstr "{0, plural, one {# символ} few {# символи} other {# символів}}"

#: src/components/shortcuts-settings.jsx:1157
msgid "Raw Shortcuts JSON"
msgstr "Вихідний JSON Ярликів"

#: src/components/shortcuts-settings.jsx:1170
msgid "Import/export settings from/to instance server (Very experimental)"
msgstr "Імпорт/експорт налаштувань з/на сервер інстансу (Дуже експериментально)"

#: src/components/status.jsx:277
msgid "Unable to format math"
msgstr ""

#: src/components/status.jsx:292
msgid "Math expressions found."
msgstr ""

#: src/components/status.jsx:294
msgid "Show markup"
msgstr ""

#: src/components/status.jsx:294
msgid "Format math"
msgstr ""

#: src/components/status.jsx:689
msgid "<0/> <1>boosted</1>"
msgstr "<0/> <1>пошири(в/ла)</1>"

#: src/components/status.jsx:792
msgid "Sorry, your current logged-in instance can't interact with this post from another instance."
msgstr "На жаль, ваш поточний інстанс не може взаємодіяти з цим дописом з іншого інстансу."

#. placeholder {0}: username || acct
#: src/components/status.jsx:946
msgid "Unliked @{0}'s post"
msgstr "Не вподобано допис @{0}"

#. placeholder {0}: username || acct
#: src/components/status.jsx:947
msgid "Liked @{0}'s post"
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:986
msgid "Unbookmarked @{0}'s post"
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:987
msgid "Bookmarked @{0}'s post"
msgstr ""

#: src/components/status.jsx:1086
msgid "Some media have no descriptions."
msgstr "Деякі медіа не мають альтернативного тексту."

#. placeholder {0}: rtf.format(-statusMonthsAgo, 'month')
#: src/components/status.jsx:1093
msgid "Old post (<0>{0}</0>)"
msgstr "Старий допис (<0>{0}</0>)"

#: src/components/status.jsx:1117
#: src/components/status.jsx:1157
#: src/components/status.jsx:2617
#: src/components/status.jsx:2640
msgid "Unboost"
msgstr "Не поширювати"

#: src/components/status.jsx:1133
#: src/components/status.jsx:2632
msgid "Quote"
msgstr "Цитувати"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1145
#: src/components/status.jsx:1611
msgid "Unboosted @{0}'s post"
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:1146
#: src/components/status.jsx:1612
msgid "Boosted @{0}'s post"
msgstr ""

#: src/components/status.jsx:1158
msgid "Boost…"
msgstr "Поширити…"

#: src/components/status.jsx:1170
#: src/components/status.jsx:1905
#: src/components/status.jsx:2653
msgid "Unlike"
msgstr "Не вподобати"

#: src/components/status.jsx:1171
#: src/components/status.jsx:1905
#: src/components/status.jsx:1906
#: src/components/status.jsx:2653
#: src/components/status.jsx:2654
msgid "Like"
msgstr "Вподобати"

#: src/components/status.jsx:1180
#: src/components/status.jsx:2665
msgid "Unbookmark"
msgstr "Вилучити з закладок"

#: src/components/status.jsx:1263
msgid "Post text copied"
msgstr ""

#: src/components/status.jsx:1266
msgid "Unable to copy post text"
msgstr ""

#: src/components/status.jsx:1272
msgid "Copy post text"
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:1290
msgid "View post by <0>@{0}</0>"
msgstr "Переглянути допис від <0>@{0}</0>"

#: src/components/status.jsx:1311
msgid "Show Edit History"
msgstr "Показати Історію Змін"

#: src/components/status.jsx:1314
msgid "Edited: {editedDateText}"
msgstr "Відредаговано: {editedDateText}"

#: src/components/status.jsx:1381
#: src/components/status.jsx:3433
msgid "Embed post"
msgstr "Вбудувати допис"

#: src/components/status.jsx:1395
msgid "Conversation unmuted"
msgstr "Бесіда більше не глушиться"

#: src/components/status.jsx:1395
msgid "Conversation muted"
msgstr "Бесіда заглушена"

#: src/components/status.jsx:1401
msgid "Unable to unmute conversation"
msgstr "Не вдалося розглушити бесіду"

#: src/components/status.jsx:1402
msgid "Unable to mute conversation"
msgstr "Не вдалося заглушити бесіду"

#: src/components/status.jsx:1411
msgid "Unmute conversation"
msgstr "Розглушити бесіду"

#: src/components/status.jsx:1418
msgid "Mute conversation"
msgstr "Заглушити бесіду"

#: src/components/status.jsx:1434
msgid "Post unpinned from profile"
msgstr "Допис відкріплено від профілю"

#: src/components/status.jsx:1435
msgid "Post pinned to profile"
msgstr "Допис закріплено в профілі"

#: src/components/status.jsx:1440
msgid "Unable to unpin post"
msgstr "Не вдалося відкріпити допис"

#: src/components/status.jsx:1440
msgid "Unable to pin post"
msgstr "Не вдалося закріпити допис"

#: src/components/status.jsx:1449
msgid "Unpin from profile"
msgstr "Відкріпити від профілю"

#: src/components/status.jsx:1456
msgid "Pin to profile"
msgstr "Закріпити в профілі"

#: src/components/status.jsx:1485
msgid "Delete this post?"
msgstr "Видалити цей допис?"

#: src/components/status.jsx:1501
msgid "Post deleted"
msgstr "Допис видалено"

#: src/components/status.jsx:1504
msgid "Unable to delete post"
msgstr "Не вдалося видалити допис"

#: src/components/status.jsx:1532
msgid "Report post…"
msgstr "Поскаржитися на допис…"

#: src/components/status.jsx:1906
#: src/components/status.jsx:1942
#: src/components/status.jsx:2654
msgid "Liked"
msgstr "Вподобано"

#: src/components/status.jsx:1939
#: src/components/status.jsx:2641
msgid "Boosted"
msgstr "Поширено"

#: src/components/status.jsx:1949
#: src/components/status.jsx:2666
msgid "Bookmarked"
msgstr "Додано в закладки"

#: src/components/status.jsx:1953
msgid "Pinned"
msgstr "Закріплено"

#: src/components/status.jsx:1999
#: src/components/status.jsx:2478
msgid "Deleted"
msgstr "Видалено"

#: src/components/status.jsx:2040
msgid "{repliesCount, plural, one {# reply} other {# replies}}"
msgstr "{repliesCount, plural, one {# відповідь} few {# відповіді} other {# відповідей}}"

#. placeholder {0}: snapStates.statusThreadNumber[sKey] ? ` ${snapStates.statusThreadNumber[sKey]}/X` : ''
#: src/components/status.jsx:2130
msgid "Thread{0}"
msgstr "Нитка{0}"

#: src/components/status.jsx:2208
#: src/components/status.jsx:2270
#: src/components/status.jsx:2374
msgid "Show less"
msgstr "Згорнути"

#: src/components/status.jsx:2208
#: src/components/status.jsx:2270
msgid "Show content"
msgstr "Показати вміст"

#. placeholder {0}: filterInfo.titlesStr
#. placeholder {0}: filterInfo?.titlesStr
#: src/components/status.jsx:2370
#: src/pages/catchup.jsx:1879
msgid "Filtered: {0}"
msgstr "Відфільтровано: {0}"

#: src/components/status.jsx:2374
msgid "Show media"
msgstr "Показати медіа"

#: src/components/status.jsx:2514
msgid "Edited"
msgstr "Відредаговано"

#: src/components/status.jsx:2591
msgid "Comments"
msgstr "Коментарі"

#. More from [Author]
#: src/components/status.jsx:2891
msgid "More from <0/>"
msgstr "Більше від <0/>"

#: src/components/status.jsx:3193
msgid "Edit History"
msgstr "Історія Змін"

#: src/components/status.jsx:3197
msgid "Failed to load history"
msgstr "Не вдалося завантажити історію"

#: src/components/status.jsx:3202
#: src/pages/annual-report.jsx:45
msgid "Loading…"
msgstr "Завантаження…"

#: src/components/status.jsx:3438
msgid "HTML Code"
msgstr "HTML-код"

#: src/components/status.jsx:3455
msgid "HTML code copied"
msgstr "HTML-код скопійовано"

#: src/components/status.jsx:3458
msgid "Unable to copy HTML code"
msgstr "Не вдалося скопіювати HTML-код"

#: src/components/status.jsx:3470
msgid "Media attachments:"
msgstr "Медіа вкладення:"

#: src/components/status.jsx:3492
msgid "Account Emojis:"
msgstr "Емодзі обліківки:"

#: src/components/status.jsx:3523
#: src/components/status.jsx:3568
msgid "static URL"
msgstr "статичний URL"

#: src/components/status.jsx:3537
msgid "Emojis:"
msgstr "Емодзі:"

#: src/components/status.jsx:3582
msgid "Notes:"
msgstr "Нотатки:"

#: src/components/status.jsx:3586
msgid "This is static, unstyled and scriptless. You may need to apply your own styles and edit as needed."
msgstr "Це статичне, не стилізоване та без сценаріїв. Можливо, варто застосувати власні стилі та відредагувати як треба."

#: src/components/status.jsx:3592
msgid "Polls are not interactive, becomes a list with vote counts."
msgstr "Опитування не інтерактивні, вони являють собою список з підрахованими голосами."

#: src/components/status.jsx:3597
msgid "Media attachments can be images, videos, audios or any file types."
msgstr "Вкладені медіа можуть бути зображеннями, відео, аудіо чи будь-якими іншими типами файлів."

#: src/components/status.jsx:3603
msgid "Post could be edited or deleted later."
msgstr "Допис можна відредагувати або видалити пізніше."

#: src/components/status.jsx:3609
msgid "Preview"
msgstr "Попередній перегляд"

#: src/components/status.jsx:3618
msgid "Note: This preview is lightly styled."
msgstr "Примітка: Цей попередній перегляд трохи стилізовано."

#. [Name] [Visibility icon] boosted
#: src/components/status.jsx:3871
msgid "<0/> <1/> boosted"
msgstr "<0/> <1/> поширили"

#: src/components/status.jsx:3973
msgid "Post hidden by your filters"
msgstr ""

#: src/components/status.jsx:3974
msgid "Post removed by author."
msgstr ""

#: src/components/status.jsx:3975
msgid "You’re not authorized to view this post."
msgstr ""

#: src/components/status.jsx:3976
msgid "Post pending author approval."
msgstr ""

#: src/components/status.jsx:3977
#: src/components/status.jsx:3978
msgid "Quoting not allowed by the author."
msgstr ""

#: src/components/timeline.jsx:485
#: src/pages/settings.jsx:1218
msgid "New posts"
msgstr "Нові дописи"

#: src/components/timeline.jsx:586
#: src/pages/home.jsx:228
#: src/pages/notifications.jsx:898
#: src/pages/status.jsx:1077
#: src/pages/status.jsx:1454
msgid "Try again"
msgstr "Спробувати знову"

#. placeholder {0}: fItems.length
#: src/components/timeline.jsx:621
msgid "{0, plural, one {# Boost} other {# Boosts}}"
msgstr "{0, plural, one {# Поширення} few {# Поширення} other{# Поширень}}"

#: src/components/timeline.jsx:626
msgid "Pinned posts"
msgstr "Закріплені дописи"

#: src/components/timeline.jsx:985
#: src/components/timeline.jsx:992
#: src/pages/catchup.jsx:1897
msgid "Thread"
msgstr "Нитка"

#. placeholder {0}: filterInfo.titlesStr
#: src/components/timeline.jsx:1007
msgid "<0>Filtered</0>: <1>{0}</1>"
msgstr "<0>Відфільтровано</0>: <1>{0}</1>"

#: src/components/translation-block.jsx:196
msgid "Auto-translated from {sourceLangText}"
msgstr "Автопереклад з {sourceLangText}"

#: src/components/translation-block.jsx:234
msgid "Translating…"
msgstr "Перекладаємо…"

#: src/components/translation-block.jsx:237
msgid "Translate from {sourceLangText} (auto-detected)"
msgstr "Перекласти з {sourceLangText} (автовизначення)"

#: src/components/translation-block.jsx:238
msgid "Translate from {sourceLangText}"
msgstr "Перекласти з {sourceLangText}"

#. placeholder {0}: detectedLang ?? '…'
#: src/components/translation-block.jsx:266
msgid "Auto ({0})"
msgstr "Авто ({0})"

#: src/components/translation-block.jsx:279
msgid "Failed to translate"
msgstr "Не вдалося перекласти"

#: src/compose.jsx:33
msgid "Editing source status"
msgstr "Редагування вихідного статусу"

#. placeholder {0}: replyToStatus.account?.acct || replyToStatus.account?.username
#: src/compose.jsx:35
msgid "Replying to @{0}"
msgstr "Відповісти @{0}"

#: src/compose.jsx:63
msgid "You may close this page now."
msgstr "Тепер ви можете закрити сторінку."

#: src/compose.jsx:71
msgid "Close window"
msgstr "Закрити вікно"

#: src/compose.jsx:87
msgid "Login required."
msgstr "Необхідно ввійти."

#: src/compose.jsx:91
#: src/pages/annual-report.jsx:164
#: src/pages/http-route.jsx:91
#: src/pages/login.jsx:280
msgid "Go home"
msgstr "На головну"

#: src/pages/account-statuses.jsx:238
msgid "Account posts"
msgstr "Дописи облікового запису"

#: src/pages/account-statuses.jsx:245
msgid "{accountDisplay} (+ Replies)"
msgstr "{accountDisplay} (+ Відповіді)"

#: src/pages/account-statuses.jsx:247
msgid "{accountDisplay} (- Boosts)"
msgstr "{accountDisplay} (- Поширення)"

#: src/pages/account-statuses.jsx:249
msgid "{accountDisplay} (#{tagged})"
msgstr "{accountDisplay} (#{tagged})"

#: src/pages/account-statuses.jsx:251
msgid "{accountDisplay} (Media)"
msgstr "{accountDisplay} (Медіа)"

#: src/pages/account-statuses.jsx:257
msgid "{accountDisplay} ({monthYear})"
msgstr "{accountDisplay} ({monthYear})"

#: src/pages/account-statuses.jsx:323
msgid "Clear filters"
msgstr "Скинути фільтри"

#: src/pages/account-statuses.jsx:326
msgid "Clear"
msgstr "Очистити"

#: src/pages/account-statuses.jsx:340
msgid "Showing post with replies"
msgstr "Показ дописів з відповідями"

#: src/pages/account-statuses.jsx:345
msgid "+ Replies"
msgstr "+ Відповіді"

#: src/pages/account-statuses.jsx:351
msgid "Showing posts without boosts"
msgstr "Показ дописів без поширень"

#: src/pages/account-statuses.jsx:356
msgid "- Boosts"
msgstr "- Поширення"

#: src/pages/account-statuses.jsx:362
msgid "Showing posts with media"
msgstr "Показ дописів з медіа"

#. placeholder {0}: tag.name
#: src/pages/account-statuses.jsx:379
msgid "Showing posts tagged with #{0}"
msgstr "Показ дописів з теґом #{0}"

#. placeholder {0}: date.toLocaleString(i18n.locale, { month: 'long', year: 'numeric', })
#: src/pages/account-statuses.jsx:418
msgid "Showing posts in {0}"
msgstr ""

#: src/pages/account-statuses.jsx:508
msgid "Nothing to see here yet."
msgstr "Поки що тут нічого нема."

#: src/pages/account-statuses.jsx:509
#: src/pages/public.jsx:99
#: src/pages/trending.jsx:452
msgid "Unable to load posts"
msgstr "Не вдалося завантажити дописи"

#: src/pages/account-statuses.jsx:550
#: src/pages/account-statuses.jsx:580
msgid "Unable to fetch account info"
msgstr "Не вдалося отримати інформацію про обліковий запис"

#. placeholder {0}: accountInstance ? ( <> {' '} (<b>{punycode.toUnicode(accountInstance)}</b>) </> ) : null
#: src/pages/account-statuses.jsx:557
msgid "Switch to account's instance {0}"
msgstr "Перемкнутися на інстанс {0} обліківки"

#: src/pages/account-statuses.jsx:587
msgid "Switch to my instance (<0>{currentInstance}</0>)"
msgstr "Перемкнутися на мій інстанс (<0>{currentInstance}</0>)"

#: src/pages/account-statuses.jsx:660
msgid "Month"
msgstr "Місяць"

#: src/pages/accounts.jsx:64
msgid "Current"
msgstr "Поточний"

#: src/pages/accounts.jsx:110
msgid "Default"
msgstr "За замовчуванням"

#: src/pages/accounts.jsx:132
msgid "Switch to this account"
msgstr "Перемкнутися на цю обліківку"

#: src/pages/accounts.jsx:141
msgid "Switch in new tab/window"
msgstr "Перемкнутися в новій вкладці/вікні"

#: src/pages/accounts.jsx:155
msgid "View profile…"
msgstr "Переглянути профіль…"

#: src/pages/accounts.jsx:173
msgid "Set as default"
msgstr "Встановити за замовчуванням"

#. placeholder {0}: account.info.acct
#: src/pages/accounts.jsx:215
msgid "Log out <0>@{0}</0>?"
msgstr "Вийти з <0>@{0}</0>?"

#: src/pages/accounts.jsx:244
msgid "Log out…"
msgstr "Вийти з…"

#. placeholder {0}: niceDateTime(account.createdAt)
#: src/pages/accounts.jsx:251
msgid "Connected on {0} (<0/>)"
msgstr ""

#: src/pages/accounts.jsx:268
msgid "Add an existing account"
msgstr "Додати існуючий обліковий запис"

#: src/pages/accounts.jsx:275
msgid "Note: <0>Default</0> account will always be used for first load. Switched accounts will persist during the session."
msgstr "Примітка: При першому завантаженні буде використано обліковий запис <0>за замовчуванням</0>. Перемикання між обліківками буде зберігатися протягом сесії."

#: src/pages/bookmarks.jsx:28
msgid "No bookmarks yet. Go bookmark something!"
msgstr "Жодної закладки. Додаймо якусь!"

#: src/pages/bookmarks.jsx:29
msgid "Unable to load bookmarks."
msgstr "Не вдалося завантажити закладки."

#: src/pages/catchup.jsx:54
msgid "last 1 hour"
msgstr "остання година"

#: src/pages/catchup.jsx:55
msgid "last 2 hours"
msgstr "останні 2 години"

#: src/pages/catchup.jsx:56
msgid "last 3 hours"
msgstr "останні 3 години"

#: src/pages/catchup.jsx:57
msgid "last 4 hours"
msgstr "останні 4 години"

#: src/pages/catchup.jsx:58
msgid "last 5 hours"
msgstr "останні 5 годин"

#: src/pages/catchup.jsx:59
msgid "last 6 hours"
msgstr "останні 6 годин"

#: src/pages/catchup.jsx:60
msgid "last 7 hours"
msgstr "останні 7 годин"

#: src/pages/catchup.jsx:61
msgid "last 8 hours"
msgstr "останні 8 годин"

#: src/pages/catchup.jsx:62
msgid "last 9 hours"
msgstr "останні 9 годин"

#: src/pages/catchup.jsx:63
msgid "last 10 hours"
msgstr "останні 10 годин"

#: src/pages/catchup.jsx:64
msgid "last 11 hours"
msgstr "останні 11 годин"

#: src/pages/catchup.jsx:65
msgid "last 12 hours"
msgstr "останні 12 годин"

#: src/pages/catchup.jsx:66
msgid "beyond 12 hours"
msgstr "більше 12 годин"

#: src/pages/catchup.jsx:73
msgid "Followed tags"
msgstr "Відстежувані теґи"

#: src/pages/catchup.jsx:74
msgid "Groups"
msgstr "Групи"

#: src/pages/catchup.jsx:597
msgid "Showing {selectedFilterCategory, select, all {all posts} original {original posts} replies {replies} boosts {boosts} followedTags {followed tags} groups {groups} filtered {filtered posts}}, {sortBy, select, createdAt {{sortOrder, select, asc {oldest} desc {latest}}} reblogsCount {{sortOrder, select, asc {fewest boosts} desc {most boosts}}} favouritesCount {{sortOrder, select, asc {fewest likes} desc {most likes}}} repliesCount {{sortOrder, select, asc {fewest replies} desc {most replies}}} density {{sortOrder, select, asc {least dense} desc {most dense}}}} first{groupBy, select, account {, grouped by authors} other {}}"
msgstr "Показуються {selectedFilterCategory, select, all {усі дописи} original {оригінальні дописи} replies {відповіді} boosts {поширення} followedTags {відстежувані теґи} groups {групи} filtered {відфільтровані дописи}}, {sortBy, select, createdAt {{sortOrder, select, asc {найстаріші} desc {найновіші}}} reblogsCount {{sortOrder, select, asc {найменш поширені} desc {найпоширеніші}}} favouritesCount {{sortOrder, select, asc {найменш вподобані} desc {найвподобаніші}}} repliesCount {{sortOrder, select, asc {найменше відповідей} desc {найбільше відповідей}}} density {{sortOrder, select, asc {найменш наповнені} desc {найбільш наповнені}}}} перші{groupBy, select, account {, згруповані за авторами} other {}}"

#: src/pages/catchup.jsx:889
#: src/pages/catchup.jsx:913
msgid "Catch-up <0>beta</0>"
msgstr "Ретроспектива <0>бета</0>"

#: src/pages/catchup.jsx:903
#: src/pages/catchup.jsx:1588
msgid "Help"
msgstr "Довідка"

#: src/pages/catchup.jsx:919
msgid "What is this?"
msgstr "Що це?"

#: src/pages/catchup.jsx:922
msgid "Catch-up is a separate timeline for your followings, offering a high-level view at a glance, with a simple, email-inspired interface to effortlessly sort and filter through posts."
msgstr "Ретроспектива (Cath-Up) - це окрема стрічка ваших підписок, що пропонує їх високорівневий перегляд з простим, натхненний email-ом інтерфейсом для багатофункціонального і легкого сортування і фільтру дописів."

#: src/pages/catchup.jsx:933
msgid "Preview of Catch-up UI"
msgstr "Попередній перегляд інтерфейсу Ретроспективи"

#: src/pages/catchup.jsx:942
msgid "Let's catch up"
msgstr "Зрозуміло, до роботи"

#: src/pages/catchup.jsx:947
msgid "Let's catch up on the posts from your followings."
msgstr "Нумо переглянемо дописи з ваших підписок."

#: src/pages/catchup.jsx:951
msgid "Show me all posts from…"
msgstr "Показати мені всі дописи з…"

#: src/pages/catchup.jsx:974
msgid "until the max"
msgstr "до максимуму"

#: src/pages/catchup.jsx:1004
msgid "Catch up"
msgstr "Наздогнати"

#: src/pages/catchup.jsx:1010
msgid "Overlaps with your last catch-up"
msgstr "Перетинається з вашою останньою ретроспективою"

#. placeholder {0}: dtf.format(new Date(lastCatchupEndAt))
#: src/pages/catchup.jsx:1022
msgid "Until the last catch-up ({0})"
msgstr "До останньої ретроспективи ({0})"

#: src/pages/catchup.jsx:1031
msgid "Note: your instance might only show a maximum of 800 posts in the Home timeline regardless of the time range. Could be less or more."
msgstr "Примітка: ваш інстанс може показати не більше 800 дописів в Домашній стрічці незалежно від часового діапазону. Може бути менше або більше."

#: src/pages/catchup.jsx:1041
msgid "Previously…"
msgstr "Раніше…"

#. placeholder {0}: pc.count
#: src/pages/catchup.jsx:1059
msgid "{0, plural, one {# post} other {# posts}}"
msgstr "{0, plural, one {# допис} few {# дописи} other {# дописів}}"

#: src/pages/catchup.jsx:1069
msgid "Remove this catch-up?"
msgstr "Видалити цю ретроспективу?"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1072
msgid "Removing Catch-up {0}"
msgstr "Видалення ретроспективи {0}"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1076
msgid "Catch-up {0} removed"
msgstr ""

#: src/pages/catchup.jsx:1090
msgid "Note: Only max 3 will be stored. The rest will be automatically removed."
msgstr "Примітка: Буде збережено не більше 3 останніх ретроспектив."

#: src/pages/catchup.jsx:1105
msgid "Fetching posts…"
msgstr "Отримання дописів…"

#: src/pages/catchup.jsx:1108
msgid "This might take a while."
msgstr "Це може зайняти деякий час."

#: src/pages/catchup.jsx:1143
msgid "Reset filters"
msgstr "Скинути фільтри"

#: src/pages/catchup.jsx:1151
#: src/pages/catchup.jsx:1594
msgid "Top links"
msgstr "Найпопулярніші посилання"

#. placeholder {0}: sharers.map((s) => { const { avatarStatic, displayName } = s; return ( <button type="button" class="plain" style={{ padding: 0, }} onClick={(e) => { e.preventDefault(); e.stopPropagation(); // Reset and filter to author const { id } = s; setSelectedAuthor(id); setSelectedFilterCategory('all'); }} > <Avatar url={avatarStatic} size="s" alt={displayName} /> </button> ); })
#: src/pages/catchup.jsx:1264
msgid "Shared by {0}"
msgstr "Поширили {0}"

#: src/pages/catchup.jsx:1319
#: src/pages/mentions.jsx:154
#: src/pages/search.jsx:329
msgid "All"
msgstr "Усе"

#. placeholder {0}: authorCountsList.length
#: src/pages/catchup.jsx:1404
msgid "{0, plural, one {# author} other {# authors}}"
msgstr "{0, plural, one {# автор} few {# автори} other {# авторів}}"

#: src/pages/catchup.jsx:1416
msgid "Sort"
msgstr "Сортування"

#: src/pages/catchup.jsx:1447
msgid "Date"
msgstr "Дата"

#: src/pages/catchup.jsx:1451
msgid "Density"
msgstr "Наповнення"

#. js-lingui-explicit-id
#: src/pages/catchup.jsx:1474
msgid "group.filter"
msgstr "Групування"

#: src/pages/catchup.jsx:1489
msgid "Authors"
msgstr "За авторами"

#: src/pages/catchup.jsx:1490
msgid "None"
msgstr "Не групувати"

#: src/pages/catchup.jsx:1506
msgid "Show all authors"
msgstr "Показати всіх авторів"

#: src/pages/catchup.jsx:1557
msgid "You don't have to read everything."
msgstr "Ви не мусите читати все."

#: src/pages/catchup.jsx:1558
msgid "That's all."
msgstr "На цьому все."

#: src/pages/catchup.jsx:1566
msgid "Back to top"
msgstr "На початок"

#: src/pages/catchup.jsx:1597
msgid "Links shared by followings, sorted by shared counts, boosts and likes."
msgstr "Посилання, поширені підписками, відсортовані за кількістю взаємодій, поширень і вподобань."

#: src/pages/catchup.jsx:1603
msgid "Sort: Density"
msgstr "Сортування: Наповнення"

#: src/pages/catchup.jsx:1606
msgid "Posts are sorted by information density or depth. Shorter posts are \"lighter\" while longer posts are \"heavier\". Posts with photos are \"heavier\" than posts without photos."
msgstr "Дописи відсортовані за наповненням або розміром інформації. Короткі дописи - \"легші\", коли як довші пости - \"важче\". Дописи із зображенням \"важче\", аніж дописи без зображень."

#: src/pages/catchup.jsx:1613
msgid "Group: Authors"
msgstr "Групування: За авторами"

#: src/pages/catchup.jsx:1616
msgid "Posts are grouped by authors, sorted by posts count per author."
msgstr "Дописи згруповані за авторами, відсортовані за кількістю дописів на автора."

#: src/pages/catchup.jsx:1663
msgid "Next author"
msgstr "Наступний автор"

#: src/pages/catchup.jsx:1671
msgid "Previous author"
msgstr "Попередній автор"

#: src/pages/catchup.jsx:1687
msgid "Scroll to top"
msgstr "Прокрутити догори"

#: src/pages/favourites.jsx:28
msgid "No likes yet. Go like something!"
msgstr "Жодного вподобання. Нумо вподобайте щось!"

#: src/pages/favourites.jsx:29
msgid "Unable to load likes."
msgstr "Не вдалося завантажити вподобання."

#: src/pages/filters.jsx:23
msgid "Home and lists"
msgstr "Домашня та Списки"

#: src/pages/filters.jsx:25
msgid "Public timelines"
msgstr "Публічна стрічка"

#: src/pages/filters.jsx:26
msgid "Conversations"
msgstr "Бесіди"

#: src/pages/filters.jsx:27
msgid "Profiles"
msgstr "Профілі"

#: src/pages/filters.jsx:42
msgid "Never"
msgstr "Ніколи"

#: src/pages/filters.jsx:104
#: src/pages/filters.jsx:229
msgid "New filter"
msgstr "Новий фільтр"

#. placeholder {0}: filters.length
#: src/pages/filters.jsx:152
msgid "{0, plural, one {# filter} other {# filters}}"
msgstr "{0, plural, one {# фільтр} few {# фільтри} other {# фільтрів}}"

#: src/pages/filters.jsx:167
msgid "Unable to load filters."
msgstr "Не вдалося завантажити фільтри."

#: src/pages/filters.jsx:171
msgid "No filters yet."
msgstr "Немає жодного фільтра."

#: src/pages/filters.jsx:178
msgid "Add filter"
msgstr "Додати фільтр"

#: src/pages/filters.jsx:229
msgid "Edit filter"
msgstr "Редагувати фільтр"

#: src/pages/filters.jsx:346
msgid "Unable to edit filter"
msgstr "Не вдалося редагувати фільтр"

#: src/pages/filters.jsx:347
msgid "Unable to create filter"
msgstr "Не вдалося створити фільтр"

#: src/pages/filters.jsx:356
msgid "Title"
msgstr "Заголовок"

#: src/pages/filters.jsx:397
msgid "Whole word"
msgstr "Ціле слово"

#: src/pages/filters.jsx:423
msgid "No keywords. Add one."
msgstr "Немає ключових слів. Додайте одне."

#: src/pages/filters.jsx:450
msgid "Add keyword"
msgstr "Додати ключове слово"

#. placeholder {0}: filteredEditKeywords.length
#: src/pages/filters.jsx:454
msgid "{0, plural, one {# keyword} other {# keywords}}"
msgstr "{0, plural, one {# ключове слово} few {# ключових слова} other {# ключових слів}}"

#: src/pages/filters.jsx:467
msgid "Filter from…"
msgstr "Фільтрувати з…"

#: src/pages/filters.jsx:493
msgid "* Not implemented yet"
msgstr "* Ще не реалізовано"

#: src/pages/filters.jsx:499
msgid "Status: <0><1/></0>"
msgstr "Статус: <0><1/></0>"

#: src/pages/filters.jsx:508
msgid "Change expiry"
msgstr "Змінити термін дії"

#: src/pages/filters.jsx:508
msgid "Expiry"
msgstr "Термін дії"

#: src/pages/filters.jsx:527
msgid "Filtered post will be…"
msgstr "Відфільтрований допис буде…"

#: src/pages/filters.jsx:538
msgid "obscured (media only)"
msgstr ""

#: src/pages/filters.jsx:552
msgid "minimized"
msgstr "згорнуто"

#: src/pages/filters.jsx:562
msgid "hidden"
msgstr "приховано"

#: src/pages/filters.jsx:579
msgid "Delete this filter?"
msgstr "Видалити цей фільтр?"

#: src/pages/filters.jsx:592
msgid "Unable to delete filter."
msgstr "Не вдалося видалити фільтр."

#: src/pages/filters.jsx:625
msgid "Expired"
msgstr "Прострочений"

#: src/pages/filters.jsx:627
msgid "Expiring <0/>"
msgstr "Минає <0/>"

#: src/pages/filters.jsx:631
msgid "Never expires"
msgstr "Необмежений термін дії"

#. placeholder {0}: followedHashtags.length
#: src/pages/followed-hashtags.jsx:71
msgid "{0, plural, one {# hashtag} other {# hashtags}}"
msgstr "{0, plural,one {# хештеґ} few {# хештеґи} other {# хештеґів}}"

#: src/pages/followed-hashtags.jsx:86
msgid "Unable to load followed hashtags."
msgstr "Не вдалося завантажити хештеґи, що відстежуються."

#: src/pages/followed-hashtags.jsx:90
msgid "No hashtags followed yet."
msgstr "Жодного хештеґу не відстежується."

#: src/pages/following.jsx:147
msgid "Nothing to see here."
msgstr "Немає на що дивитись."

#: src/pages/following.jsx:148
#: src/pages/list.jsx:109
msgid "Unable to load posts."
msgstr "Не вдалося завантажити дописи."

#: src/pages/hashtag.jsx:56
msgid "{hashtagTitle} (Media only) on {instance}"
msgstr "{hashtagTitle} (Лише медіа) на {instance}"

#: src/pages/hashtag.jsx:57
msgid "{hashtagTitle} on {instance}"
msgstr "{hashtagTitle} на {instance}"

#: src/pages/hashtag.jsx:59
msgid "{hashtagTitle} (Media only)"
msgstr "{hashtagTitle} (Лише медіа)"

#: src/pages/hashtag.jsx:60
msgid "{hashtagTitle}"
msgstr "{hashtagTitle}"

#: src/pages/hashtag.jsx:185
msgid "No one has posted anything with this tag yet."
msgstr "Жодного допису із цим хештеґом."

#: src/pages/hashtag.jsx:186
msgid "Unable to load posts with this tag"
msgstr "Не вдалося завантажити дописи із цим хештеґом."

#: src/pages/hashtag.jsx:212
msgid "Unfollow #{hashtag}?"
msgstr "Не відстежувати #{hashtag}?"

#: src/pages/hashtag.jsx:227
msgid "Unfollowed #{hashtag}"
msgstr "#{hashtag} не відстежується"

#: src/pages/hashtag.jsx:242
msgid "Followed #{hashtag}"
msgstr "#{hashtag} відстежується"

#: src/pages/hashtag.jsx:258
msgid "Following…"
msgstr "Відстежуємо…"

#: src/pages/hashtag.jsx:287
msgid "Unfeatured on profile"
msgstr "Не рекомендовано в профілі"

#: src/pages/hashtag.jsx:301
msgid "Unable to unfeature on profile"
msgstr "Не вдалося скасувати рекомендацію в профілі"

#: src/pages/hashtag.jsx:310
#: src/pages/hashtag.jsx:326
msgid "Featured on profile"
msgstr "Рекомендовано в профілі"

#: src/pages/hashtag.jsx:398
msgid "{TOTAL_TAGS_LIMIT, plural, other {Max # tags}}"
msgstr "{TOTAL_TAGS_LIMIT, plural, few {Макс. # теґи} other {Макс. # теґів}}"

#: src/pages/hashtag.jsx:401
msgid "Add hashtag"
msgstr "Додати хештеґ"

#: src/pages/hashtag.jsx:433
msgid "Remove hashtag"
msgstr "Видалити хештеґ"

#: src/pages/hashtag.jsx:447
msgid "{SHORTCUTS_LIMIT, plural, one {Max # shortcut reached. Unable to add shortcut.} other {Max # shortcuts reached. Unable to add shortcut.}}"
msgstr "Досягнуто ліміт в {SHORTCUTS_LIMIT, plural, one {# ярлик.} few {# ярлики.} other {# ярликів.}} Не вдалося додати ярлик."

#: src/pages/hashtag.jsx:476
msgid "This shortcut already exists"
msgstr "Цей ярлик вже існує"

#: src/pages/hashtag.jsx:479
msgid "Hashtag shortcut added"
msgstr "Ярлик хештеґу додано"

#: src/pages/hashtag.jsx:485
msgid "Add to Shortcuts"
msgstr "Додати в Ярлики"

#: src/pages/hashtag.jsx:491
#: src/pages/public.jsx:141
#: src/pages/trending.jsx:481
msgid "Enter a new instance e.g. \"mastodon.social\""
msgstr "Введіть новий інстанс, напр. \"twiukraine.com\""

#: src/pages/hashtag.jsx:494
#: src/pages/public.jsx:144
#: src/pages/trending.jsx:484
msgid "Invalid instance"
msgstr "Некоректний інстанс"

#: src/pages/hashtag.jsx:508
#: src/pages/public.jsx:158
#: src/pages/trending.jsx:496
msgid "Go to another instance…"
msgstr "Перейти до іншого інстансу…"

#: src/pages/hashtag.jsx:521
#: src/pages/public.jsx:171
#: src/pages/trending.jsx:507
msgid "Go to my instance (<0>{currentInstance}</0>)"
msgstr "Перейти до мого інстансу (<0>{currentInstance}</0>)"

#: src/pages/home.jsx:224
msgid "Unable to fetch notifications."
msgstr "Не вдалося отримати сповіщення."

#: src/pages/home.jsx:245
msgid "<0>New</0> <1>Follow Requests</1>"
msgstr "<0>Нові</0> <1>Запити на Підписку</1>"

#: src/pages/home.jsx:251
msgid "See all"
msgstr "Переглянути всі"

#: src/pages/http-route.jsx:68
msgid "Resolving…"
msgstr "Визначається…"

#: src/pages/http-route.jsx:79
msgid "Unable to resolve URL"
msgstr "Не вдалося визначити URL"

#: src/pages/list.jsx:108
msgid "Nothing yet."
msgstr "Поки нічого."

#: src/pages/list.jsx:201
#: src/pages/list.jsx:307
msgid "Manage members"
msgstr "Керувати учасниками"

#. placeholder {0}: account.username
#: src/pages/list.jsx:342
msgid "Remove <0>@{0}</0> from list?"
msgstr "Вилучити <0>@{0}</0> зі списку?"

#: src/pages/list.jsx:388
msgid "Remove…"
msgstr "Вилучити…"

#. placeholder {0}: lists.length
#: src/pages/lists.jsx:115
msgid "{0, plural, one {# list} other {# lists}}"
msgstr "{0, plural, one {# список} few {# списки} other {# списків}}"

#: src/pages/lists.jsx:135
msgid "No lists yet."
msgstr "Списків поки немає."

#: src/pages/login.jsx:122
#: src/pages/login.jsx:132
msgid "Failed to register application"
msgstr "Не вдалося зареєструвати застосунок"

#: src/pages/login.jsx:218
msgid "instance domain"
msgstr "домен інстансу"

#: src/pages/login.jsx:242
msgid "e.g. “mastodon.social”"
msgstr "напр. \"twiukraine.com\""

#: src/pages/login.jsx:253
msgid "Failed to log in. Please try again or try another instance."
msgstr "Не вдалося увійти. Будь ласка, повторіть спробу або спробуйте інший екземпляр."

#: src/pages/login.jsx:265
msgid "Continue with {selectedInstanceText}"
msgstr "Продовжити з {selectedInstanceText}"

#: src/pages/login.jsx:266
msgid "Continue"
msgstr "Продовжити"

#: src/pages/login.jsx:274
msgid "Don't have an account? Create one!"
msgstr "У вас ще немає облікового запису? Створіть його!"

#: src/pages/mentions.jsx:21
msgid "Private mentions"
msgstr "Особисті згадки"

#: src/pages/mentions.jsx:166
msgid "Private"
msgstr "Приватне"

#: src/pages/mentions.jsx:176
msgid "No one mentioned you :("
msgstr "Ніхто не згадував вас :("

#: src/pages/mentions.jsx:177
msgid "Unable to load mentions."
msgstr "Не вдалося завантажити згадки."

#: src/pages/notifications.jsx:108
msgid "You don't follow"
msgstr "На кого ви не підписані"

#: src/pages/notifications.jsx:109
msgid "Who don't follow you"
msgstr "Хто не підписаний на вас"

#: src/pages/notifications.jsx:110
msgid "With a new account"
msgstr "З новим обліковим записом"

#: src/pages/notifications.jsx:111
msgid "Who unsolicitedly private mention you"
msgstr "Хто небажано згадав вас"

#: src/pages/notifications.jsx:112
msgid "Who are limited by server moderators"
msgstr "Хто обмежений модераторами сервера"

#: src/pages/notifications.jsx:605
#: src/pages/notifications.jsx:946
msgid "Notifications settings"
msgstr "Налаштування сповіщень"

#: src/pages/notifications.jsx:623
msgid "New notifications"
msgstr "Нові сповіщення"

#. placeholder {0}: announcements.length
#: src/pages/notifications.jsx:634
msgid "{0, plural, one {Announcement} other {Announcements}}"
msgstr "{0, plural, one {Оголошення} few {Оголошення} other {Оголошень}}"

#: src/pages/notifications.jsx:681
#: src/pages/settings.jsx:1206
msgid "Follow requests"
msgstr "Запити на підписку"

#. placeholder {0}: followRequests.length
#: src/pages/notifications.jsx:686
msgid "{0, plural, one {# follow request} other {# follow requests}}"
msgstr "{0, plural, one {# запит} few {# запити} other {# запитів}} на підписку"

#. placeholder {0}: notificationsPolicy.summary.pendingRequestsCount
#: src/pages/notifications.jsx:741
msgid "{0, plural, one {Filtered notifications from # person} other {Filtered notifications from # people}}"
msgstr "Відфільтровані сповіщення від {0, plural, one {# користувача} other {# користувачів}}"

#: src/pages/notifications.jsx:814
msgid "Only mentions"
msgstr "Лише згадки"

#: src/pages/notifications.jsx:818
msgid "Today"
msgstr "Сьогодні"

#: src/pages/notifications.jsx:823
msgid "You're all caught up."
msgstr "Ви наздогнали все."

#: src/pages/notifications.jsx:846
msgid "Yesterday"
msgstr "Вчора"

#: src/pages/notifications.jsx:894
msgid "Unable to load notifications"
msgstr "Не вдалося завантажити сповіщення"

#: src/pages/notifications.jsx:973
msgid "Notifications settings updated"
msgstr "Налаштування сповіщень оновлено"

#: src/pages/notifications.jsx:981
msgid "Filter out notifications from people:"
msgstr "Відфільтрувати сповіщення від користувачів:"

#: src/pages/notifications.jsx:995
msgid "Filter"
msgstr "Фільтрувати"

#: src/pages/notifications.jsx:998
msgid "Ignore"
msgstr "Ігнорувати"

#. placeholder {0}: niceDateTime(updatedAtDate)
#: src/pages/notifications.jsx:1071
msgid "Updated <0>{0}</0>"
msgstr "Оновлено <0>{0}</0>"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1139
msgid "View notifications from <0>@{0}</0>"
msgstr "Переглянути сповіщення від <0>@{0}</0>"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1160
msgid "Notifications from <0>@{0}</0>"
msgstr "Сповіщення від <0>@{0}</0>"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1228
msgid "Notifications from @{0} will not be filtered from now on."
msgstr "Відтепер сповіщення від @{0} не будуть відфільтровані."

#: src/pages/notifications.jsx:1233
msgid "Unable to accept notification request"
msgstr "Не вдалося прийняти запит на сповіщення"

#: src/pages/notifications.jsx:1238
msgid "Allow"
msgstr "Дозволити"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1258
msgid "Notifications from @{0} will not show up in Filtered notifications from now on."
msgstr ""

#: src/pages/notifications.jsx:1263
msgid "Unable to dismiss notification request"
msgstr "Не вдалося відхилити запит на сповіщення"

#: src/pages/notifications.jsx:1268
msgid "Dismiss"
msgstr "Відхилити"

#: src/pages/notifications.jsx:1283
msgid "Dismissed"
msgstr "Відхилено"

#: src/pages/public.jsx:28
msgid "Local timeline ({instance})"
msgstr "Локальна стрічка ({instance})"

#: src/pages/public.jsx:29
msgid "Federated timeline ({instance})"
msgstr "Федеративна стрічка ({instance})"

#: src/pages/public.jsx:92
msgid "Local timeline"
msgstr "Локальна стрічка"

#: src/pages/public.jsx:92
msgid "Federated timeline"
msgstr "Федеративна стрічка"

#: src/pages/public.jsx:98
msgid "No one has posted anything yet."
msgstr "Ще ніхто нічого не опублікував."

#: src/pages/public.jsx:125
msgid "Switch to Federated"
msgstr "Перемкнути на Федеративну"

#: src/pages/public.jsx:132
msgid "Switch to Local"
msgstr "Перемкнути на Локальну"

#: src/pages/scheduled-posts.jsx:110
msgid "No scheduled posts."
msgstr ""

#. Scheduled [in 1 day] ([Thu, Feb 27, 6:30:00 PM])
#. placeholder {0}: niceDateTime(scheduledAt, { formatOpts: { weekday: 'short', second: 'numeric', }, })
#: src/pages/scheduled-posts.jsx:207
msgid "Scheduled <0><1/></0> <2>({0})</2>"
msgstr ""

#. Scheduled [in 1 day]
#: src/pages/scheduled-posts.jsx:263
msgid "Scheduled <0><1/></0>"
msgstr ""

#: src/pages/scheduled-posts.jsx:308
msgid "Scheduled post rescheduled"
msgstr ""

#: src/pages/scheduled-posts.jsx:315
msgid "Failed to reschedule post"
msgstr ""

#: src/pages/scheduled-posts.jsx:338
msgid "Reschedule"
msgstr ""

#: src/pages/scheduled-posts.jsx:344
msgid "Delete scheduled post?"
msgstr ""

#: src/pages/scheduled-posts.jsx:352
msgid "Scheduled post deleted"
msgstr ""

#: src/pages/scheduled-posts.jsx:359
msgid "Failed to delete scheduled post"
msgstr ""

#: src/pages/search.jsx:50
msgid "Search: {q} (Posts)"
msgstr "Пошук: {q} (Дописи)"

#: src/pages/search.jsx:53
msgid "Search: {q} (Accounts)"
msgstr "Пошук: {q} (Облікові записи)"

#: src/pages/search.jsx:56
msgid "Search: {q} (Hashtags)"
msgstr "Пошук: {q} (Хештеґи)"

#: src/pages/search.jsx:59
msgid "Search: {q}"
msgstr "Пошук: {q}"

#: src/pages/search.jsx:339
#: src/pages/search.jsx:421
msgid "Hashtags"
msgstr "Хештеґи"

#: src/pages/search.jsx:371
#: src/pages/search.jsx:425
#: src/pages/search.jsx:495
msgid "See more"
msgstr "Показати більше"

#: src/pages/search.jsx:397
msgid "See more accounts"
msgstr "Переглянути більше облікових записів"

#: src/pages/search.jsx:411
msgid "No accounts found."
msgstr "Жодного облікового запису не знайдено."

#: src/pages/search.jsx:467
msgid "See more hashtags"
msgstr "Переглянути більше хештеґів"

#: src/pages/search.jsx:481
msgid "No hashtags found."
msgstr "Хештеґи не знайдено."

#: src/pages/search.jsx:525
msgid "See more posts"
msgstr "Показати більше повідомлень"

#: src/pages/search.jsx:539
msgid "No posts found."
msgstr "Жодного допису не знайдено."

#: src/pages/search.jsx:583
msgid "Enter your search term or paste a URL above to get started."
msgstr "Введіть пошуковий запит або вставте посилання вище, щоб розпочати."

#: src/pages/settings.jsx:95
msgid "Settings"
msgstr "Налаштування"

#: src/pages/settings.jsx:104
msgid "Appearance"
msgstr "Тема"

#: src/pages/settings.jsx:180
msgid "Light"
msgstr "Світла"

#: src/pages/settings.jsx:191
msgid "Dark"
msgstr "Темна"

#: src/pages/settings.jsx:204
msgid "Auto"
msgstr "Автоматична"

#: src/pages/settings.jsx:214
msgid "Text size"
msgstr "Розмір тексту"

#. Preview of one character, in smallest size
#. Preview of one character, in largest size
#: src/pages/settings.jsx:219
#: src/pages/settings.jsx:244
msgid "A"
msgstr "А"

#: src/pages/settings.jsx:258
msgid "Display language"
msgstr "Мова інтерфейсу"

#: src/pages/settings.jsx:267
msgid "Volunteer translations"
msgstr "Допомогти з перекладом"

#: src/pages/settings.jsx:278
msgid "Posting"
msgstr "Публікація"

#: src/pages/settings.jsx:285
msgid "Default visibility"
msgstr "Видимість допису за замовчуванням"

#: src/pages/settings.jsx:286
#: src/pages/settings.jsx:332
msgid "Synced"
msgstr "Синхронізовано"

#: src/pages/settings.jsx:311
msgid "Failed to update posting privacy"
msgstr "Не вдалося оновити видимість дописів"

#: src/pages/settings.jsx:334
msgid "Synced to your instance server's settings. <0>Go to your instance ({instance}) for more settings.</0>"
msgstr "Синхронізовано з налаштуваннями на вашому інстансі.<0> Перейдіть на сайт вашого інстансу ({instance}) для детальних налаштувань.</0>"

#: src/pages/settings.jsx:349
msgid "Experiments"
msgstr "Експериментальні опції"

#: src/pages/settings.jsx:362
msgid "Auto refresh timeline posts"
msgstr "Автоматично оновлювати дописи стрічки"

#: src/pages/settings.jsx:374
msgid "Boosts carousel"
msgstr "Карусель поширень"

#: src/pages/settings.jsx:391
msgid "Post translation"
msgstr "Переклад допису"

#: src/pages/settings.jsx:402
msgid "Translate to "
msgstr ""

#: src/pages/settings.jsx:413
msgid "System language ({systemTargetLanguageText})"
msgstr "Мова системи ({systemTargetLanguageText})"

#. placeholder {0}: snapStates.settings.contentTranslationHideLanguages .length
#: src/pages/settings.jsx:439
msgid "{0, plural, =0 {Hide \"Translate\" button for:} other {Hide \"Translate\" button for (#):}}"
msgstr "{0, plural, =0 {Приховати кнопку \"Перекласти\" для:} other {Приховати кнопку \"Перекласти\" для (#):}}"

#: src/pages/settings.jsx:494
msgid "Note: This feature uses external translation services, powered by <0>{TRANSLATION_API_NAME}</0>."
msgstr ""

#: src/pages/settings.jsx:522
msgid "Auto inline translation"
msgstr "Автоматичний вбудований переклад"

#: src/pages/settings.jsx:526
msgid "Automatically show translation for posts in timeline. Only works for <0>short</0> posts without content warning, media and poll."
msgstr "Автоматично показувати переклад постів в стрічці. Працює лише для <0>коротких</0> дописів без чутливого контенту, медіа та опитування."

#: src/pages/settings.jsx:547
msgid "GIF Picker for composer"
msgstr "Пошук GIF в редакторі"

#: src/pages/settings.jsx:551
msgid "Note: This feature uses external GIF search service, powered by <0>GIPHY</0>. G-rated (suitable for viewing by all ages), tracking parameters are stripped, referrer information is omitted from requests, but search queries and IP address information will still reach their servers."
msgstr "Примітка: Ця функція використовує зовнішній сервіс пошуку GIF від <0>GIPHY</0>. G-рейтинг (підходить для перегляду будь-якому віку), параметри відстеження та інформація про джерело будуть прибрані, але пошукові запити та дані IP-адреси все одно будуть передаватися на їхній сервер."

#: src/pages/settings.jsx:580
msgid "Image description generator"
msgstr "Генератор опису зображення"

#: src/pages/settings.jsx:585
msgid "Only for new images while composing new posts."
msgstr "Лише для нових зображень при створенні нових дописів."

#: src/pages/settings.jsx:592
msgid "Note: This feature uses external AI service, powered by <0>img-alt-api</0>. May not work well. Only for images and in English."
msgstr "Примітка: Ця функціональність використовує зовнішній сервіс AI, що базується на <0>img-alt-api</0>. Може не працювати добре. Тільки для зображень та англійською мовою."

#: src/pages/settings.jsx:618
msgid "Server-side grouped notifications"
msgstr "Згруповані сервером сповіщення"

#: src/pages/settings.jsx:622
msgid "Alpha-stage feature. Potentially improved grouping window but basic grouping logic."
msgstr "Функціональність на етапі альфи. Потенційно покращене групування вікон, але базова логіка групування."

#: src/pages/settings.jsx:643
msgid "\"Cloud\" import/export for shortcuts settings"
msgstr "\"Хмарний\" імпорт/експорт для налаштувань ярликів"

#: src/pages/settings.jsx:648
msgid "⚠️⚠️⚠️ Very experimental.<0/>Stored in your own profile’s notes. Profile (private) notes are mainly used for other profiles, and hidden for own profile."
msgstr "⚠️⚠️⚠️ Занадто експериментально.<0/>Зберігається в нотатках вашого профілю. (Приватні) нотатки профілю зазвичай використовуються для інших профілів і приховані для власного профілю."

#: src/pages/settings.jsx:659
msgid "Note: This feature uses currently-logged-in instance server API."
msgstr "Примітка: Ця функціональність використовує API поточно-авторизованого сервера інстансу."

#: src/pages/settings.jsx:676
msgid "Cloak mode <0>(<1>Text</1> → <2>████</2>)</0>"
msgstr "Режим маскування <0>(<1>Текст</1> → <2>████</2>)</0>"

#: src/pages/settings.jsx:685
msgid "Replace text as blocks, useful when taking screenshots, for privacy reasons."
msgstr "Замінює текст блоками. Корисно при створенні скриншотів (з міркувань конфіденційності)."

#: src/pages/settings.jsx:710
msgid "About"
msgstr "Про застосунок"

#: src/pages/settings.jsx:749
msgid "<0>Built</0> by <1>@cheeaun</1>"
msgstr "<0>Створено</0> <1>@cheeaun</1>"

#: src/pages/settings.jsx:778
msgid "Sponsor"
msgstr "Спонсорувати"

#: src/pages/settings.jsx:786
msgid "Donate"
msgstr "Підтримати"

#: src/pages/settings.jsx:802
msgid "What's new"
msgstr ""

#: src/pages/settings.jsx:806
msgid "Privacy Policy"
msgstr "Політика конфіденційності"

#. placeholder {0}: WEBSITE.replace(/https?:\/\//g, '').replace(/\/$/, '')
#: src/pages/settings.jsx:813
msgid "<0>Site:</0> {0}"
msgstr "<0>Адреса клієнта:</0> {0}"

#. placeholder {0}: !__FAKE_COMMIT_HASH__ && ( <span class="ib insignificant"> ( <a href={`https://github.com/cheeaun/phanpy/commit/${__COMMIT_HASH__}`} target="_blank" rel="noopener" > <RelativeTime datetime={new Date(__BUILD_TIME__)} /> </a> ) </span> )
#: src/pages/settings.jsx:820
msgid "<0>Version:</0> <1/> {0}"
msgstr "<0>Версія:</0> <1/> {0}"

#: src/pages/settings.jsx:835
msgid "Version string copied"
msgstr "Версію скопійовано"

#: src/pages/settings.jsx:838
msgid "Unable to copy version string"
msgstr "Не вдалося скопіювати версію"

#: src/pages/settings.jsx:1103
#: src/pages/settings.jsx:1108
msgid "Failed to update subscription. Please try again."
msgstr "Не вдалося оновити підписку. Будь ласка, спробуйте ще раз."

#: src/pages/settings.jsx:1114
msgid "Failed to remove subscription. Please try again."
msgstr "Не вдалося скасувати підписку. Будь ласка, спробуйте ще раз."

#: src/pages/settings.jsx:1121
msgid "Push Notifications (beta)"
msgstr "Push-сповіщення (бета)"

#: src/pages/settings.jsx:1143
msgid "Push notifications are blocked. Please enable them in your browser settings."
msgstr "Push-сповіщення заблоковані. Будь ласка, увімкніть їх у налаштуваннях свого браузеру."

#. placeholder {0}: [ { value: 'all', label: t`anyone`, }, { value: 'followed', label: t`people I follow`, }, { value: 'follower', label: t`followers`, }, ].map((type) => ( <option value={type.value}>{type.label}</option> ))
#: src/pages/settings.jsx:1152
msgid "Allow from <0>{0}</0>"
msgstr "Дозволити від <0>{0}</0>"

#: src/pages/settings.jsx:1161
msgid "anyone"
msgstr "будь-кого"

#: src/pages/settings.jsx:1165
msgid "people I follow"
msgstr "моїх підписок"

#: src/pages/settings.jsx:1169
msgid "followers"
msgstr "підписників"

#: src/pages/settings.jsx:1202
msgid "Follows"
msgstr "Підписки"

#: src/pages/settings.jsx:1210
msgid "Polls"
msgstr "Опитування"

#: src/pages/settings.jsx:1214
msgid "Post edits"
msgstr "Редагування дописів"

#: src/pages/settings.jsx:1235
msgid "Push permission was not granted since your last login. You'll need to <0><1>log in</1> again to grant push permission</0>."
msgstr "Дозвіл на push-сповіщення не було надано з моменту останнього входу. Вам потрібно <0><1>увійти в систему</1> знову, щоб надати дозвіл."

#: src/pages/settings.jsx:1251
msgid "NOTE: Push notifications only work for <0>one account</0>."
msgstr "ПРИМІТКА: Push-сповіщення працюють лише для <0>одного облікового запису</0>."

#. js-lingui-explicit-id
#: src/pages/status.jsx:657
#: src/pages/status.jsx:1220
msgid "post.title"
msgstr "Допис"

#: src/pages/status.jsx:911
msgid "You're not logged in. Interactions (reply, boost, etc) are not possible."
msgstr "Ви не увійшли в систему. Взаємодія (відповідь, поширення тощо) неможлива."

#: src/pages/status.jsx:931
msgid "This post is from another instance (<0>{instance}</0>). Interactions (reply, boost, etc) are not possible."
msgstr "Цей допис з іншого інстансу (<0>{instance}</0>). Взаємодія (відповідь, поширення тощо) неможлива."

#: src/pages/status.jsx:959
msgid "Error: {e}"
msgstr "Помилка: {e}"

#: src/pages/status.jsx:966
msgid "Switch to my instance to enable interactions"
msgstr "Перемкнутися на мій інстанс задля взаємодії"

#: src/pages/status.jsx:1068
msgid "Unable to load replies."
msgstr "Не вдалося завантажити відповіді."

#: src/pages/status.jsx:1180
msgid "Back"
msgstr "Назад"

#: src/pages/status.jsx:1211
msgid "Go to main post"
msgstr "Перейти до головного запису"

#. placeholder {0}: ancestors.length
#: src/pages/status.jsx:1234
msgid "{0} posts above ‒ Go to top"
msgstr "{0} дописів вище ‒ Перейти вгору"

#: src/pages/status.jsx:1281
#: src/pages/status.jsx:1344
msgid "Switch to Side Peek view"
msgstr "Перемкнутися на вигляд з Боковою Панеллю"

#: src/pages/status.jsx:1345
msgid "Switch to Full view"
msgstr "Перемкнутися на Повний вигляд"

#: src/pages/status.jsx:1363
msgid "Show all sensitive content"
msgstr "Показати весь чутливий вміст"

#: src/pages/status.jsx:1368
msgid "Experimental"
msgstr "Експериментальне"

#: src/pages/status.jsx:1377
msgid "Unable to switch"
msgstr "Не вдалося перемкнутися"

#. placeholder {0}: punycode.toUnicode( postInstance, )
#: src/pages/status.jsx:1384
msgid "Switch to post's instance ({0})"
msgstr ""

#: src/pages/status.jsx:1387
msgid "Switch to post's instance"
msgstr "Перемкнутися на інстанс допису"

#: src/pages/status.jsx:1445
msgid "Unable to load post"
msgstr "Не вдалося завантажити дописи"

#. placeholder {0}: replies.length
#. placeholder {1}: shortenNumber(replies.length)
#: src/pages/status.jsx:1581
msgid "{0, plural, one {# reply} other {<0>{1}</0> replies}}"
msgstr "{0, plural, one {# відповідь} few {<0>{1}</0> відповіді} other {<0>{1}</0> відповідей}}"

#. placeholder {0}: shortenNumber(totalComments)
#: src/pages/status.jsx:1599
msgid "{totalComments, plural, one {# comment} other {<0>{0}</0> comments}}"
msgstr "{totalComments, plural, one {# коментар} few {<0>{0}</0> коментарі} other {<0>{0}</0> коментарів}}"

#: src/pages/status.jsx:1621
msgid "View post with its replies"
msgstr "Переглянути допис і відповіді на нього"

#: src/pages/trending.jsx:77
msgid "Trending ({instance})"
msgstr "Популярне ({instance})"

#: src/pages/trending.jsx:235
msgid "Trending News"
msgstr "Популярні Новини"

#. By [Author]
#. placeholder {0}: author ? ( <NameText account={author} showAvatar /> ) : authorUrl ? ( <a href={authorUrl} target="_blank" rel="noopener" > {authorName} </a> ) : ( authorName )
#: src/pages/trending.jsx:350
msgid "By {0}"
msgstr "Від {0}"

#: src/pages/trending.jsx:411
msgid "Back to showing trending posts"
msgstr "Назад до популярних дописів"

#. placeholder {0}: currentLink .replace(/^https?:\/\/(www\.)?/i, '') .replace(/\/$/, '')
#: src/pages/trending.jsx:416
msgid "Showing posts mentioning <0>{0}</0>"
msgstr "Показ дописів зі згадкою <0>{0}</0>"

#: src/pages/trending.jsx:428
msgid "Trending posts"
msgstr "Популярні дописи"

#: src/pages/trending.jsx:451
msgid "No trending posts."
msgstr "Немає популярних дописів."

#: src/pages/welcome.jsx:54
msgid "A minimalistic opinionated Mastodon web client."
msgstr "Мінімалістичний вдумливий клієнт для багатьох платформ Федиверсу."

#: src/pages/welcome.jsx:65
msgid "Log in with Mastodon"
msgstr "Увійти через Федиверс"

#: src/pages/welcome.jsx:71
msgid "Sign up"
msgstr "Зареєструватися"

#: src/pages/welcome.jsx:78
msgid "Connect your existing Mastodon/Fediverse account.<0/>Your credentials are not stored on this server."
msgstr "Під'єднайте ваш обліковий запис Федиверсу (Mastodon, Pleroma тощо).<0/>Ваші облікові дані не зберігаються на цьому сервері."

#: src/pages/welcome.jsx:95
msgid "<0>Built</0> by <1>@cheeaun</1>. <2>Privacy Policy</2>."
msgstr "<0>Створений</0> <1>@cheeaun</1>. <2>Політика Конфіденційності</2>."

#: src/pages/welcome.jsx:126
msgid "Screenshot of Boosts Carousel"
msgstr "Скриншот Каруселі Поширень"

#: src/pages/welcome.jsx:130
msgid "Boosts Carousel"
msgstr "Карусель Поширень"

#: src/pages/welcome.jsx:133
msgid "Visually separate original posts and re-shared posts (boosted posts)."
msgstr "Візуально розділяє оригінальні та повторно поширені дописи."

#: src/pages/welcome.jsx:142
msgid "Screenshot of nested comments thread"
msgstr "Скриншот вкладених коментарів нитки"

#: src/pages/welcome.jsx:146
msgid "Nested comments thread"
msgstr "Вкладені коментарі нитки"

#: src/pages/welcome.jsx:149
msgid "Effortlessly follow conversations. Semi-collapsible replies."
msgstr "Легке відстежування бесід. Відповіді, що напів згортаються."

#: src/pages/welcome.jsx:157
msgid "Screenshot of grouped notifications"
msgstr "Скриншот згрупованих сповіщень"

#: src/pages/welcome.jsx:161
msgid "Grouped notifications"
msgstr "Згруповані сповіщення"

#: src/pages/welcome.jsx:164
msgid "Similar notifications are grouped and collapsed to reduce clutter."
msgstr "Подібні сповіщення групуються і згортаються, щоб зменшити захаращення."

#: src/pages/welcome.jsx:173
msgid "Screenshot of multi-column UI"
msgstr "Скриншот інтерфейсу з кількома стовпцями"

#: src/pages/welcome.jsx:177
msgid "Single or multi-column"
msgstr "Один або декілька стовпців"

#: src/pages/welcome.jsx:180
msgid "By default, single column for zen-mode seekers. Configurable multi-column for power users."
msgstr "За замовченням, один стовпець - для шукачів сконцентрованого перегляду. Декілька стовпців - для просунутих користувачів."

#: src/pages/welcome.jsx:189
msgid "Screenshot of multi-hashtag timeline with a form to add more hashtags"
msgstr "Скриншот стрічки з кількома хештеґами з формою для додавання хештеґів"

#: src/pages/welcome.jsx:193
msgid "Multi-hashtag timeline"
msgstr "Стрічка з кількома хештеґами"

#: src/pages/welcome.jsx:196
msgid "Up to 5 hashtags combined into a single timeline."
msgstr "До 5 хештеґів, об'єднаних в одну стрічку"

#: src/utils/open-compose.js:24
msgid "Looks like your browser is blocking popups."
msgstr "Схоже, ваш браузер блокує вікна, що спливають."

#: src/utils/show-compose.js:16
msgid "A draft post is currently minimized. Post or discard it before creating a new one."
msgstr "Чернетка допису наразі мінімізована. Опублікуйте або видаліть його перед створенням нового."

#: src/utils/show-compose.js:21
msgid "A post is currently open. Post or discard it before creating a new one."
msgstr "Допис наразі відкрито. Опублікуйте або видаліть його перед створенням нового."

