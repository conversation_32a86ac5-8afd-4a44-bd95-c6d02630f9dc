msgid ""
msgstr ""
"POT-Creation-Date: 2025-05-03 21:08+0800\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: ru\n"
"Project-Id-Version: phanpy\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-07-15 09:08\n"
"Last-Translator: \n"
"Language-Team: Russian\n"
"Plural-Forms: nplurals=4; plural=((n%10==1 && n%100!=11) ? 0 : ((n%10 >= 2 && n%10 <=4 && (n%100 < 12 || n%100 > 14)) ? 1 : ((n%10 == 0 || (n%10 >= 5 && n%10 <=9)) || (n%100 >= 11 && n%100 <= 14)) ? 2 : 3));\n"
"X-Crowdin-Project: phanpy\n"
"X-Crowdin-Project-ID: 703337\n"
"X-Crowdin-Language: ru\n"
"X-Crowdin-File: /main/src/locales/en.po\n"
"X-Crowdin-File-ID: 18\n"

#: src/components/account-block.jsx:137
msgid "Locked"
msgstr "Закрытый профиль"

#. placeholder {0}: shortenNumber(statusesCount)
#: src/components/account-block.jsx:143
msgid "Posts: {0}"
msgstr "Посты: {0}"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-block.jsx:148
msgid "Last posted: {0}"
msgstr "Последний пост: {0}"

#: src/components/account-block.jsx:163
#: src/components/account-info.jsx:710
msgid "Automated"
msgstr "Бот"

#: src/components/account-block.jsx:170
#: src/components/account-info.jsx:715
#: src/components/status.jsx:665
msgid "Group"
msgstr "Группа"

#: src/components/account-block.jsx:180
msgid "Mutual"
msgstr "Взаимная подписка"

#: src/components/account-block.jsx:184
#: src/components/account-info.jsx:1875
msgid "Requested"
msgstr "Запрос отправлен"

#: src/components/account-block.jsx:188
#: src/components/account-info.jsx:1866
msgid "Following"
msgstr "Подписка"

#: src/components/account-block.jsx:192
#: src/components/account-info.jsx:1180
msgid "Follows you"
msgstr "Подписан(а) на вас"

#: src/components/account-block.jsx:200
msgid "{followersCount, plural, one {# follower} other {# followers}}"
msgstr "{followersCount, plural, one {# подписчик} many {# подписчиков} other {# подписчика}}"

#: src/components/account-block.jsx:209
#: src/components/account-info.jsx:758
msgid "Verified"
msgstr "Подтверждено"

#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#: src/components/account-block.jsx:224
#: src/components/account-info.jsx:860
msgid "Joined <0>{0}</0>"
msgstr "Присоединил(ся/ась) <0>{0}</0>"

#: src/components/account-info.jsx:63
msgid "Forever"
msgstr "Навсегда"

#: src/components/account-info.jsx:403
msgid "Unable to load account."
msgstr "Не удалось загрузить учётную запись."

#: src/components/account-info.jsx:418
msgid "Go to account page"
msgstr "Перейти на страницу учётной записи"

#: src/components/account-info.jsx:447
#: src/components/account-info.jsx:780
#: src/components/account-info.jsx:810
msgid "Followers"
msgstr "Подписчик(а/ов)"

#. js-lingui-explicit-id
#: src/components/account-info.jsx:451
#: src/components/account-info.jsx:820
#: src/components/account-info.jsx:839
msgid "following.stats"
msgstr "Подпис(ки/ок)"

#: src/components/account-info.jsx:454
#: src/components/account-info.jsx:856
#: src/pages/account-statuses.jsx:487
#: src/pages/search.jsx:344
#: src/pages/search.jsx:491
msgid "Posts"
msgstr "Посты"

#: src/components/account-info.jsx:462
#: src/components/account-info.jsx:1236
#: src/components/compose.jsx:2792
#: src/components/media-alt-modal.jsx:55
#: src/components/media-modal.jsx:363
#: src/components/status.jsx:1915
#: src/components/status.jsx:1932
#: src/components/status.jsx:2057
#: src/components/status.jsx:2686
#: src/components/status.jsx:2689
#: src/pages/account-statuses.jsx:531
#: src/pages/accounts.jsx:118
#: src/pages/hashtag.jsx:203
#: src/pages/list.jsx:171
#: src/pages/public.jsx:116
#: src/pages/scheduled-posts.jsx:89
#: src/pages/status.jsx:1305
#: src/pages/trending.jsx:474
msgid "More"
msgstr "Ещё"

#: src/components/account-info.jsx:474
msgid "<0>{displayName}</0> has indicated that their new account is now:"
msgstr "<0>{displayName}</0> указал(а), что теперь использует новую учётную запись:"

#: src/components/account-info.jsx:619
#: src/components/account-info.jsx:1464
msgid "Handle copied"
msgstr "Имя пользователя скопировано"

#: src/components/account-info.jsx:622
#: src/components/account-info.jsx:1467
msgid "Unable to copy handle"
msgstr "Не удалось скопировать имя пользователя"

#: src/components/account-info.jsx:628
#: src/components/account-info.jsx:1473
msgid "Copy handle"
msgstr "Копировать имя пользователя"

#: src/components/account-info.jsx:634
msgid "Go to original profile page"
msgstr "Перейти на страницу оригинального профиля"

#: src/components/account-info.jsx:652
msgid "View profile image"
msgstr "Просмотр изображения профиля"

#: src/components/account-info.jsx:670
msgid "View profile header"
msgstr "Открыть изображение-шапку"

#: src/components/account-info.jsx:686
#: src/components/account-info.jsx:1774
#: src/components/account-info.jsx:2300
msgid "Edit profile"
msgstr "Редактировать профиль"

#: src/components/account-info.jsx:705
msgid "In Memoriam"
msgstr "Вечная память"

#: src/components/account-info.jsx:787
#: src/components/account-info.jsx:830
msgid "This user has chosen to not make this information available."
msgstr "Этот пользователь предпочёл не раскрывать эту информацию."

#. placeholder {0}: ( postingStats.originals / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {1}: ( postingStats.replies / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {2}: ( postingStats.boosts / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#: src/components/account-info.jsx:885
msgid "{0} original posts, {1} replies, {2} boosts"
msgstr "Оригинальные посты: {0}. Ответы: {1}. Продвижения: {2}"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.daysSinceLastPost
#. placeholder {2}: postingStats.daysSinceLastPost
#. placeholder {3}: postingStats.daysSinceLastPost
#. placeholder {4}: postingStats.total
#. placeholder {5}: postingStats.total
#. placeholder {6}: postingStats.daysSinceLastPost
#: src/components/account-info.jsx:901
msgid "{0, plural, one {{1, plural, one {Last 1 post in the past 1 day} other {Last 1 post in the past {2} days}}} other {{3, plural, one {Last {4} posts in the past 1 day} other {Last {5} posts in the past {6} days}}}}"
msgstr "{0, plural, one {{1, plural, one {1 последний пост за 1 последний день} other {1 последний пост за {2} последних ден(я/ей)}}} other {{3, plural, one {Последние {4} пост(а/ов) за 1 последний день} other {Последние {5} пост(а/ов) за {6} последних ден(я/ей)}}}}"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.total
#: src/components/account-info.jsx:917
msgid "{0, plural, one {Last 1 post in the past year(s)} other {Last {1} posts in the past year(s)}}"
msgstr "{0, plural, one {{1} пост} few {{1} поста} many {{1} постов} other {{1} поста}} за последний год или годы"

#: src/components/account-info.jsx:942
#: src/pages/catchup.jsx:70
msgid "Original"
msgstr "Оригинальные"

#: src/components/account-info.jsx:946
#: src/components/status.jsx:2470
#: src/pages/catchup.jsx:71
#: src/pages/catchup.jsx:1448
#: src/pages/catchup.jsx:2061
#: src/pages/status.jsx:1024
#: src/pages/status.jsx:1651
msgid "Replies"
msgstr "Ответы"

#: src/components/account-info.jsx:950
#: src/pages/catchup.jsx:72
#: src/pages/catchup.jsx:1450
#: src/pages/catchup.jsx:2073
#: src/pages/settings.jsx:1198
msgid "Boosts"
msgstr "Продвижения"

#: src/components/account-info.jsx:956
msgid "Post stats unavailable."
msgstr "Статистика публикации постов недоступна."

#: src/components/account-info.jsx:987
msgid "View post stats"
msgstr "Показать статистику публикации постов"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-info.jsx:1184
msgid "Last post: <0>{0}</0>"
msgstr "Последний пост: <0>{0}</0>"

#: src/components/account-info.jsx:1198
msgid "Muted"
msgstr "Игнорируется"

#: src/components/account-info.jsx:1203
msgid "Blocked"
msgstr "Заблокирован(а)"

#: src/components/account-info.jsx:1212
msgid "Private note"
msgstr "Личная заметка"

#: src/components/account-info.jsx:1269
msgid "Mention <0>@{username}</0>"
msgstr "Упомянуть <0>@{username}</0>"

#: src/components/account-info.jsx:1281
msgid "Translate bio"
msgstr "Перевести поле «О себе»"

#: src/components/account-info.jsx:1292
msgid "Edit private note"
msgstr "Редактировать личную заметку"

#: src/components/account-info.jsx:1292
msgid "Add private note"
msgstr "Добавить личную заметку"

#: src/components/account-info.jsx:1312
msgid "Notifications enabled for @{username}'s posts."
msgstr "Уведомления о новых постах @{username} включены."

#: src/components/account-info.jsx:1313
msgid " Notifications disabled for @{username}'s posts."
msgstr "Уведомления о новых постах @{username} отключены."

#: src/components/account-info.jsx:1325
msgid "Disable notifications"
msgstr "Отключить уведомления"

#: src/components/account-info.jsx:1326
msgid "Enable notifications"
msgstr "Включить уведомления"

#: src/components/account-info.jsx:1343
msgid "Boosts from @{username} enabled."
msgstr "Продвижения @{username} видны."

#: src/components/account-info.jsx:1344
msgid "Boosts from @{username} disabled."
msgstr "Продвижения @{username} скрыты."

#: src/components/account-info.jsx:1355
msgid "Disable boosts"
msgstr "Скрыть продвижения"

#: src/components/account-info.jsx:1355
msgid "Enable boosts"
msgstr "Включить продвижения"

#: src/components/account-info.jsx:1374
msgid "@{username} is no longer featured on your profile."
msgstr "@{username} больше не отображается в вашем профиле."

#: src/components/account-info.jsx:1384
msgid "@{username} is now featured on your profile."
msgstr "@{username} теперь отображается в вашем профиле."

#: src/components/account-info.jsx:1392
msgid "Unable to unfeature @{username} on your profile."
msgstr "Не удалось удалить @{username} из вашего профиля."

#: src/components/account-info.jsx:1396
msgid "Unable to feature @{username} on your profile."
msgstr "Не удалось отобразить @{username} в вашем профиле."

#: src/components/account-info.jsx:1405
msgid "Don't feature on profile"
msgstr "Не показывать в профиле"

#: src/components/account-info.jsx:1406
#: src/pages/hashtag.jsx:333
msgid "Feature on profile"
msgstr "Рекомендовать в профиле"

#: src/components/account-info.jsx:1415
msgid "Show featured profiles"
msgstr "Показать рекомендуемые профили"

#: src/components/account-info.jsx:1430
#: src/components/account-info.jsx:1440
#: src/components/account-info.jsx:2046
msgid "Add/Remove from Lists"
msgstr "Управление списками"

#: src/components/account-info.jsx:1490
#: src/components/status.jsx:1337
msgid "Link copied"
msgstr "Ссылка скопирована"

#: src/components/account-info.jsx:1493
#: src/components/status.jsx:1340
msgid "Unable to copy link"
msgstr "Не удалось скопировать ссылку"

#: src/components/account-info.jsx:1499
#: src/components/shortcuts-settings.jsx:1059
#: src/components/status.jsx:1346
#: src/components/status.jsx:3464
msgid "Copy"
msgstr "Копировать"

#: src/components/account-info.jsx:1514
#: src/components/shortcuts-settings.jsx:1077
#: src/components/status.jsx:1362
msgid "Sharing doesn't seem to work."
msgstr "Кажется, функция «Поделиться» не работает."

#: src/components/account-info.jsx:1520
#: src/components/status.jsx:1368
msgid "Share…"
msgstr "Поделиться…"

#: src/components/account-info.jsx:1540
msgid "Unmuted @{username}"
msgstr "@{username} убран(а) из списка игнорируемых"

#: src/components/account-info.jsx:1552
msgid "Unmute <0>@{username}</0>"
msgstr "Убрать <0>@{username}</0> из игнорируемых"

#: src/components/account-info.jsx:1568
msgid "Mute <0>@{username}</0>…"
msgstr "Игнорировать <0>@{username}</0>…"

#. placeholder {0}: typeof MUTE_DURATIONS_LABELS[duration] === 'function' ? MUTE_DURATIONS_LABELS[duration]() : _(MUTE_DURATIONS_LABELS[duration])
#: src/components/account-info.jsx:1600
msgid "Muted @{username} for {0}"
msgstr "@{username} добавлен(а) в список игнорируемых на {0}"

#: src/components/account-info.jsx:1612
msgid "Unable to mute @{username}"
msgstr "Не удалось добавить @{username} в список игнорируемых"

#: src/components/account-info.jsx:1633
msgid "Remove <0>@{username}</0> from followers?"
msgstr "Удалить <0>@{username}</0> из подписчиков?"

#: src/components/account-info.jsx:1653
msgid "@{username} removed from followers"
msgstr "@{username} убран(а) из подписчиков"

#: src/components/account-info.jsx:1665
msgid "Remove follower…"
msgstr "Убрать подписчика…"

#: src/components/account-info.jsx:1676
msgid "Block <0>@{username}</0>?"
msgstr "Заблокировать <0>@{username}</0>?"

#: src/components/account-info.jsx:1700
msgid "Unblocked @{username}"
msgstr "@{username} разблокирован(а)"

#: src/components/account-info.jsx:1708
msgid "Blocked @{username}"
msgstr "@{username} заблокирован(а)"

#: src/components/account-info.jsx:1716
msgid "Unable to unblock @{username}"
msgstr "Не удалось разблокировать @{username}"

#: src/components/account-info.jsx:1718
msgid "Unable to block @{username}"
msgstr "Не удалось заблокировать @{username}"

#: src/components/account-info.jsx:1728
msgid "Unblock <0>@{username}</0>"
msgstr "Разблокировать <0>@{username}</0>"

#: src/components/account-info.jsx:1737
msgid "Block <0>@{username}</0>…"
msgstr "Заблокировать <0>@{username}</0>…"

#: src/components/account-info.jsx:1754
msgid "Report <0>@{username}</0>…"
msgstr "Пожаловаться на <0>@{username}</0>…"

#: src/components/account-info.jsx:1810
msgid "Withdraw follow request?"
msgstr "Отозвать запрос на подписку?"

#. placeholder {0}: info.acct || info.username
#: src/components/account-info.jsx:1811
msgid "Unfollow @{0}?"
msgstr "Отписаться от @{0}?"

#: src/components/account-info.jsx:1869
msgid "Unfollow…"
msgstr "Отписаться…"

#: src/components/account-info.jsx:1878
msgid "Withdraw…"
msgstr "Отозвать…"

#: src/components/account-info.jsx:1885
#: src/components/account-info.jsx:1889
#: src/pages/hashtag.jsx:265
msgid "Follow"
msgstr "Подписаться"

#: src/components/account-info.jsx:1986
#: src/components/account-info.jsx:2041
#: src/components/account-info.jsx:2175
#: src/components/account-info.jsx:2295
#: src/components/account-sheet.jsx:38
#: src/components/compose.jsx:891
#: src/components/compose.jsx:2748
#: src/components/compose.jsx:3228
#: src/components/compose.jsx:3437
#: src/components/compose.jsx:3667
#: src/components/drafts.jsx:59
#: src/components/embed-modal.jsx:13
#: src/components/generic-accounts.jsx:151
#: src/components/keyboard-shortcuts-help.jsx:43
#: src/components/list-add-edit.jsx:37
#: src/components/media-alt-modal.jsx:43
#: src/components/media-modal.jsx:327
#: src/components/notification-service.jsx:157
#: src/components/report-modal.jsx:118
#: src/components/shortcuts-settings.jsx:230
#: src/components/shortcuts-settings.jsx:583
#: src/components/shortcuts-settings.jsx:783
#: src/components/status.jsx:3188
#: src/components/status.jsx:3428
#: src/components/status.jsx:3937
#: src/pages/accounts.jsx:45
#: src/pages/catchup.jsx:1584
#: src/pages/filters.jsx:225
#: src/pages/list.jsx:302
#: src/pages/notifications.jsx:942
#: src/pages/scheduled-posts.jsx:259
#: src/pages/settings.jsx:90
#: src/pages/status.jsx:1392
msgid "Close"
msgstr "Закрыть"

#: src/components/account-info.jsx:1991
msgid "Translated Bio"
msgstr "Перевод поля «О себе»"

#: src/components/account-info.jsx:2086
msgid "Unable to remove from list."
msgstr "Не удалось убрать из списка."

#: src/components/account-info.jsx:2087
msgid "Unable to add to list."
msgstr "Не удалось добавить в список."

#: src/components/account-info.jsx:2106
#: src/pages/lists.jsx:131
msgid "Unable to load lists."
msgstr "Не удалось загрузить списки."

#: src/components/account-info.jsx:2110
msgid "No lists."
msgstr "Списки отсутствуют."

#: src/components/account-info.jsx:2121
#: src/components/list-add-edit.jsx:41
#: src/pages/lists.jsx:62
msgid "New list"
msgstr "Новый список"

#. placeholder {0}: account?.username || account?.acct
#: src/components/account-info.jsx:2180
msgid "Private note about <0>@{0}</0>"
msgstr "Личная заметка о <0>@{0}</0>"

#: src/components/account-info.jsx:2210
msgid "Unable to update private note."
msgstr "Не удалось обновить личную заметку."

#: src/components/account-info.jsx:2233
#: src/components/account-info.jsx:2531
msgid "Cancel"
msgstr "Отмена"

#: src/components/account-info.jsx:2238
msgid "Save & close"
msgstr "Сохранить и закрыть"

#: src/components/account-info.jsx:2355
msgid "Unable to update profile."
msgstr "Не удалось обновить профиль."

#: src/components/account-info.jsx:2362
msgid "Header picture"
msgstr "Изображение заголовка"

#: src/components/account-info.jsx:2414
msgid "Profile picture"
msgstr "Изображение профиля"

#: src/components/account-info.jsx:2466
#: src/components/list-add-edit.jsx:106
msgid "Name"
msgstr "Название"

#: src/components/account-info.jsx:2479
msgid "Bio"
msgstr "О себе"

#: src/components/account-info.jsx:2492
msgid "Extra fields"
msgstr "Дополнительные поля"

#: src/components/account-info.jsx:2498
msgid "Label"
msgstr "Заголовок"

#: src/components/account-info.jsx:2501
msgid "Content"
msgstr "Содержание"

#: src/components/account-info.jsx:2534
#: src/components/list-add-edit.jsx:152
#: src/components/shortcuts-settings.jsx:715
#: src/pages/filters.jsx:570
#: src/pages/notifications.jsx:1008
msgid "Save"
msgstr "Сохранить"

#: src/components/account-info.jsx:2588
msgid "username"
msgstr "имя пользователя"

#: src/components/account-info.jsx:2592
msgid "server domain name"
msgstr "доменное имя сервера"

#. placeholder {0}: info.username
#: src/components/account-info.jsx:2658
msgid "Profiles featured by @{0}"
msgstr "Профили, которые рекомендует @{0}"

#: src/components/account-info.jsx:2684
msgid "No featured profiles."
msgstr "Нет рекомендуемых профилей."

#: src/components/background-service.jsx:158
msgid "Cloak mode disabled"
msgstr "Режим маскировки отключён"

#: src/components/background-service.jsx:158
msgid "Cloak mode enabled"
msgstr "Режим маскировки включён"

#: src/components/columns.jsx:27
#: src/components/nav-menu.jsx:181
#: src/components/shortcuts-settings.jsx:139
#: src/components/timeline.jsx:469
#: src/pages/catchup.jsx:883
#: src/pages/filters.jsx:90
#: src/pages/followed-hashtags.jsx:41
#: src/pages/home.jsx:54
#: src/pages/notifications.jsx:587
#: src/pages/scheduled-posts.jsx:74
msgid "Home"
msgstr "Домашняя"

#: src/components/compose-button.jsx:150
#: src/compose.jsx:38
msgid "Compose"
msgstr "Написать"

#: src/components/compose-button.jsx:177
#: src/components/nav-menu.jsx:265
#: src/pages/scheduled-posts.jsx:31
#: src/pages/scheduled-posts.jsx:78
msgid "Scheduled Posts"
msgstr "Отложенные посты"

#: src/components/compose-button.jsx:190
msgid "Add to thread"
msgstr "Добавить в обсуждение"

#: src/components/compose.jsx:211
msgid "Take photo or video"
msgstr "Сделать фото или видео"

#: src/components/compose.jsx:212
msgid "Add media"
msgstr "Добавить медиафайл"

#: src/components/compose.jsx:213
msgid "Add custom emoji"
msgstr "Добавить пользовательские эмодзи"

#: src/components/compose.jsx:214
msgid "Add GIF"
msgstr "Добавить GIF"

#: src/components/compose.jsx:215
msgid "Add poll"
msgstr "Добавить голосование"

#: src/components/compose.jsx:216
msgid "Schedule post"
msgstr "Запланировать пост"

#: src/components/compose.jsx:416
msgid "You have unsaved changes. Discard this post?"
msgstr "У вас есть несохраненные изменения. Отменить этот пост?"

#. placeholder {0}: unsupportedFiles.length
#. placeholder {1}: unsupportedFiles[0].name
#. placeholder {2}: lf.format( unsupportedFiles.map((f) => f.name), )
#: src/components/compose.jsx:654
msgid "{0, plural, one {File {1} is not supported.} other {Files {2} are not supported.}}"
msgstr "{0, plural, one {Файл {1} не поддерживается.} other {Файлы {2} не поддерживаются.}}"

#: src/components/compose.jsx:664
#: src/components/compose.jsx:682
#: src/components/compose.jsx:1794
#: src/components/compose.jsx:1919
msgid "{maxMediaAttachments, plural, one {You can only attach up to 1 file.} other {You can only attach up to # files.}}"
msgstr "{maxMediaAttachments, plural, one {Вы можете прикрепить только 1 файл.} other {Вы можете прикрепить до # файлов.}}"

#: src/components/compose.jsx:872
msgid "Pop out"
msgstr "Открыть в отдельном окне"

#: src/components/compose.jsx:879
msgid "Minimize"
msgstr "Свернуть"

#: src/components/compose.jsx:915
msgid "Looks like you closed the parent window."
msgstr "Похоже, вы закрыли родительское окно."

#: src/components/compose.jsx:922
msgid "Looks like you already have a compose field open in the parent window and currently publishing. Please wait for it to be done and try again later."
msgstr "Похоже, вы уже публикуете пост в родительском окне. Дождитесь публикации и попробуйте снова."

#: src/components/compose.jsx:927
msgid "Looks like you already have a compose field open in the parent window. Popping in this window will discard the changes you made in the parent window. Continue?"
msgstr "Похоже, вы уже пишите или редактируете пост в родительском окне. Возвращение этого окна отменит изменения в родительском окне. Продолжить?"

#: src/components/compose.jsx:970
msgid "Pop in"
msgstr "Вернуть в родительское окно"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#. placeholder {1}: rtf.format(-replyToStatusMonthsAgo, 'month')
#: src/components/compose.jsx:980
msgid "Replying to @{0}’s post (<0>{1}</0>)"
msgstr "Отвечаем пост @{0}(<0>{1}</0>)"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#: src/components/compose.jsx:990
msgid "Replying to @{0}’s post"
msgstr "Отвечаем на пост @{0}"

#: src/components/compose.jsx:1003
msgid "Editing source post"
msgstr "Редактирование исходного поста"

#: src/components/compose.jsx:1056
msgid "Poll must have at least 2 options"
msgstr "Опрос должен иметь не менее 2 вариантов ответа"

#: src/components/compose.jsx:1060
msgid "Some poll choices are empty"
msgstr "Некоторые варианты ответа пусты"

#: src/components/compose.jsx:1073
msgid "Some media have no descriptions. Continue?"
msgstr "У некоторых медиафайлов нет описаний. Продолжить?"

#: src/components/compose.jsx:1125
msgid "Attachment #{i} failed"
msgstr "Не удалось прикрепить вложение №{i}"

#: src/components/compose.jsx:1221
#: src/components/status.jsx:2245
#: src/components/timeline.jsx:1023
msgid "Content warning"
msgstr "Предупреждение о содержании"

#: src/components/compose.jsx:1237
msgid "Content warning or sensitive media"
msgstr "Предупреждение о содержании или медиафайлах деликатного характера"

#: src/components/compose.jsx:1273
#: src/components/status.jsx:99
#: src/pages/settings.jsx:318
msgid "Public"
msgstr "Публичный"

#: src/components/compose.jsx:1278
#: src/components/nav-menu.jsx:349
#: src/components/shortcuts-settings.jsx:165
#: src/components/status.jsx:100
msgid "Local"
msgstr "Локальная"

#: src/components/compose.jsx:1282
#: src/components/status.jsx:101
#: src/pages/settings.jsx:321
msgid "Unlisted"
msgstr "Без включения в поиск"

#: src/components/compose.jsx:1285
#: src/components/status.jsx:102
#: src/pages/settings.jsx:324
msgid "Followers only"
msgstr "Только для подписчиков"

#: src/components/compose.jsx:1288
#: src/components/status.jsx:103
#: src/components/status.jsx:2121
msgid "Private mention"
msgstr "Личное упоминание"

#: src/components/compose.jsx:1297
msgid "Post your reply"
msgstr "Опубликовать ответ"

#: src/components/compose.jsx:1299
msgid "Edit your post"
msgstr "Редактировать свой пост"

#: src/components/compose.jsx:1300
msgid "What are you doing?"
msgstr "Начните писать свои мысли"

#: src/components/compose.jsx:1379
msgid "Mark media as sensitive"
msgstr "Отметить медиафайл как деликатный"

#: src/components/compose.jsx:1416
msgid "Posting on <0/>"
msgstr "Публикация в <0/>"

#: src/components/compose.jsx:1447
#: src/components/compose.jsx:3286
#: src/components/shortcuts-settings.jsx:715
#: src/pages/list.jsx:388
msgid "Add"
msgstr "Добавить"

#: src/components/compose.jsx:1675
msgid "Schedule"
msgstr "Запланировать"

#: src/components/compose.jsx:1677
#: src/components/keyboard-shortcuts-help.jsx:155
#: src/components/status.jsx:1109
#: src/components/status.jsx:1895
#: src/components/status.jsx:1896
#: src/components/status.jsx:2590
msgid "Reply"
msgstr "Ответить"

#: src/components/compose.jsx:1679
msgid "Update"
msgstr "Сохранить"

#: src/components/compose.jsx:1680
msgctxt "Submit button in composer"
msgid "Post"
msgstr "Опубликовать"

#: src/components/compose.jsx:1806
msgid "Downloading GIF…"
msgstr "Загружаем GIF-анимацию…"

#: src/components/compose.jsx:1834
msgid "Failed to download GIF"
msgstr "Не удалось загрузить GIF-анимацию"

#: src/components/compose.jsx:2049
#: src/components/compose.jsx:2126
#: src/components/nav-menu.jsx:244
msgid "More…"
msgstr "Ещё…"

#: src/components/compose.jsx:2562
msgid "Uploaded"
msgstr "Загружено"

#: src/components/compose.jsx:2575
msgid "Image description"
msgstr "Описание изображения"

#: src/components/compose.jsx:2576
msgid "Video description"
msgstr "Описание видео"

#: src/components/compose.jsx:2577
msgid "Audio description"
msgstr "Описание аудио"

#. placeholder {0}: prettyBytes( imageSize, )
#. placeholder {0}: prettyBytes( videoSize, )
#. placeholder {1}: prettyBytes(imageSizeLimit)
#. placeholder {1}: prettyBytes(videoSizeLimit)
#: src/components/compose.jsx:2612
#: src/components/compose.jsx:2632
msgid "File size too large. Uploading might encounter issues. Try reduce the file size from {0} to {1} or lower."
msgstr "Файл слишком большой — при загрузке могут возникнуть проблемы. Попробуйте уменьшить размер с {0} до {1} или меньше."

#. placeholder {0}: i18n.number( width, )
#. placeholder {1}: i18n.number(height)
#. placeholder {2}: i18n.number(newWidth)
#. placeholder {3}: i18n.number( newHeight, )
#: src/components/compose.jsx:2624
#: src/components/compose.jsx:2644
msgid "Dimension too large. Uploading might encounter issues. Try reduce dimension from {0}×{1}px to {2}×{3}px."
msgstr "Разрешение слишком большое. При загрузке могут возникнуть проблемы. Попробуйте уменьшить разрешение с {0}×{1}px to {2}×{3}px."

#: src/components/compose.jsx:2652
msgid "Frame rate too high. Uploading might encounter issues."
msgstr "Слишком высокая частота кадров — при загрузке могут возникнуть проблемы."

#: src/components/compose.jsx:2712
#: src/components/compose.jsx:2962
#: src/components/shortcuts-settings.jsx:726
#: src/pages/catchup.jsx:1081
#: src/pages/filters.jsx:413
msgid "Remove"
msgstr "Убрать"

#: src/components/compose.jsx:2729
#: src/compose.jsx:84
msgid "Error"
msgstr "Ошибка"

#: src/components/compose.jsx:2754
msgid "Edit image description"
msgstr "Редактировать описание изображения"

#: src/components/compose.jsx:2755
msgid "Edit video description"
msgstr "Редактировать описание видео"

#: src/components/compose.jsx:2756
msgid "Edit audio description"
msgstr "Редактировать описание аудио"

#: src/components/compose.jsx:2801
#: src/components/compose.jsx:2850
msgid "Generating description. Please wait…"
msgstr "Создаём описание. Один момент, пожалуйста…"

#. placeholder {0}: e.message
#: src/components/compose.jsx:2821
msgid "Failed to generate description: {0}"
msgstr "Не удалось сгенерировать описание: {0}"

#: src/components/compose.jsx:2822
msgid "Failed to generate description"
msgstr "Не удалось создать описание"

#: src/components/compose.jsx:2834
#: src/components/compose.jsx:2840
#: src/components/compose.jsx:2886
msgid "Generate description…"
msgstr "Автоматическое описание…"

#. placeholder {0}: e?.message ? `: ${e.message}` : ''
#: src/components/compose.jsx:2873
msgid "Failed to generate description{0}"
msgstr "Не удалось сгенерировать описание: {0}"

#. placeholder {0}: localeCode2Text(lang)
#: src/components/compose.jsx:2888
msgid "({0}) <0>— experimental</0>"
msgstr "({0}) <0>— экспериментальная функция</0>"

#: src/components/compose.jsx:2907
msgid "Done"
msgstr "Готово"

#. placeholder {0}: i + 1
#: src/components/compose.jsx:2943
msgid "Choice {0}"
msgstr "Выбор {0}"

#: src/components/compose.jsx:2990
msgid "Multiple choices"
msgstr "Разрешить несколько ответов"

#: src/components/compose.jsx:2993
msgid "Duration"
msgstr "Продолжительность"

#: src/components/compose.jsx:3024
msgid "Remove poll"
msgstr "Убрать опрос"

#: src/components/compose.jsx:3245
msgid "Search accounts"
msgstr "Поиск пользователей"

#: src/components/compose.jsx:3299
#: src/components/generic-accounts.jsx:236
msgid "Error loading accounts"
msgstr "Ошибка поиска пользователей"

#: src/components/compose.jsx:3443
msgid "Custom emojis"
msgstr "Пользовательские эмодзи"

#: src/components/compose.jsx:3463
msgid "Search emoji"
msgstr "Поиск эмодзи"

#: src/components/compose.jsx:3494
msgid "Error loading custom emojis"
msgstr "Ошибка загрузки пользовательских эмодзи"

#: src/components/compose.jsx:3505
msgid "Recently used"
msgstr "Недавно использованные"

#: src/components/compose.jsx:3506
msgid "Others"
msgstr "Прочие"

#. placeholder {0}: i18n.number(emojis.length - max)
#: src/components/compose.jsx:3544
msgid "{0} more…"
msgstr "Ещё {0}…"

#: src/components/compose.jsx:3682
msgid "Search GIFs"
msgstr "Поиск GIF"

#: src/components/compose.jsx:3697
msgid "Powered by GIPHY"
msgstr "На основе GIPHY"

#: src/components/compose.jsx:3705
msgid "Type to search GIFs"
msgstr "Начните набирать для поиска GIF-анимаций"

#: src/components/compose.jsx:3803
#: src/components/media-modal.jsx:469
#: src/components/timeline.jsx:927
msgid "Previous"
msgstr "Назад"

#: src/components/compose.jsx:3821
#: src/components/media-modal.jsx:488
#: src/components/timeline.jsx:944
msgid "Next"
msgstr "Вперёд"

#: src/components/compose.jsx:3838
msgid "Error loading GIFs"
msgstr "Ошибка поиска GIF-анимаций"

#: src/components/drafts.jsx:64
#: src/pages/settings.jsx:702
msgid "Unsent drafts"
msgstr "Черновики"

#: src/components/drafts.jsx:69
msgid "Looks like you have unsent drafts. Let's continue where you left off."
msgstr "У вас есть незаконченные черновики. Давайте продолжим с места, где вы остановились."

#: src/components/drafts.jsx:103
msgid "Delete this draft?"
msgstr "Удалить этот черновик?"

#: src/components/drafts.jsx:118
msgid "Error deleting draft! Please try again."
msgstr "Не удалось удалить черновик. Попробуйте снова, пожалуйста."

#: src/components/drafts.jsx:128
#: src/components/list-add-edit.jsx:188
#: src/components/status.jsx:1512
#: src/pages/filters.jsx:603
#: src/pages/scheduled-posts.jsx:369
msgid "Delete…"
msgstr "Удалить…"

#: src/components/drafts.jsx:147
msgid "Error fetching reply-to status!"
msgstr "Не удалось найти пост, к которому относится этот ответ."

#: src/components/drafts.jsx:172
msgid "Delete all drafts?"
msgstr "Очистить черновики?"

#: src/components/drafts.jsx:190
msgid "Error deleting drafts! Please try again."
msgstr "Не удалось очистить черновики. Попробуйте снова, пожалуйста."

#: src/components/drafts.jsx:202
msgid "Delete all…"
msgstr "Удалить все…"

#: src/components/drafts.jsx:210
msgid "No drafts found."
msgstr "У вас пока нет черновиков."

#: src/components/drafts.jsx:247
#: src/pages/catchup.jsx:1932
msgid "Poll"
msgstr "Опрос"

#: src/components/drafts.jsx:250
#: src/pages/account-statuses.jsx:367
msgid "Media"
msgstr "Медиафайлы"

#: src/components/embed-modal.jsx:18
msgid "Open in new window"
msgstr "Открыть в новом окне"

#: src/components/follow-request-buttons.jsx:43
#: src/pages/notifications.jsx:992
msgid "Accept"
msgstr "Принять"

#: src/components/follow-request-buttons.jsx:69
msgid "Reject"
msgstr "Отклонить"

#: src/components/follow-request-buttons.jsx:76
#: src/pages/notifications.jsx:1276
msgid "Accepted"
msgstr "Принят"

#: src/components/follow-request-buttons.jsx:80
msgid "Rejected"
msgstr "Отклонён"

#: src/components/generic-accounts.jsx:154
#: src/components/notification.jsx:455
#: src/pages/accounts.jsx:50
#: src/pages/search.jsx:334
#: src/pages/search.jsx:367
msgid "Accounts"
msgstr "Учётные записи"

#: src/components/generic-accounts.jsx:214
#: src/components/timeline.jsx:551
#: src/pages/list.jsx:321
#: src/pages/notifications.jsx:922
#: src/pages/search.jsx:561
#: src/pages/status.jsx:1425
msgid "Show more…"
msgstr "Показать ещё…"

#: src/components/generic-accounts.jsx:219
#: src/components/timeline.jsx:556
#: src/pages/search.jsx:566
msgid "The end."
msgstr "Конец."

#: src/components/generic-accounts.jsx:240
msgid "Nothing to show"
msgstr "Нечего отображать"

#: src/components/keyboard-shortcuts-help.jsx:47
#: src/components/nav-menu.jsx:368
#: src/pages/catchup.jsx:1622
msgid "Keyboard shortcuts"
msgstr "Горячие клавиши"

#: src/components/keyboard-shortcuts-help.jsx:55
msgid "Keyboard shortcuts help"
msgstr "Справка по горячим клавишам"

#: src/components/keyboard-shortcuts-help.jsx:59
#: src/pages/catchup.jsx:1647
msgid "Next post"
msgstr "Следующий пост"

#: src/components/keyboard-shortcuts-help.jsx:63
#: src/pages/catchup.jsx:1655
msgid "Previous post"
msgstr "Предыдущий пост"

#: src/components/keyboard-shortcuts-help.jsx:67
msgid "Skip carousel to next post"
msgstr "Перейти к следующему посту в карусели"

#: src/components/keyboard-shortcuts-help.jsx:69
msgid "<0>Shift</0> + <1>j</1>"
msgstr "<0>Shift</0> + <1>j</1>"

#: src/components/keyboard-shortcuts-help.jsx:75
msgid "Skip carousel to previous post"
msgstr "Перейти к предыдущему посту в карусели"

#: src/components/keyboard-shortcuts-help.jsx:77
msgid "<0>Shift</0> + <1>k</1>"
msgstr "<0>Shift</0> + <1>k</1>"

#: src/components/keyboard-shortcuts-help.jsx:83
msgid "Load new posts"
msgstr "Обновить ленту"

#: src/components/keyboard-shortcuts-help.jsx:87
#: src/pages/catchup.jsx:1679
msgid "Open post details"
msgstr "Раскрыть пост"

#: src/components/keyboard-shortcuts-help.jsx:89
msgid "<0>Enter</0> or <1>o</1>"
msgstr "<0>Enter</0> или <1>o</1>"

#: src/components/keyboard-shortcuts-help.jsx:96
msgid "Expand content warning or<0/>toggle expanded/collapsed thread"
msgstr "Развернуть предупреждение о содержимом или<0/>свернуть/развернуть обсуждение"

#: src/components/keyboard-shortcuts-help.jsx:105
msgid "Close post or dialogs"
msgstr "Закрыть пост или диалоги"

#: src/components/keyboard-shortcuts-help.jsx:107
msgid "<0>Esc</0> or <1>Backspace</1>"
msgstr "<0>Esc</0> или <1>Backspace</1>"

#: src/components/keyboard-shortcuts-help.jsx:113
msgid "Focus column in multi-column mode"
msgstr "Фокус столбца в многоколоночном режиме"

#: src/components/keyboard-shortcuts-help.jsx:115
msgid "<0>1</0> to <1>9</1>"
msgstr "<0>1</0> по <1>9</1>"

#: src/components/keyboard-shortcuts-help.jsx:121
msgid "Focus next column in multi-column mode"
msgstr "Перейти к следующему столбцу в многоколонном режиме"

#: src/components/keyboard-shortcuts-help.jsx:125
msgid "Focus previous column in multi-column mode"
msgstr "Перейти к предыдущему столбцу в многоколонном режиме"

#: src/components/keyboard-shortcuts-help.jsx:129
msgid "Compose new post"
msgstr "Новый пост"

#: src/components/keyboard-shortcuts-help.jsx:133
msgid "Compose new post (new window)"
msgstr "Создать новый пост (новое окно)"

#: src/components/keyboard-shortcuts-help.jsx:136
msgid "<0>Shift</0> + <1>c</1>"
msgstr "<0>Shift</0> + <1>c</1>"

#: src/components/keyboard-shortcuts-help.jsx:142
msgid "Send post"
msgstr "Отправить пост"

#: src/components/keyboard-shortcuts-help.jsx:144
msgid "<0>Ctrl</0> + <1>Enter</1> or <2>⌘</2> + <3>Enter</3>"
msgstr "<0>Ctrl</0> + <1>Enter</1> или <2>⌘</2> + <3>Enter</3>"

#: src/components/keyboard-shortcuts-help.jsx:151
#: src/components/nav-menu.jsx:337
#: src/components/search-form.jsx:73
#: src/components/shortcuts-settings.jsx:52
#: src/components/shortcuts-settings.jsx:179
#: src/pages/search.jsx:46
#: src/pages/search.jsx:316
msgid "Search"
msgstr "Поиск"

#: src/components/keyboard-shortcuts-help.jsx:159
msgid "Reply (new window)"
msgstr "Ответить (в новом окне)"

#: src/components/keyboard-shortcuts-help.jsx:162
msgid "<0>Shift</0> + <1>r</1>"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:168
msgid "Like (favourite)"
msgstr "Нравится (избранное)"

#: src/components/keyboard-shortcuts-help.jsx:170
msgid "<0>l</0> or <1>f</1>"
msgstr "<0>l</0> или <1>f</1>"

#: src/components/keyboard-shortcuts-help.jsx:176
#: src/components/status.jsx:1117
#: src/components/status.jsx:2617
#: src/components/status.jsx:2640
#: src/components/status.jsx:2641
msgid "Boost"
msgstr "Продвинуть"

#: src/components/keyboard-shortcuts-help.jsx:178
msgid "<0>Shift</0> + <1>b</1>"
msgstr "<0>Shift</0> + <1>b</1>"

#: src/components/keyboard-shortcuts-help.jsx:184
#: src/components/status.jsx:1180
#: src/components/status.jsx:2665
#: src/components/status.jsx:2666
msgid "Bookmark"
msgstr "Закладка"

#: src/components/keyboard-shortcuts-help.jsx:188
msgid "Toggle Cloak mode"
msgstr "Включить \"приватный\" режим"

#: src/components/keyboard-shortcuts-help.jsx:190
msgid "<0>Shift</0> + <1>Alt</1> + <2>k</2>"
msgstr "<0>Shift</0> + <1>Alt</1> + <2>k</2>"

#: src/components/list-add-edit.jsx:41
msgid "Edit list"
msgstr "Редактировать список"

#: src/components/list-add-edit.jsx:97
msgid "Unable to edit list."
msgstr "Не удалось изменить список."

#: src/components/list-add-edit.jsx:98
msgid "Unable to create list."
msgstr "Не удалось создать список."

#: src/components/list-add-edit.jsx:126
msgid "Show replies to list members"
msgstr "Показать ответы участникам списка"

#: src/components/list-add-edit.jsx:129
msgid "Show replies to people I follow"
msgstr "Показать ответы людям, на которых я подписан(а)"

#: src/components/list-add-edit.jsx:132
msgid "Don't show replies"
msgstr "Не показывать ответы"

#: src/components/list-add-edit.jsx:146
msgid "Hide posts on this list from Home/Following"
msgstr "Скрыть посты этого списка из Домашней ленты/Подписок"

#: src/components/list-add-edit.jsx:152
#: src/pages/filters.jsx:570
msgid "Create"
msgstr "Создать"

#: src/components/list-add-edit.jsx:159
msgid "Delete this list?"
msgstr "Удалить этот список?"

#: src/components/list-add-edit.jsx:178
msgid "Unable to delete list."
msgstr "Не удалось удалить список."

#: src/components/list-exclusive-badge.jsx:14
#: src/pages/list.jsx:179
#: src/pages/lists.jsx:107
msgid "Posts on this list are hidden from Home/Following"
msgstr "Записи в этом списке скрыты из Домашней/Подписок"

#: src/components/media-alt-modal.jsx:48
#: src/components/media.jsx:51
msgid "Media description"
msgstr "Описание медиа"

#: src/components/media-alt-modal.jsx:67
#: src/components/status.jsx:1223
#: src/components/status.jsx:1232
#: src/components/translation-block.jsx:239
msgid "Translate"
msgstr "Перевести"

#: src/components/media-alt-modal.jsx:78
#: src/components/status.jsx:1251
msgid "Speak"
msgstr "Произнести"

#: src/components/media-modal.jsx:374
msgid "Open original media in new window"
msgstr "Открыть исходный файл в новом окне"

#: src/components/media-modal.jsx:378
msgid "Open original media"
msgstr "Открыть исходный файл"

#: src/components/media-modal.jsx:394
msgid "Attempting to describe image. Please wait…"
msgstr "Попытка описать изображение. Пожалуйста, подождите…"

#: src/components/media-modal.jsx:409
msgid "Failed to describe image"
msgstr "Не удалось описать изображение"

#: src/components/media-modal.jsx:419
msgid "Describe image…"
msgstr "Опишите изображение…"

#: src/components/media-modal.jsx:443
msgid "View post"
msgstr "Просмотреть публикацию"

#: src/components/media-post.jsx:127
msgid "Sensitive media"
msgstr "Деликатный контент"

#: src/components/media-post.jsx:132
msgid "Filtered: {filterTitleStr}"
msgstr "Отфильтровано: {filterTitleStr}"

#: src/components/media-post.jsx:133
#: src/components/status.jsx:3767
#: src/components/status.jsx:3863
#: src/components/status.jsx:3941
#: src/components/timeline.jsx:1012
#: src/pages/catchup.jsx:75
#: src/pages/catchup.jsx:1880
msgid "Filtered"
msgstr "Отфильтровано"

#: src/components/media.jsx:477
msgid "Open file"
msgstr "Открыть файл"

#: src/components/modals.jsx:75
msgid "Post scheduled"
msgstr "Пост запланирован"

#: src/components/modals.jsx:76
msgid "Post published. Check it out."
msgstr "Пост опубликован. Проверьте его."

#: src/components/modals.jsx:78
msgid "Reply scheduled"
msgstr "Ответ запланирован"

#: src/components/modals.jsx:79
msgid "Reply posted. Check it out."
msgstr "Ответ опубликован. Проверьте его."

#: src/components/modals.jsx:80
msgid "Post updated. Check it out."
msgstr "Пост обновлен. Проверьте его."

#: src/components/nav-menu.jsx:123
msgid "Menu"
msgstr "Меню"

#: src/components/nav-menu.jsx:159
msgid "Reload page now to update?"
msgstr "Перезагрузить страницу для обновления?"

#: src/components/nav-menu.jsx:171
msgid "New update available…"
msgstr "Доступно обновление…"

#. js-lingui-explicit-id
#: src/components/nav-menu.jsx:190
#: src/components/shortcuts-settings.jsx:140
#: src/pages/following.jsx:23
#: src/pages/following.jsx:145
msgid "following.title"
msgstr "Подписки"

#: src/components/nav-menu.jsx:197
#: src/pages/catchup.jsx:878
msgid "Catch-up"
msgstr "Catch-up"

#: src/components/nav-menu.jsx:204
#: src/components/shortcuts-settings.jsx:58
#: src/components/shortcuts-settings.jsx:146
#: src/pages/home.jsx:240
#: src/pages/mentions.jsx:21
#: src/pages/mentions.jsx:174
#: src/pages/settings.jsx:1190
#: src/pages/trending.jsx:384
msgid "Mentions"
msgstr "Упоминания"

#: src/components/nav-menu.jsx:211
#: src/components/shortcuts-settings.jsx:49
#: src/components/shortcuts-settings.jsx:152
#: src/pages/filters.jsx:24
#: src/pages/home.jsx:86
#: src/pages/home.jsx:198
#: src/pages/notifications.jsx:117
#: src/pages/notifications.jsx:591
msgid "Notifications"
msgstr "Уведомления"

#: src/components/nav-menu.jsx:214
msgid "New"
msgstr "Новые"

#: src/components/nav-menu.jsx:225
msgid "Profile"
msgstr "Профиль"

#: src/components/nav-menu.jsx:233
#: src/components/shortcuts-settings.jsx:54
#: src/components/shortcuts-settings.jsx:195
#: src/pages/bookmarks.jsx:12
#: src/pages/bookmarks.jsx:26
msgid "Bookmarks"
msgstr "Закладки"

#: src/components/nav-menu.jsx:253
#: src/components/shortcuts-settings.jsx:55
#: src/components/shortcuts-settings.jsx:201
#: src/pages/catchup.jsx:1449
#: src/pages/catchup.jsx:2067
#: src/pages/favourites.jsx:12
#: src/pages/favourites.jsx:26
#: src/pages/settings.jsx:1194
msgid "Likes"
msgstr "Отметки \"нравится\""

#: src/components/nav-menu.jsx:259
#: src/pages/followed-hashtags.jsx:15
#: src/pages/followed-hashtags.jsx:45
msgid "Followed Hashtags"
msgstr "Хэштеги (подписки)"

#: src/components/nav-menu.jsx:273
#: src/pages/account-statuses.jsx:333
#: src/pages/filters.jsx:55
#: src/pages/filters.jsx:94
#: src/pages/hashtag.jsx:344
msgid "Filters"
msgstr "Фильтры"

#: src/components/nav-menu.jsx:281
msgid "Muted users"
msgstr "Игнорируемые пользователи"

#: src/components/nav-menu.jsx:289
msgid "Muted users…"
msgstr "Игнорируемые пользователи…"

#: src/components/nav-menu.jsx:296
msgid "Blocked users"
msgstr "Заблокированные пользователи"

#: src/components/nav-menu.jsx:304
msgid "Blocked users…"
msgstr "Заблокированные пользователи…"

#: src/components/nav-menu.jsx:316
msgid "Accounts…"
msgstr "Учётные записи…"

#: src/components/nav-menu.jsx:326
#: src/pages/login.jsx:32
#: src/pages/login.jsx:199
#: src/pages/status.jsx:924
#: src/pages/welcome.jsx:65
msgid "Log in"
msgstr "Войти"

#: src/components/nav-menu.jsx:343
#: src/components/shortcuts-settings.jsx:57
#: src/components/shortcuts-settings.jsx:172
#: src/pages/trending.jsx:444
msgid "Trending"
msgstr "В тренде"

#: src/components/nav-menu.jsx:355
#: src/components/shortcuts-settings.jsx:165
msgid "Federated"
msgstr "Федеративная"

#: src/components/nav-menu.jsx:378
msgid "Shortcuts / Columns…"
msgstr "Ярлыки / Столбцы…"

#: src/components/nav-menu.jsx:388
#: src/components/nav-menu.jsx:402
msgid "Settings…"
msgstr "Параметры…"

#: src/components/nav-menu.jsx:432
#: src/components/nav-menu.jsx:467
#: src/components/shortcuts-settings.jsx:50
#: src/components/shortcuts-settings.jsx:158
#: src/pages/list.jsx:127
#: src/pages/lists.jsx:18
#: src/pages/lists.jsx:54
msgid "Lists"
msgstr "Списки"

#: src/components/nav-menu.jsx:440
#: src/components/shortcuts.jsx:241
#: src/pages/list.jsx:139
msgid "All Lists"
msgstr "Все списки"

#: src/components/notification-service.jsx:161
msgid "Notification"
msgstr "Уведомление"

#: src/components/notification-service.jsx:167
msgid "This notification is from your other account."
msgstr "Это уведомление от вашей другой учетной записи."

#: src/components/notification-service.jsx:196
msgid "View all notifications"
msgstr "Просмотреть все уведомления"

#: src/components/notification.jsx:71
msgid "{account} reacted to your post with {emojiObject}"
msgstr "{account} отреагировал(а) на ваш пост при помощи {emojiObject}"

#: src/components/notification.jsx:78
msgid "{account} published a post."
msgstr "{account} опубликовал(а) пост."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:86
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted your reply.} other {{account} boosted your post.}}} other {{account} boosted {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted your reply.} other {<2><3>{1}</3> people</2> boosted your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} продвинул(а) ваш ответ.} other {{account} продвинул(а) ваш пост.}}} other {{account} продвинул(а) {postsCount} ваших пост(а/ов).}}} other {{postType, select, reply {<0><1>{0}</1> человек</0> продвинули ваш ответ.} other {<2><3>{1}</3> человек</2> продвинули ваш пост.}}}}"

#. placeholder {0}: shortenNumber(count)
#: src/components/notification.jsx:129
msgid "{count, plural, =1 {{account} followed you.} other {<0><1>{0}</1> people</0> followed you.}}"
msgstr "{count, plural, =1 {{account} подписал(ся/ась) на вас.} other {<0><1>{0}</1> человек(а)</0> подписалось на вас.}}"

#: src/components/notification.jsx:143
msgid "{account} requested to follow you."
msgstr "{account} запросил(а) разрешение на подписку."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:152
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} liked your reply.} other {{account} liked your post.}}} other {{account} liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> liked your reply.} other {<2><3>{1}</3> people</2> liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} понравился ваш ответ.} other {{account} понравился ваш пост.}}} other {{account} понравилось {postsCount} ваших пост(а/ов).}}} other {{postType, select, reply {<0><1>{0}</1> человекам</0> понравился ваш ответ.} other {<2><3>{1}</3> человекам</2> понравился ваш пост.}}}}"

#: src/components/notification.jsx:194
msgid "A poll you have voted in or created has ended."
msgstr "Опрос, который вы создавали или в котором принимали участие, был завершён."

#: src/components/notification.jsx:195
msgid "A poll you have created has ended."
msgstr "Созданный вами опрос завершился."

#: src/components/notification.jsx:196
msgid "A poll you have voted in has ended."
msgstr "Опрос, в котором вы проголосовали, завершен."

#: src/components/notification.jsx:197
msgid "A post you interacted with has been edited."
msgstr "Пост, с которым вы ранее взаимодействовали, был отредактирован."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:205
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted & liked your reply.} other {{account} boosted & liked your post.}}} other {{account} boosted & liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted & liked your reply.} other {<2><3>{1}</3> people</2> boosted & liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} продвинул(а) ваш ответ и поставил(а) ему отметку \"нравится\".} other {{account} продвинул(а) ваш пост и поставил(а) ему отметку \"нравится\".}}} other {{account} продвинул(а) и поставил(а) отметку \"нравится\" {postsCount} вашим постам.}}} other {{postType, select, reply {<0><1>{0}</1> человек(а)</0> продвинули ваш ответ и поставили ему отметку \"нравится\".} other {<2><3>{1}</3> человек(а)</2> продвинули ваш пост и поставили ему отметку \"нравится\".}}}}"

#: src/components/notification.jsx:247
msgid "{account} signed up."
msgstr "{account} зарегистрировал(ся/ась)."

#: src/components/notification.jsx:249
msgid "{account} reported {targetAccount}"
msgstr "{account} пожаловался на {targetAccount}"

#: src/components/notification.jsx:254
msgid "Lost connections with <0>{name}</0>."
msgstr "Потеряны соединения с <0>{name}</0>."

#: src/components/notification.jsx:260
msgid "Moderation warning"
msgstr "Предупреждение о модерации"

#: src/components/notification.jsx:265
msgid "Your {year} #Wrapstodon is here!"
msgstr "Ваш {year} #Warpstodon уже здесь!"

#: src/components/notification.jsx:271
msgid "An admin from <0>{from}</0> has suspended <1>{targetName}</1>, which means you can no longer receive updates from them or interact with them."
msgstr "Администратор <0>{from}</0> приостановил учётную запись <1>{targetName}</1>, что означает, что вы больше не можете получать обновления или взаимодействовать с этой учётной записи."

#: src/components/notification.jsx:277
msgid "An admin from <0>{from}</0> has blocked <1>{targetName}</1>. Affected followers: {followersCount}, followings: {followingCount}."
msgstr "Администратор <0>{from}</0> заблокировал <1>{targetName}</1>. Затронуло подписок: {followersCount}, подписок всего: {followingCount}."

#: src/components/notification.jsx:283
msgid "You have blocked <0>{targetName}</0>. Removed followers: {followersCount}, followings: {followingCount}."
msgstr "Вы заблокировали <0>{targetName}</0>. Удалено {followersCount} подпис(ок/ки), подписок всего: {followingCount}."

#: src/components/notification.jsx:291
msgid "Your account has received a moderation warning."
msgstr "Ваша учетная запись получила предупреждение о модерации."

#: src/components/notification.jsx:292
msgid "Your account has been disabled."
msgstr "Ваша учётная запись была отключена."

#: src/components/notification.jsx:293
msgid "Some of your posts have been marked as sensitive."
msgstr "Некоторые из ваших сообщений были отмечены как деликатные."

#: src/components/notification.jsx:294
msgid "Some of your posts have been deleted."
msgstr "Некоторые из ваших записей были удалены."

#: src/components/notification.jsx:295
msgid "Your posts will be marked as sensitive from now on."
msgstr "С этого момента ваши сообщения будут помечены как деликатные."

#: src/components/notification.jsx:296
msgid "Your account has been limited."
msgstr "Ваша учётная запись была ограничена."

#: src/components/notification.jsx:297
msgid "Your account has been suspended."
msgstr "Действие вашей учётной записи приостановлено."

#: src/components/notification.jsx:374
msgid "[Unknown notification type: {type}]"
msgstr "[Неизвестный тип уведомления: {type}]"

#: src/components/notification.jsx:451
#: src/components/status.jsx:1194
#: src/components/status.jsx:1204
msgid "Boosted/Liked by…"
msgstr "Продвинули/Отметили как \"нравится\"…"

#: src/components/notification.jsx:452
msgid "Liked by…"
msgstr "Нравится…"

#: src/components/notification.jsx:453
msgid "Boosted by…"
msgstr "Продвинули…"

#: src/components/notification.jsx:454
msgid "Followed by…"
msgstr "Подписались…"

#: src/components/notification.jsx:535
#: src/components/notification.jsx:551
msgid "Learn more <0/>"
msgstr "Подробнее <0/>"

#: src/components/notification.jsx:560
msgid "View #Wrapstodon"
msgstr "Посмотреть #Wrapstodon"

#: src/components/notification.jsx:801
#: src/components/status.jsx:403
msgid "Read more →"
msgstr "Читать далее →"

#: src/components/poll.jsx:113
msgid "Voted"
msgstr "Проголосовали"

#: src/components/poll.jsx:119
msgid "{optionVotesCount, plural, one {# vote} other {# votes}}"
msgstr "{optionVotesCount, plural, one {# голос} other {# голос(а/ов)}}"

#: src/components/poll.jsx:139
#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Hide results"
msgstr "Скрыть результаты"

#: src/components/poll.jsx:188
msgid "Vote"
msgstr "Голосовать"

#: src/components/poll.jsx:208
#: src/components/poll.jsx:210
#: src/pages/scheduled-posts.jsx:100
#: src/pages/status.jsx:1294
#: src/pages/status.jsx:1317
msgid "Refresh"
msgstr "Обновить"

#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Show results"
msgstr "Показать результаты"

#. placeholder {0}: shortenNumber(votesCount)
#. placeholder {1}: shortenNumber(votesCount)
#: src/components/poll.jsx:231
msgid "{votesCount, plural, one {<0>{0}</0> vote} other {<1>{1}</1> votes}}"
msgstr "{votesCount, plural, one {<0>{0}</0> проголосовал(а)} other {<1>{1}</1> проголосовали}}"

#. placeholder {0}: shortenNumber(votersCount)
#. placeholder {1}: shortenNumber(votersCount)
#: src/components/poll.jsx:248
msgid "{votersCount, plural, one {<0>{0}</0> voter} other {<1>{1}</1> voters}}"
msgstr "{votersCount, plural, one {<0>{0}</0> голосующ(ий/ая)} other {<1>{1}</1> голосующих}}"

#: src/components/poll.jsx:268
msgid "Ended <0/>"
msgstr "Завершено <0/>"

#: src/components/poll.jsx:272
msgid "Ended"
msgstr "Завершено"

#: src/components/poll.jsx:275
msgid "Ending <0/>"
msgstr "Завершение <0/>"

#: src/components/poll.jsx:279
msgid "Ending"
msgstr "Завершение"

#. Relative time in seconds, as short as possible
#. placeholder {0}: seconds < 1 ? 1 : Math.floor(seconds)
#: src/components/relative-time.jsx:61
msgid "{0}s"
msgstr "{0}с"

#. Relative time in minutes, as short as possible
#. placeholder {0}: Math.floor(seconds / minute)
#: src/components/relative-time.jsx:66
msgid "{0}m"
msgstr "{0}м"

#. Relative time in hours, as short as possible
#. placeholder {0}: Math.floor(seconds / hour)
#: src/components/relative-time.jsx:71
msgid "{0}h"
msgstr "{0}ч"

#: src/components/report-modal.jsx:30
msgid "Spam"
msgstr "Спам"

#: src/components/report-modal.jsx:31
msgid "Malicious links, fake engagement, or repetitive replies"
msgstr "Вредоносные ссылки, поддельные действия или повторяющиеся ответы"

#: src/components/report-modal.jsx:34
msgid "Illegal"
msgstr "Нарушение закона/правил"

#: src/components/report-modal.jsx:35
msgid "Violates the law of your or the server's country"
msgstr "Нарушает закон вашей страны или сервера"

#: src/components/report-modal.jsx:38
msgid "Server rule violation"
msgstr "Нарушение правил сервера"

#: src/components/report-modal.jsx:39
msgid "Breaks specific server rules"
msgstr "Нарушение определённых правил сервера"

#: src/components/report-modal.jsx:40
msgid "Violation"
msgstr "Нарушение"

#: src/components/report-modal.jsx:43
msgid "Other"
msgstr "Другое"

#: src/components/report-modal.jsx:44
msgid "Issue doesn't fit other categories"
msgstr "Проблема не соответствует другим категориям"

#: src/components/report-modal.jsx:111
msgid "Report Post"
msgstr "Пожаловаться на публикацию"

#: src/components/report-modal.jsx:111
msgid "Report @{username}"
msgstr "Пожаловаться на @{username}"

#: src/components/report-modal.jsx:147
msgid "Pending review"
msgstr "Ожидает рассмотрения"

#: src/components/report-modal.jsx:189
msgid "Post reported"
msgstr "Жалоба на публикацию была отправлена"

#: src/components/report-modal.jsx:189
msgid "Profile reported"
msgstr "Жалоба на профиль была отправлена"

#: src/components/report-modal.jsx:197
msgid "Unable to report post"
msgstr "Не удалось пожаловаться на публикацию"

#: src/components/report-modal.jsx:198
msgid "Unable to report profile"
msgstr "Не удалось пожаловаться на профиль"

#: src/components/report-modal.jsx:206
msgid "What's the issue with this post?"
msgstr "В чем проблема с этой публикацией?"

#: src/components/report-modal.jsx:207
msgid "What's the issue with this profile?"
msgstr "В чем проблема с этим профилем?"

#: src/components/report-modal.jsx:276
msgid "Additional info"
msgstr "Дополнительная информация"

#: src/components/report-modal.jsx:299
msgid "Forward to <0>{domain}</0>"
msgstr "Переслать <0>{domain}</0>"

#: src/components/report-modal.jsx:309
msgid "Send Report"
msgstr "Пожаловаться"

#: src/components/report-modal.jsx:318
msgid "Muted {username}"
msgstr "Добавить @{username} в игнорируемые"

#: src/components/report-modal.jsx:321
msgid "Unable to mute {username}"
msgstr "Не удалось добавить @{username} в список игнорируемых"

#: src/components/report-modal.jsx:326
msgid "Send Report <0>+ Mute profile</0>"
msgstr "Отправить жалобу <0>+ Заглушить профиль</0>"

#: src/components/report-modal.jsx:337
msgid "Blocked {username}"
msgstr "{username} заблокирован(а)"

#: src/components/report-modal.jsx:340
msgid "Unable to block {username}"
msgstr "Не удалось заблокировать {username}"

#: src/components/report-modal.jsx:345
msgid "Send Report <0>+ Block profile</0>"
msgstr "Отправить жалобу <0>+ Заблокировать профиль</0>"

#: src/components/search-form.jsx:203
msgid "{query} <0>‒ accounts, hashtags & posts</0>"
msgstr "{query} <0>- аккаунтов, хэштегов и постов</0>"

#: src/components/search-form.jsx:216
msgid "Posts with <0>{query}</0>"
msgstr "Посты с <0>{query}</0>"

#. placeholder {0}: query.replace(/^#/, '')
#: src/components/search-form.jsx:228
msgid "Posts tagged with <0>#{0}</0>"
msgstr "Посты, помеченные <0>#{0}</0>"

#: src/components/search-form.jsx:242
msgid "Look up <0>{query}</0>"
msgstr "Посмотреть <0>{query}</0>"

#: src/components/search-form.jsx:253
msgid "Accounts with <0>{query}</0>"
msgstr "Учетные записи с <0>{query}</0>"

#: src/components/shortcuts-settings.jsx:48
msgid "Home / Following"
msgstr "Домашняя / Подписки"

#: src/components/shortcuts-settings.jsx:51
msgid "Public (Local / Federated)"
msgstr "Публичная (Локальная / Федеративная)"

#: src/components/shortcuts-settings.jsx:53
msgid "Account"
msgstr "Учетная запись"

#: src/components/shortcuts-settings.jsx:56
msgid "Hashtag"
msgstr "Хэштег"

#: src/components/shortcuts-settings.jsx:63
msgid "List ID"
msgstr "ID списка"

#: src/components/shortcuts-settings.jsx:70
msgid "Local only"
msgstr "Только локальная"

#: src/components/shortcuts-settings.jsx:75
#: src/components/shortcuts-settings.jsx:84
#: src/components/shortcuts-settings.jsx:122
#: src/pages/login.jsx:203
msgid "Instance"
msgstr "Инстанс"

#: src/components/shortcuts-settings.jsx:78
#: src/components/shortcuts-settings.jsx:87
#: src/components/shortcuts-settings.jsx:125
msgid "Optional, e.g. mastodon.social"
msgstr "Необязательно, например, mastodon.social"

#: src/components/shortcuts-settings.jsx:93
msgid "Search term"
msgstr "Поисковый запрос"

#: src/components/shortcuts-settings.jsx:96
msgid "Optional, unless for multi-column mode"
msgstr "Необязательно, если не выбран многоколоночный режим"

#: src/components/shortcuts-settings.jsx:113
msgid "e.g. PixelArt (Max 5, space-separated)"
msgstr "напр. PixelArt (Макс 5, разделенные пробелом)"

#: src/components/shortcuts-settings.jsx:117
#: src/pages/hashtag.jsx:360
msgid "Media only"
msgstr "Только с медиафайлами"

#: src/components/shortcuts-settings.jsx:235
#: src/components/shortcuts.jsx:218
msgid "Shortcuts"
msgstr "Ярлыки"

#: src/components/shortcuts-settings.jsx:243
msgid "beta"
msgstr "бета-версия"

#: src/components/shortcuts-settings.jsx:249
msgid "Specify a list of shortcuts that'll appear as:"
msgstr "Укажите список ярлыков, которые будут отображаться как:"

#: src/components/shortcuts-settings.jsx:255
msgid "Floating button"
msgstr "Плавающая кнопка"

#: src/components/shortcuts-settings.jsx:260
msgid "Tab/Menu bar"
msgstr "Вкладки/Меню"

#: src/components/shortcuts-settings.jsx:265
msgid "Multi-column"
msgstr "Многоколоночный"

#: src/components/shortcuts-settings.jsx:332
msgid "Not available in current view mode"
msgstr "Недоступно в текущем режиме просмотра"

#: src/components/shortcuts-settings.jsx:351
#: src/pages/accounts.jsx:188
msgid "Move up"
msgstr "Переместить вверх"

#: src/components/shortcuts-settings.jsx:367
#: src/pages/accounts.jsx:203
msgid "Move down"
msgstr "Переместить вниз"

#: src/components/shortcuts-settings.jsx:379
#: src/components/status.jsx:1474
#: src/pages/list.jsx:195
msgid "Edit"
msgstr "Редактировать"

#: src/components/shortcuts-settings.jsx:400
msgid "Add more than one shortcut/column to make this work."
msgstr "Добавьте более одного ярлыка/столбца, чтобы сделать это."

#: src/components/shortcuts-settings.jsx:411
msgid "No columns yet. Tap on the Add column button."
msgstr "Еще нет столбцов. Нажмите на кнопку Добавить столбец."

#: src/components/shortcuts-settings.jsx:412
msgid "No shortcuts yet. Tap on the Add shortcut button."
msgstr "Пока нет ярлыков. Нажмите на кнопку Добавить ярлык."

#: src/components/shortcuts-settings.jsx:415
msgid "Not sure what to add?<0/>Try adding <1>Home / Following and Notifications</1> first."
msgstr "Не уверены, что добавить? <0/>Попробуйте добавить <1>Домашняя / Подписки и Уведомления</1>."

#: src/components/shortcuts-settings.jsx:443
msgid "Max {SHORTCUTS_LIMIT} columns"
msgstr "Макс. {SHORTCUTS_LIMIT} столбцов"

#: src/components/shortcuts-settings.jsx:444
msgid "Max {SHORTCUTS_LIMIT} shortcuts"
msgstr "Макс. {SHORTCUTS_LIMIT} ярлыков"

#: src/components/shortcuts-settings.jsx:458
msgid "Import/export"
msgstr "Импорт/экспорт"

#: src/components/shortcuts-settings.jsx:468
msgid "Add column…"
msgstr "Добавить столбец…"

#: src/components/shortcuts-settings.jsx:469
msgid "Add shortcut…"
msgstr "Добавить ярлык…"

#: src/components/shortcuts-settings.jsx:516
msgid "Specific list is optional. For multi-column mode, list is required, else the column will not be shown."
msgstr "Определенный список является необязательным. Для многоколоночного режима требуется список, иначе столбец не будет показан."

#: src/components/shortcuts-settings.jsx:517
msgid "For multi-column mode, search term is required, else the column will not be shown."
msgstr "Для многоколоночного режима требуется поисковый запрос, иначе столбец не будет показан."

#: src/components/shortcuts-settings.jsx:518
msgid "Multiple hashtags are supported. Space-separated."
msgstr "Поддерживаются несколько хэштегов. Разделенные пробелами."

#: src/components/shortcuts-settings.jsx:587
msgid "Edit shortcut"
msgstr "Редактировать ярлык"

#: src/components/shortcuts-settings.jsx:587
msgid "Add shortcut"
msgstr "Добавить ярлык"

#: src/components/shortcuts-settings.jsx:623
msgid "Timeline"
msgstr "Лента"

#: src/components/shortcuts-settings.jsx:649
msgid "List"
msgstr "Список"

#: src/components/shortcuts-settings.jsx:788
msgid "Import/Export <0>Shortcuts</0>"
msgstr "Импорт/Экспорт <0>Ярлыков</0>"

#: src/components/shortcuts-settings.jsx:798
msgid "Import"
msgstr "Импорт"

#: src/components/shortcuts-settings.jsx:806
msgid "Paste shortcuts here"
msgstr "Вставьте ярлыки сюда"

#: src/components/shortcuts-settings.jsx:822
msgid "Downloading saved shortcuts from instance server…"
msgstr "Загрузка сохраненных ярлыков с сервера инстанса…"

#: src/components/shortcuts-settings.jsx:851
msgid "Unable to download shortcuts"
msgstr "Не удается загрузить ярлыки"

#: src/components/shortcuts-settings.jsx:854
msgid "Download shortcuts from instance server"
msgstr "Скачать ярлыки с сервера инстанса"

#: src/components/shortcuts-settings.jsx:912
msgid "* Exists in current shortcuts"
msgstr "* Существуют в текущем ярлыке"

#: src/components/shortcuts-settings.jsx:917
msgid "List may not work if it's from a different account."
msgstr "Список не может работать, если он из другой учетной записи."

#: src/components/shortcuts-settings.jsx:927
msgid "Invalid settings format"
msgstr "Недопустимый формат настроек"

#: src/components/shortcuts-settings.jsx:935
msgid "Append to current shortcuts?"
msgstr "Добавить в текущие ярлыки?"

#: src/components/shortcuts-settings.jsx:938
msgid "Only shortcuts that don’t exist in current shortcuts will be appended."
msgstr "Будут добавлены только ярлыки, которые не существуют в текущем ярлыке."

#: src/components/shortcuts-settings.jsx:960
msgid "No new shortcuts to import"
msgstr "Нет новых ярлыков для импорта"

#: src/components/shortcuts-settings.jsx:975
msgid "Shortcuts imported. Exceeded max {SHORTCUTS_LIMIT}, so the rest are not imported."
msgstr "Ярлыки импортированы. Превышен максимальный размер ({SHORTCUTS_LIMIT}), поэтому остальные не импортируются."

#: src/components/shortcuts-settings.jsx:976
#: src/components/shortcuts-settings.jsx:1000
msgid "Shortcuts imported"
msgstr "Ярлыков импортировано"

#: src/components/shortcuts-settings.jsx:986
msgid "Import & append…"
msgstr "Импортировать и добавить…"

#: src/components/shortcuts-settings.jsx:994
msgid "Override current shortcuts?"
msgstr "Заменить текущие ярлыки?"

#: src/components/shortcuts-settings.jsx:995
msgid "Import shortcuts?"
msgstr "Импортировать ярлыки?"

#: src/components/shortcuts-settings.jsx:1009
msgid "or override…"
msgstr "или заменить…"

#: src/components/shortcuts-settings.jsx:1009
msgid "Import…"
msgstr "Импортировать…"

#: src/components/shortcuts-settings.jsx:1018
msgid "Export"
msgstr "Экспорт"

#: src/components/shortcuts-settings.jsx:1033
msgid "Shortcuts copied"
msgstr "Ярлыки скопированы"

#: src/components/shortcuts-settings.jsx:1036
msgid "Unable to copy shortcuts"
msgstr "Не удалось скопировать ярлыки"

#: src/components/shortcuts-settings.jsx:1050
msgid "Shortcut settings copied"
msgstr "Настройки ярлыков скопированы"

#: src/components/shortcuts-settings.jsx:1053
msgid "Unable to copy shortcut settings"
msgstr "Не удалось скопировать настройки ярлыка"

#: src/components/shortcuts-settings.jsx:1083
msgid "Share"
msgstr "Поделиться"

#: src/components/shortcuts-settings.jsx:1122
msgid "Saving shortcuts to instance server…"
msgstr "Сохранение ярлыков на сервер инстанса…"

#: src/components/shortcuts-settings.jsx:1129
msgid "Shortcuts saved"
msgstr "Ярлыки сохранены"

#: src/components/shortcuts-settings.jsx:1134
msgid "Unable to save shortcuts"
msgstr "Не удалось сохранить ярлыки"

#: src/components/shortcuts-settings.jsx:1137
msgid "Sync to instance server"
msgstr "Синхронизировать с сервером инстанса"

#. placeholder {0}: shortcutsStr.length
#: src/components/shortcuts-settings.jsx:1145
msgid "{0, plural, one {# character} other {# characters}}"
msgstr "{0, plural, one {# символ} other {# символов}}"

#: src/components/shortcuts-settings.jsx:1157
msgid "Raw Shortcuts JSON"
msgstr "Исходный JSON ярлыков"

#: src/components/shortcuts-settings.jsx:1170
msgid "Import/export settings from/to instance server (Very experimental)"
msgstr "Импорт/экспорт настроек с сервера инстанса (Очень экспериментально)"

#: src/components/status.jsx:277
msgid "Unable to format math"
msgstr ""

#: src/components/status.jsx:292
msgid "Math expressions found."
msgstr ""

#: src/components/status.jsx:294
msgid "Show markup"
msgstr ""

#: src/components/status.jsx:294
msgid "Format math"
msgstr ""

#: src/components/status.jsx:689
msgid "<0/> <1>boosted</1>"
msgstr "<0/> <1>продвинул(а)</1>"

#: src/components/status.jsx:792
msgid "Sorry, your current logged-in instance can't interact with this post from another instance."
msgstr "К сожалению, ваша текущая учетная запись не может взаимодействовать с этим постом из другого инстанса."

#. placeholder {0}: username || acct
#: src/components/status.jsx:946
msgid "Unliked @{0}'s post"
msgstr "Удалена отметка \"нравится\" у поста от @{0}"

#. placeholder {0}: username || acct
#: src/components/status.jsx:947
msgid "Liked @{0}'s post"
msgstr "Понравился пост от @{0}"

#. placeholder {0}: username || acct
#: src/components/status.jsx:986
msgid "Unbookmarked @{0}'s post"
msgstr "Удалена закладка поста от @{0}"

#. placeholder {0}: username || acct
#: src/components/status.jsx:987
msgid "Bookmarked @{0}'s post"
msgstr "Добавлена закладка поста от @{0}"

#: src/components/status.jsx:1086
msgid "Some media have no descriptions."
msgstr "Некоторые медиа не имеют описаний."

#. placeholder {0}: rtf.format(-statusMonthsAgo, 'month')
#: src/components/status.jsx:1093
msgid "Old post (<0>{0}</0>)"
msgstr "Старый пост (<0>{0}</0>)"

#: src/components/status.jsx:1117
#: src/components/status.jsx:1157
#: src/components/status.jsx:2617
#: src/components/status.jsx:2640
msgid "Unboost"
msgstr "Прекратить продвигать"

#: src/components/status.jsx:1133
#: src/components/status.jsx:2632
msgid "Quote"
msgstr "Цитировать"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1145
#: src/components/status.jsx:1611
msgid "Unboosted @{0}'s post"
msgstr "Прекратил(а) продвигать пост @{0}"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1146
#: src/components/status.jsx:1612
msgid "Boosted @{0}'s post"
msgstr "Продвинул(а) пост @{0}"

#: src/components/status.jsx:1158
msgid "Boost…"
msgstr "Продвинуть…"

#: src/components/status.jsx:1170
#: src/components/status.jsx:1905
#: src/components/status.jsx:2653
msgid "Unlike"
msgstr "Не нравится"

#: src/components/status.jsx:1171
#: src/components/status.jsx:1905
#: src/components/status.jsx:1906
#: src/components/status.jsx:2653
#: src/components/status.jsx:2654
msgid "Like"
msgstr "Нравится"

#: src/components/status.jsx:1180
#: src/components/status.jsx:2665
msgid "Unbookmark"
msgstr "Удалить закладку"

#: src/components/status.jsx:1263
msgid "Post text copied"
msgstr "Текст поста скопирован"

#: src/components/status.jsx:1266
msgid "Unable to copy post text"
msgstr "Не удалось скопировать текст поста"

#: src/components/status.jsx:1272
msgid "Copy post text"
msgstr "Копировать текст поста"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1290
msgid "View post by <0>@{0}</0>"
msgstr "Посмотреть пост <0>@{0}</0>"

#: src/components/status.jsx:1311
msgid "Show Edit History"
msgstr "Показать историю редактирования"

#: src/components/status.jsx:1314
msgid "Edited: {editedDateText}"
msgstr "Отредактировано: {editedDateText}"

#: src/components/status.jsx:1381
#: src/components/status.jsx:3433
msgid "Embed post"
msgstr "Встроить пост"

#: src/components/status.jsx:1395
msgid "Conversation unmuted"
msgstr "Обсуждение не игнорируется"

#: src/components/status.jsx:1395
msgid "Conversation muted"
msgstr "Обсуждение игнорируется"

#: src/components/status.jsx:1401
msgid "Unable to unmute conversation"
msgstr "Не удалось прекратить игнорировать обсуждение"

#: src/components/status.jsx:1402
msgid "Unable to mute conversation"
msgstr "Не удалось игнорировать обсуждение"

#: src/components/status.jsx:1411
msgid "Unmute conversation"
msgstr "Не игнорировать обсуждение"

#: src/components/status.jsx:1418
msgid "Mute conversation"
msgstr "Игнорировать обсуждение"

#: src/components/status.jsx:1434
msgid "Post unpinned from profile"
msgstr "Пост откреплён из профиля"

#: src/components/status.jsx:1435
msgid "Post pinned to profile"
msgstr "Пост прикреплён к профилю"

#: src/components/status.jsx:1440
msgid "Unable to unpin post"
msgstr "Не удалось открепить пост"

#: src/components/status.jsx:1440
msgid "Unable to pin post"
msgstr "Не удалось закрепить пост"

#: src/components/status.jsx:1449
msgid "Unpin from profile"
msgstr "Открепить от профиля"

#: src/components/status.jsx:1456
msgid "Pin to profile"
msgstr "Закрепить в профиле"

#: src/components/status.jsx:1485
msgid "Delete this post?"
msgstr "Удалить этот пост?"

#: src/components/status.jsx:1501
msgid "Post deleted"
msgstr "Пост удален"

#: src/components/status.jsx:1504
msgid "Unable to delete post"
msgstr "Не удалось удалить пост"

#: src/components/status.jsx:1532
msgid "Report post…"
msgstr "Пожаловаться на пост…"

#: src/components/status.jsx:1906
#: src/components/status.jsx:1942
#: src/components/status.jsx:2654
msgid "Liked"
msgstr "Нравится"

#: src/components/status.jsx:1939
#: src/components/status.jsx:2641
msgid "Boosted"
msgstr "Продвинут"

#: src/components/status.jsx:1949
#: src/components/status.jsx:2666
msgid "Bookmarked"
msgstr "Добавлен в закладки"

#: src/components/status.jsx:1953
msgid "Pinned"
msgstr "Закреплён"

#: src/components/status.jsx:1999
#: src/components/status.jsx:2478
msgid "Deleted"
msgstr "Удалён"

#: src/components/status.jsx:2040
msgid "{repliesCount, plural, one {# reply} other {# replies}}"
msgstr "{repliesCount, plural, one {# ответ} other {# ответ(а/ов)}}"

#. placeholder {0}: snapStates.statusThreadNumber[sKey] ? ` ${snapStates.statusThreadNumber[sKey]}/X` : ''
#: src/components/status.jsx:2130
msgid "Thread{0}"
msgstr "Обсуждение{0}"

#: src/components/status.jsx:2208
#: src/components/status.jsx:2270
#: src/components/status.jsx:2374
msgid "Show less"
msgstr "Свернуть/скрыть"

#: src/components/status.jsx:2208
#: src/components/status.jsx:2270
msgid "Show content"
msgstr "Показать содержимое"

#. placeholder {0}: filterInfo.titlesStr
#. placeholder {0}: filterInfo?.titlesStr
#: src/components/status.jsx:2370
#: src/pages/catchup.jsx:1879
msgid "Filtered: {0}"
msgstr "Отфильтровано: {0}"

#: src/components/status.jsx:2374
msgid "Show media"
msgstr "Показать медиа"

#: src/components/status.jsx:2514
msgid "Edited"
msgstr "Отредактировано"

#: src/components/status.jsx:2591
msgid "Comments"
msgstr "Комментарии"

#. More from [Author]
#: src/components/status.jsx:2891
msgid "More from <0/>"
msgstr "Больше от <0/>"

#: src/components/status.jsx:3193
msgid "Edit History"
msgstr "История изменений"

#: src/components/status.jsx:3197
msgid "Failed to load history"
msgstr "Не удалось загрузить историю"

#: src/components/status.jsx:3202
#: src/pages/annual-report.jsx:45
msgid "Loading…"
msgstr "Загрузка…"

#: src/components/status.jsx:3438
msgid "HTML Code"
msgstr "HTML-код"

#: src/components/status.jsx:3455
msgid "HTML code copied"
msgstr "HTML-код скопирован"

#: src/components/status.jsx:3458
msgid "Unable to copy HTML code"
msgstr "Не удалось скопировать HTML-код"

#: src/components/status.jsx:3470
msgid "Media attachments:"
msgstr "Медиа-вложения:"

#: src/components/status.jsx:3492
msgid "Account Emojis:"
msgstr "Эмодзи пользователя:"

#: src/components/status.jsx:3523
#: src/components/status.jsx:3568
msgid "static URL"
msgstr "статический URL"

#: src/components/status.jsx:3537
msgid "Emojis:"
msgstr "Эмодзи:"

#: src/components/status.jsx:3582
msgid "Notes:"
msgstr "Заметки:"

#: src/components/status.jsx:3586
msgid "This is static, unstyled and scriptless. You may need to apply your own styles and edit as needed."
msgstr "Это статическое, нестилизованное и не требующее сценариев. Возможно, вам придется применить свои собственные стили и отредактировать их по мере необходимости."

#: src/components/status.jsx:3592
msgid "Polls are not interactive, becomes a list with vote counts."
msgstr "Опросы не интерактивны, они представляют собой список с подсчетом голосов."

#: src/components/status.jsx:3597
msgid "Media attachments can be images, videos, audios or any file types."
msgstr "Медиа-вложения могут быть изображениями, видео, аудио или определёнными типами файлов."

#: src/components/status.jsx:3603
msgid "Post could be edited or deleted later."
msgstr "Пост может быть отредактирован или удален позже."

#: src/components/status.jsx:3609
msgid "Preview"
msgstr "Предпросмотр"

#: src/components/status.jsx:3618
msgid "Note: This preview is lightly styled."
msgstr "Примечание: Этот предварительный просмотр слегка стилизован."

#. [Name] [Visibility icon] boosted
#: src/components/status.jsx:3871
msgid "<0/> <1/> boosted"
msgstr "<0/> <1/> продвинули"

#: src/components/status.jsx:3973
msgid "Post hidden by your filters"
msgstr ""

#: src/components/status.jsx:3974
msgid "Post removed by author."
msgstr "Пост удален автором."

#: src/components/status.jsx:3975
msgid "You’re not authorized to view this post."
msgstr "У вас нет прав для просмотра этого поста."

#: src/components/status.jsx:3976
msgid "Post pending author approval."
msgstr "Пост в ожидании одобрения автора."

#: src/components/status.jsx:3977
#: src/components/status.jsx:3978
msgid "Quoting not allowed by the author."
msgstr "Цитирование не разрешено автором."

#: src/components/timeline.jsx:485
#: src/pages/settings.jsx:1218
msgid "New posts"
msgstr "Новые посты"

#: src/components/timeline.jsx:586
#: src/pages/home.jsx:228
#: src/pages/notifications.jsx:898
#: src/pages/status.jsx:1077
#: src/pages/status.jsx:1454
msgid "Try again"
msgstr "Повторите попытку"

#. placeholder {0}: fItems.length
#: src/components/timeline.jsx:621
msgid "{0, plural, one {# Boost} other {# Boosts}}"
msgstr "{0, plural, one {# Продвижение} other {# Продвижения}}"

#: src/components/timeline.jsx:626
msgid "Pinned posts"
msgstr "Закреплённые посты"

#: src/components/timeline.jsx:985
#: src/components/timeline.jsx:992
#: src/pages/catchup.jsx:1897
msgid "Thread"
msgstr "Обсуждение"

#. placeholder {0}: filterInfo.titlesStr
#: src/components/timeline.jsx:1007
msgid "<0>Filtered</0>: <1>{0}</1>"
msgstr "<0>Отфильтровано</0>: <1>{0}</1>"

#: src/components/translation-block.jsx:196
msgid "Auto-translated from {sourceLangText}"
msgstr "Автоперевод с {sourceLangText}"

#: src/components/translation-block.jsx:234
msgid "Translating…"
msgstr "Переводим…"

#: src/components/translation-block.jsx:237
msgid "Translate from {sourceLangText} (auto-detected)"
msgstr "Перевод с {sourceLangText} (автоопределение)"

#: src/components/translation-block.jsx:238
msgid "Translate from {sourceLangText}"
msgstr "Перевод с {sourceLangText}"

#. placeholder {0}: detectedLang ?? '…'
#: src/components/translation-block.jsx:266
msgid "Auto ({0})"
msgstr "Авто ({0})"

#: src/components/translation-block.jsx:279
msgid "Failed to translate"
msgstr "Не удалось перевести"

#: src/compose.jsx:33
msgid "Editing source status"
msgstr "Редактирование статуса источника"

#. placeholder {0}: replyToStatus.account?.acct || replyToStatus.account?.username
#: src/compose.jsx:35
msgid "Replying to @{0}"
msgstr "Ответить @{0}"

#: src/compose.jsx:63
msgid "You may close this page now."
msgstr "Теперь вы можете закрыть эту страницу."

#: src/compose.jsx:71
msgid "Close window"
msgstr "Закрыть окно"

#: src/compose.jsx:87
msgid "Login required."
msgstr "Требуется авторизация."

#: src/compose.jsx:91
#: src/pages/annual-report.jsx:164
#: src/pages/http-route.jsx:91
#: src/pages/login.jsx:280
msgid "Go home"
msgstr "Вернуться на главную"

#: src/pages/account-statuses.jsx:238
msgid "Account posts"
msgstr "Посты пользователя"

#: src/pages/account-statuses.jsx:245
msgid "{accountDisplay} (+ Replies)"
msgstr "{accountDisplay} (+ Ответы)"

#: src/pages/account-statuses.jsx:247
msgid "{accountDisplay} (- Boosts)"
msgstr "{accountDisplay} (- Продвижения)"

#: src/pages/account-statuses.jsx:249
msgid "{accountDisplay} (#{tagged})"
msgstr "{accountDisplay} (#{tagged})"

#: src/pages/account-statuses.jsx:251
msgid "{accountDisplay} (Media)"
msgstr "{accountDisplay} (Медиа)"

#: src/pages/account-statuses.jsx:257
msgid "{accountDisplay} ({monthYear})"
msgstr "{accountDisplay} ({monthYear})"

#: src/pages/account-statuses.jsx:323
msgid "Clear filters"
msgstr "Очистить фильтры"

#: src/pages/account-statuses.jsx:326
msgid "Clear"
msgstr "Очистить"

#: src/pages/account-statuses.jsx:340
msgid "Showing post with replies"
msgstr "Посты с ответами"

#: src/pages/account-statuses.jsx:345
msgid "+ Replies"
msgstr "+ Ответы"

#: src/pages/account-statuses.jsx:351
msgid "Showing posts without boosts"
msgstr "Посты без продвижений"

#: src/pages/account-statuses.jsx:356
msgid "- Boosts"
msgstr "- Продвижения"

#: src/pages/account-statuses.jsx:362
msgid "Showing posts with media"
msgstr "Посты с медиа-контентом"

#. placeholder {0}: tag.name
#: src/pages/account-statuses.jsx:379
msgid "Showing posts tagged with #{0}"
msgstr "Посты, помеченные #{0}"

#. placeholder {0}: date.toLocaleString(i18n.locale, { month: 'long', year: 'numeric', })
#: src/pages/account-statuses.jsx:418
msgid "Showing posts in {0}"
msgstr "Посты от {0}"

#: src/pages/account-statuses.jsx:508
msgid "Nothing to see here yet."
msgstr "Здесь пока ничего нет."

#: src/pages/account-statuses.jsx:509
#: src/pages/public.jsx:99
#: src/pages/trending.jsx:452
msgid "Unable to load posts"
msgstr "Не удается загрузить посты"

#: src/pages/account-statuses.jsx:550
#: src/pages/account-statuses.jsx:580
msgid "Unable to fetch account info"
msgstr "Не удалось получить информацию об учетной записи"

#. placeholder {0}: accountInstance ? ( <> {' '} (<b>{punycode.toUnicode(accountInstance)}</b>) </> ) : null
#: src/pages/account-statuses.jsx:557
msgid "Switch to account's instance {0}"
msgstr "Переключиться на инстанс {0} учетной записи"

#: src/pages/account-statuses.jsx:587
msgid "Switch to my instance (<0>{currentInstance}</0>)"
msgstr "Переключиться на мой инстанс (<0>{currentInstance}</0>)"

#: src/pages/account-statuses.jsx:660
msgid "Month"
msgstr "Месяц"

#: src/pages/accounts.jsx:64
msgid "Current"
msgstr "Текущий"

#: src/pages/accounts.jsx:110
msgid "Default"
msgstr "По умолчанию"

#: src/pages/accounts.jsx:132
msgid "Switch to this account"
msgstr "Переключить на этот аккаунт"

#: src/pages/accounts.jsx:141
msgid "Switch in new tab/window"
msgstr "Переключиться в новой вкладке/окне"

#: src/pages/accounts.jsx:155
msgid "View profile…"
msgstr "Посмотреть профиль…"

#: src/pages/accounts.jsx:173
msgid "Set as default"
msgstr "Установить по умолчанию"

#. placeholder {0}: account.info.acct
#: src/pages/accounts.jsx:215
msgid "Log out <0>@{0}</0>?"
msgstr "Выйти из<0>@{0}</0>?"

#: src/pages/accounts.jsx:244
msgid "Log out…"
msgstr "Выйти…"

#. placeholder {0}: niceDateTime(account.createdAt)
#: src/pages/accounts.jsx:251
msgid "Connected on {0} (<0/>)"
msgstr "Подключено к {0} (<0/>)"

#: src/pages/accounts.jsx:268
msgid "Add an existing account"
msgstr "Добавить существующую учетную запись"

#: src/pages/accounts.jsx:275
msgid "Note: <0>Default</0> account will always be used for first load. Switched accounts will persist during the session."
msgstr "Примечание. Учетная запись <0>по умолчанию</0> всегда будет использоваться для первой загрузки. Переключенные учетные записи будут сохраняться в течение сеанса."

#: src/pages/bookmarks.jsx:28
msgid "No bookmarks yet. Go bookmark something!"
msgstr "Нет закладок. Добавьте какую-нибудь!"

#: src/pages/bookmarks.jsx:29
msgid "Unable to load bookmarks."
msgstr "Не удалось загрузить закладки."

#: src/pages/catchup.jsx:54
msgid "last 1 hour"
msgstr "за последний час"

#: src/pages/catchup.jsx:55
msgid "last 2 hours"
msgstr "за последние 2 часа"

#: src/pages/catchup.jsx:56
msgid "last 3 hours"
msgstr "за последние 3 часа"

#: src/pages/catchup.jsx:57
msgid "last 4 hours"
msgstr "за последние 4 часа"

#: src/pages/catchup.jsx:58
msgid "last 5 hours"
msgstr "за последние 5 часов"

#: src/pages/catchup.jsx:59
msgid "last 6 hours"
msgstr "за последние 6 часов"

#: src/pages/catchup.jsx:60
msgid "last 7 hours"
msgstr "за последние 7 часов"

#: src/pages/catchup.jsx:61
msgid "last 8 hours"
msgstr "за последние 8 часов"

#: src/pages/catchup.jsx:62
msgid "last 9 hours"
msgstr "за последние 9 часов"

#: src/pages/catchup.jsx:63
msgid "last 10 hours"
msgstr "за последние 10 часов"

#: src/pages/catchup.jsx:64
msgid "last 11 hours"
msgstr "за последние 11 часов"

#: src/pages/catchup.jsx:65
msgid "last 12 hours"
msgstr "за последние 12 часов"

#: src/pages/catchup.jsx:66
msgid "beyond 12 hours"
msgstr "более 12 часов"

#: src/pages/catchup.jsx:73
msgid "Followed tags"
msgstr "Отслеживаемые хэштеги"

#: src/pages/catchup.jsx:74
msgid "Groups"
msgstr "Группы"

#: src/pages/catchup.jsx:597
msgid "Showing {selectedFilterCategory, select, all {all posts} original {original posts} replies {replies} boosts {boosts} followedTags {followed tags} groups {groups} filtered {filtered posts}}, {sortBy, select, createdAt {{sortOrder, select, asc {oldest} desc {latest}}} reblogsCount {{sortOrder, select, asc {fewest boosts} desc {most boosts}}} favouritesCount {{sortOrder, select, asc {fewest likes} desc {most likes}}} repliesCount {{sortOrder, select, asc {fewest replies} desc {most replies}}} density {{sortOrder, select, asc {least dense} desc {most dense}}}} first{groupBy, select, account {, grouped by authors} other {}}"
msgstr "Отображается {selectedFilterCategory, select, all {все посты} original {оригинальные посты} replies {ответы} boosts {продвижения} followedTags {отслеживаемые тэги} groups {группы} filtered {отфильтрованные посты}}, {sortBy, select, createdAt {{sortOrder, select, asc {старые} desc {новейшие}}} reblogsCount {{sortOrder, select, asc {меньше всего продвижений} desc {больше всего продвижений}}} favouritesCount {{sortOrder, select, asc {меньше всего отметок \"нравится\"} desc {больше всего отметок \"нравится\"}}} repliesCount {{sortOrder, select, asc {меньше всего ответов} desc {больше всего ответов}}} density {{sortOrder, select, asc {менее плотные} desc {более плотные}}}} первые{groupBy, select, account {, сгруппированы по авторам} other {}}"

#: src/pages/catchup.jsx:889
#: src/pages/catchup.jsx:913
msgid "Catch-up <0>beta</0>"
msgstr "Catch-up <0>бета</0>"

#: src/pages/catchup.jsx:903
#: src/pages/catchup.jsx:1588
msgid "Help"
msgstr "Справка"

#: src/pages/catchup.jsx:919
msgid "What is this?"
msgstr "Что это?"

#: src/pages/catchup.jsx:922
msgid "Catch-up is a separate timeline for your followings, offering a high-level view at a glance, with a simple, email-inspired interface to effortlessly sort and filter through posts."
msgstr "Catch-up — это отдельная лента для ваших подписок, предлагающая на первый взгляд высокоуровневый вид, с простым и вдохновленным почтой интерфейсом, позволяющим легко сортировать и фильтровать по постам."

#: src/pages/catchup.jsx:933
msgid "Preview of Catch-up UI"
msgstr "Предпросмотр интерфейса Catch-up"

#: src/pages/catchup.jsx:942
msgid "Let's catch up"
msgstr "Давайте приступим"

#: src/pages/catchup.jsx:947
msgid "Let's catch up on the posts from your followings."
msgstr "Давай посмотрим на посты из ваших подписок."

#: src/pages/catchup.jsx:951
msgid "Show me all posts from…"
msgstr "Покажи мне все посты…"

#: src/pages/catchup.jsx:974
msgid "until the max"
msgstr "до максимума"

#: src/pages/catchup.jsx:1004
msgid "Catch up"
msgstr "Наверстать упущенное"

#: src/pages/catchup.jsx:1010
msgid "Overlaps with your last catch-up"
msgstr "Перепишет ваш последний catch-up"

#. placeholder {0}: dtf.format(new Date(lastCatchupEndAt))
#: src/pages/catchup.jsx:1022
msgid "Until the last catch-up ({0})"
msgstr "До последнего catch-up ({0})"

#: src/pages/catchup.jsx:1031
msgid "Note: your instance might only show a maximum of 800 posts in the Home timeline regardless of the time range. Could be less or more."
msgstr "Примечание: Ваш инстанс может показывать не более 800 сообщений в домашней ленте, независимо от диапазона времени. Может быть меньше или больше."

#: src/pages/catchup.jsx:1041
msgid "Previously…"
msgstr "Ранее…"

#. placeholder {0}: pc.count
#: src/pages/catchup.jsx:1059
msgid "{0, plural, one {# post} other {# posts}}"
msgstr "{0, plural, one {# пост} other {# посты}}"

#: src/pages/catchup.jsx:1069
msgid "Remove this catch-up?"
msgstr "Удалить этот catch-up?"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1072
msgid "Removing Catch-up {0}"
msgstr "Убираем Catch-up {0}"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1076
msgid "Catch-up {0} removed"
msgstr "Catch-up {0} убран"

#: src/pages/catchup.jsx:1090
msgid "Note: Only max 3 will be stored. The rest will be automatically removed."
msgstr "Внимание: Будут сохранены только не более 3. Остальные будут автоматически удалены."

#: src/pages/catchup.jsx:1105
msgid "Fetching posts…"
msgstr "Подгружаем посты…"

#: src/pages/catchup.jsx:1108
msgid "This might take a while."
msgstr "Это займет некоторое время."

#: src/pages/catchup.jsx:1143
msgid "Reset filters"
msgstr "Сбросить фильтры"

#: src/pages/catchup.jsx:1151
#: src/pages/catchup.jsx:1594
msgid "Top links"
msgstr "Самые популярные ссылки"

#. placeholder {0}: sharers.map((s) => { const { avatarStatic, displayName } = s; return ( <button type="button" class="plain" style={{ padding: 0, }} onClick={(e) => { e.preventDefault(); e.stopPropagation(); // Reset and filter to author const { id } = s; setSelectedAuthor(id); setSelectedFilterCategory('all'); }} > <Avatar url={avatarStatic} size="s" alt={displayName} /> </button> ); })
#: src/pages/catchup.jsx:1264
msgid "Shared by {0}"
msgstr "Поделил(ся/ась) {0}"

#: src/pages/catchup.jsx:1319
#: src/pages/mentions.jsx:154
#: src/pages/search.jsx:329
msgid "All"
msgstr "Все"

#. placeholder {0}: authorCountsList.length
#: src/pages/catchup.jsx:1404
msgid "{0, plural, one {# author} other {# authors}}"
msgstr "{0, plural, one {# автор} other {# авторы}}"

#: src/pages/catchup.jsx:1416
msgid "Sort"
msgstr "Сортировка"

#: src/pages/catchup.jsx:1447
msgid "Date"
msgstr "Дата"

#: src/pages/catchup.jsx:1451
msgid "Density"
msgstr "Плотность"

#. js-lingui-explicit-id
#: src/pages/catchup.jsx:1474
msgid "group.filter"
msgstr "Фильтр группы"

#: src/pages/catchup.jsx:1489
msgid "Authors"
msgstr "Авторы"

#: src/pages/catchup.jsx:1490
msgid "None"
msgstr "Отсутствует"

#: src/pages/catchup.jsx:1506
msgid "Show all authors"
msgstr "Показать всех авторов"

#: src/pages/catchup.jsx:1557
msgid "You don't have to read everything."
msgstr "Вы не обязаны читать всё."

#: src/pages/catchup.jsx:1558
msgid "That's all."
msgstr "Вот и всё!"

#: src/pages/catchup.jsx:1566
msgid "Back to top"
msgstr "Вернуться к началу"

#: src/pages/catchup.jsx:1597
msgid "Links shared by followings, sorted by shared counts, boosts and likes."
msgstr "Ссылки, которыми поделились подписчики, отсортированы по количеству взаимодействий, продвижений и отметок \"нравится\"."

#: src/pages/catchup.jsx:1603
msgid "Sort: Density"
msgstr "Сортировка: Плотность"

#: src/pages/catchup.jsx:1606
msgid "Posts are sorted by information density or depth. Shorter posts are \"lighter\" while longer posts are \"heavier\". Posts with photos are \"heavier\" than posts without photos."
msgstr "Посты сортируются по плотности или глубине информации. Более короткие сообщения \"легкие\", в то время как длинные сообщения \"тяжелее\". Сообщения с фотографиями \"тяжелее\", чем сообщения без фотографий."

#: src/pages/catchup.jsx:1613
msgid "Group: Authors"
msgstr "Группа: Авторы"

#: src/pages/catchup.jsx:1616
msgid "Posts are grouped by authors, sorted by posts count per author."
msgstr "Посты группируются по авторам, сортируются по количеству сообщений на автора."

#: src/pages/catchup.jsx:1663
msgid "Next author"
msgstr "Следующий автор"

#: src/pages/catchup.jsx:1671
msgid "Previous author"
msgstr "Предыдущий автор"

#: src/pages/catchup.jsx:1687
msgid "Scroll to top"
msgstr "Прокрутка к началу"

#: src/pages/favourites.jsx:28
msgid "No likes yet. Go like something!"
msgstr "Нет отметок \"нравится\". Лайкните что-нибудь!"

#: src/pages/favourites.jsx:29
msgid "Unable to load likes."
msgstr "Не удалось загрузить отметки \"нравится\"."

#: src/pages/filters.jsx:23
msgid "Home and lists"
msgstr "Домашняя лента и списки"

#: src/pages/filters.jsx:25
msgid "Public timelines"
msgstr "Публичные ленты"

#: src/pages/filters.jsx:26
msgid "Conversations"
msgstr "Диалоги"

#: src/pages/filters.jsx:27
msgid "Profiles"
msgstr "Профили"

#: src/pages/filters.jsx:42
msgid "Never"
msgstr "Никогда"

#: src/pages/filters.jsx:104
#: src/pages/filters.jsx:229
msgid "New filter"
msgstr "Новый фильтр"

#. placeholder {0}: filters.length
#: src/pages/filters.jsx:152
msgid "{0, plural, one {# filter} other {# filters}}"
msgstr "{0, plural, one {# фильтр} other {# фильтр(а/ов)}}"

#: src/pages/filters.jsx:167
msgid "Unable to load filters."
msgstr "Не удалось загрузить фильтры."

#: src/pages/filters.jsx:171
msgid "No filters yet."
msgstr "Пока нет фильтров."

#: src/pages/filters.jsx:178
msgid "Add filter"
msgstr "Добавить фильтр"

#: src/pages/filters.jsx:229
msgid "Edit filter"
msgstr "Редактировать фильтр"

#: src/pages/filters.jsx:346
msgid "Unable to edit filter"
msgstr "Не удалось изменить фильтр"

#: src/pages/filters.jsx:347
msgid "Unable to create filter"
msgstr "Не удалось создать фильтр"

#: src/pages/filters.jsx:356
msgid "Title"
msgstr "Заголовок"

#: src/pages/filters.jsx:397
msgid "Whole word"
msgstr "Слово целиком"

#: src/pages/filters.jsx:423
msgid "No keywords. Add one."
msgstr "Нет ключевых слов. Добавьте их."

#: src/pages/filters.jsx:450
msgid "Add keyword"
msgstr "Добавить ключевое слово"

#. placeholder {0}: filteredEditKeywords.length
#: src/pages/filters.jsx:454
msgid "{0, plural, one {# keyword} other {# keywords}}"
msgstr "{0, plural, one {# ключевое слово} other {# ключевых слов(а)}}"

#: src/pages/filters.jsx:467
msgid "Filter from…"
msgstr "Фильтр от…"

#: src/pages/filters.jsx:493
msgid "* Not implemented yet"
msgstr "* Ещё не реализовано"

#: src/pages/filters.jsx:499
msgid "Status: <0><1/></0>"
msgstr "Состояние: <0><1/></0>"

#: src/pages/filters.jsx:508
msgid "Change expiry"
msgstr "Изменить срок действия"

#: src/pages/filters.jsx:508
msgid "Expiry"
msgstr "Срок действия"

#: src/pages/filters.jsx:527
msgid "Filtered post will be…"
msgstr "Отфильтрованный пост будет…"

#: src/pages/filters.jsx:538
msgid "obscured (media only)"
msgstr "скрытый (только для медиа)"

#: src/pages/filters.jsx:552
msgid "minimized"
msgstr "свернуто"

#: src/pages/filters.jsx:562
msgid "hidden"
msgstr "скрыто"

#: src/pages/filters.jsx:579
msgid "Delete this filter?"
msgstr "Удалить этот фильтр?"

#: src/pages/filters.jsx:592
msgid "Unable to delete filter."
msgstr "Не удалось удалить фильтр."

#: src/pages/filters.jsx:625
msgid "Expired"
msgstr "Срок действия истёк"

#: src/pages/filters.jsx:627
msgid "Expiring <0/>"
msgstr "Истекает <0/>"

#: src/pages/filters.jsx:631
msgid "Never expires"
msgstr "Никогда не истекает"

#. placeholder {0}: followedHashtags.length
#: src/pages/followed-hashtags.jsx:71
msgid "{0, plural, one {# hashtag} other {# hashtags}}"
msgstr "{0, plural, one {# хэштег} other {# хэштеги}}"

#: src/pages/followed-hashtags.jsx:86
msgid "Unable to load followed hashtags."
msgstr "Не удалось загрузить отслеживаемые хэштеги."

#: src/pages/followed-hashtags.jsx:90
msgid "No hashtags followed yet."
msgstr "Вы не отслеживаете ни одного хэштега."

#: src/pages/following.jsx:147
msgid "Nothing to see here."
msgstr "Здесь пусто."

#: src/pages/following.jsx:148
#: src/pages/list.jsx:109
msgid "Unable to load posts."
msgstr "Не удалось загрузить посты."

#: src/pages/hashtag.jsx:56
msgid "{hashtagTitle} (Media only) on {instance}"
msgstr "{hashtagTitle} (только медиа) на {instance}"

#: src/pages/hashtag.jsx:57
msgid "{hashtagTitle} on {instance}"
msgstr "{hashtagTitle} на {instance}"

#: src/pages/hashtag.jsx:59
msgid "{hashtagTitle} (Media only)"
msgstr "{hashtagTitle} (только медиа)"

#: src/pages/hashtag.jsx:60
msgid "{hashtagTitle}"
msgstr "{hashtagTitle}"

#: src/pages/hashtag.jsx:185
msgid "No one has posted anything with this tag yet."
msgstr "Никто еще ничего не написал с этим тегом."

#: src/pages/hashtag.jsx:186
msgid "Unable to load posts with this tag"
msgstr "Не удается загрузить записи с этим тегом"

#: src/pages/hashtag.jsx:212
msgid "Unfollow #{hashtag}?"
msgstr "Отписаться от #{hashtag}?"

#: src/pages/hashtag.jsx:227
msgid "Unfollowed #{hashtag}"
msgstr "Отписаться от #{hashtag}"

#: src/pages/hashtag.jsx:242
msgid "Followed #{hashtag}"
msgstr "Подписаться на #{hashtag}"

#: src/pages/hashtag.jsx:258
msgid "Following…"
msgstr "Подписка…"

#: src/pages/hashtag.jsx:287
msgid "Unfeatured on profile"
msgstr "Не рекомендуется в профиле"

#: src/pages/hashtag.jsx:301
msgid "Unable to unfeature on profile"
msgstr "Не удалось отменить рекомендацию в профиле"

#: src/pages/hashtag.jsx:310
#: src/pages/hashtag.jsx:326
msgid "Featured on profile"
msgstr "Рекомендуется в профиле"

#: src/pages/hashtag.jsx:398
msgid "{TOTAL_TAGS_LIMIT, plural, other {Max # tags}}"
msgstr "{TOTAL_TAGS_LIMIT, plural, other {Предел - # тэг(а/ов)}}"

#: src/pages/hashtag.jsx:401
msgid "Add hashtag"
msgstr "Добавить хэштег"

#: src/pages/hashtag.jsx:433
msgid "Remove hashtag"
msgstr "Удалить хэштег"

#: src/pages/hashtag.jsx:447
msgid "{SHORTCUTS_LIMIT, plural, one {Max # shortcut reached. Unable to add shortcut.} other {Max # shortcuts reached. Unable to add shortcut.}}"
msgstr "{SHORTCUTS_LIMIT, plural, one {Достигнут предел в # ярлык. Не удалось добавить ярлык.} other {Достигнут предел в # ярлык(а/ов). Не удалось добавить ярлык.}}"

#: src/pages/hashtag.jsx:476
msgid "This shortcut already exists"
msgstr "Ярлык уже существует"

#: src/pages/hashtag.jsx:479
msgid "Hashtag shortcut added"
msgstr "Ярлык для хэштега добавлен"

#: src/pages/hashtag.jsx:485
msgid "Add to Shortcuts"
msgstr "Добавить в ярлыки"

#: src/pages/hashtag.jsx:491
#: src/pages/public.jsx:141
#: src/pages/trending.jsx:481
msgid "Enter a new instance e.g. \"mastodon.social\""
msgstr "Введите новый инстанс, например \"mastodon.social\""

#: src/pages/hashtag.jsx:494
#: src/pages/public.jsx:144
#: src/pages/trending.jsx:484
msgid "Invalid instance"
msgstr "Некорректный инстанс"

#: src/pages/hashtag.jsx:508
#: src/pages/public.jsx:158
#: src/pages/trending.jsx:496
msgid "Go to another instance…"
msgstr "Перейти к другому инстансу…"

#: src/pages/hashtag.jsx:521
#: src/pages/public.jsx:171
#: src/pages/trending.jsx:507
msgid "Go to my instance (<0>{currentInstance}</0>)"
msgstr "Переключиться на мой инстанс (<0>{currentInstance}</0>)"

#: src/pages/home.jsx:224
msgid "Unable to fetch notifications."
msgstr "Не удалось загрузить уведомления."

#: src/pages/home.jsx:245
msgid "<0>New</0> <1>Follow Requests</1>"
msgstr "<0>Новые</0> <1>запросы на подписку</1>"

#: src/pages/home.jsx:251
msgid "See all"
msgstr "Показать все"

#: src/pages/http-route.jsx:68
msgid "Resolving…"
msgstr "Определение…"

#: src/pages/http-route.jsx:79
msgid "Unable to resolve URL"
msgstr "Не удается определить URL"

#: src/pages/list.jsx:108
msgid "Nothing yet."
msgstr "Пока что ничего нет."

#: src/pages/list.jsx:201
#: src/pages/list.jsx:307
msgid "Manage members"
msgstr "Управление участниками"

#. placeholder {0}: account.username
#: src/pages/list.jsx:342
msgid "Remove <0>@{0}</0> from list?"
msgstr "Удалить <0>@{0}</0> из списка?"

#: src/pages/list.jsx:388
msgid "Remove…"
msgstr "Удалить…"

#. placeholder {0}: lists.length
#: src/pages/lists.jsx:115
msgid "{0, plural, one {# list} other {# lists}}"
msgstr "{0, plural, one {# список} other {# списк(а/ов)}}"

#: src/pages/lists.jsx:135
msgid "No lists yet."
msgstr "Пока нет списков."

#: src/pages/login.jsx:122
#: src/pages/login.jsx:132
msgid "Failed to register application"
msgstr "Не удалось зарегистрировать приложение"

#: src/pages/login.jsx:218
msgid "instance domain"
msgstr "домен инстанса"

#: src/pages/login.jsx:242
msgid "e.g. “mastodon.social”"
msgstr "например “mastodon.social”"

#: src/pages/login.jsx:253
msgid "Failed to log in. Please try again or try another instance."
msgstr "Не удалось войти. Пожалуйста, попробуйте еще раз или попробуйте другой инстанс."

#: src/pages/login.jsx:265
msgid "Continue with {selectedInstanceText}"
msgstr "Продолжить на {selectedInstanceText}"

#: src/pages/login.jsx:266
msgid "Continue"
msgstr "Продолжить"

#: src/pages/login.jsx:274
msgid "Don't have an account? Create one!"
msgstr "Нет учетной записи? Создайте её!"

#: src/pages/mentions.jsx:21
msgid "Private mentions"
msgstr "Личные упоминания"

#: src/pages/mentions.jsx:166
msgid "Private"
msgstr "Личные"

#: src/pages/mentions.jsx:176
msgid "No one mentioned you :("
msgstr "Никто не упомянул вас :("

#: src/pages/mentions.jsx:177
msgid "Unable to load mentions."
msgstr "Не удалось загрузить упоминания."

#: src/pages/notifications.jsx:108
msgid "You don't follow"
msgstr "Которых вы не читаете"

#: src/pages/notifications.jsx:109
msgid "Who don't follow you"
msgstr "Кто не подписан на вас"

#: src/pages/notifications.jsx:110
msgid "With a new account"
msgstr "С новой учетной записью"

#: src/pages/notifications.jsx:111
msgid "Who unsolicitedly private mention you"
msgstr "Кому нежелательно лично упоминать вас"

#: src/pages/notifications.jsx:112
msgid "Who are limited by server moderators"
msgstr "Кто ограничен модераторами сервера"

#: src/pages/notifications.jsx:605
#: src/pages/notifications.jsx:946
msgid "Notifications settings"
msgstr "Параметры уведомлений"

#: src/pages/notifications.jsx:623
msgid "New notifications"
msgstr "Новые уведомления"

#. placeholder {0}: announcements.length
#: src/pages/notifications.jsx:634
msgid "{0, plural, one {Announcement} other {Announcements}}"
msgstr "{0, plural, one {Оповещение} other {Оповещени(я/й)}}"

#: src/pages/notifications.jsx:681
#: src/pages/settings.jsx:1206
msgid "Follow requests"
msgstr "Запросы на подписку"

#. placeholder {0}: followRequests.length
#: src/pages/notifications.jsx:686
msgid "{0, plural, one {# follow request} other {# follow requests}}"
msgstr "{0, plural, one {# запрос на подписку} other {# запрос(а/ов) на подписку}}"

#. placeholder {0}: notificationsPolicy.summary.pendingRequestsCount
#: src/pages/notifications.jsx:741
msgid "{0, plural, one {Filtered notifications from # person} other {Filtered notifications from # people}}"
msgstr "{0, plural, one {Отфильтрованные уведомления от # человека} other {Отфильтрованные уведомления от # человек}}"

#: src/pages/notifications.jsx:814
msgid "Only mentions"
msgstr "Только упоминания"

#: src/pages/notifications.jsx:818
msgid "Today"
msgstr "Сегодня"

#: src/pages/notifications.jsx:823
msgid "You're all caught up."
msgstr "Вы уловили всё."

#: src/pages/notifications.jsx:846
msgid "Yesterday"
msgstr "Вчера"

#: src/pages/notifications.jsx:894
msgid "Unable to load notifications"
msgstr "Не удалось загрузить уведомления"

#: src/pages/notifications.jsx:973
msgid "Notifications settings updated"
msgstr "Параметры уведомлений обновлены"

#: src/pages/notifications.jsx:981
msgid "Filter out notifications from people:"
msgstr "Отфильтровать уведомления от людей:"

#: src/pages/notifications.jsx:995
msgid "Filter"
msgstr "Фильтр"

#: src/pages/notifications.jsx:998
msgid "Ignore"
msgstr "Игнорировать"

#. placeholder {0}: niceDateTime(updatedAtDate)
#: src/pages/notifications.jsx:1071
msgid "Updated <0>{0}</0>"
msgstr "Обновлено <0>{0}</0>"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1139
msgid "View notifications from <0>@{0}</0>"
msgstr "Просмотр уведомлений от <0>@{0}</0>"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1160
msgid "Notifications from <0>@{0}</0>"
msgstr "Уведомления от <0>@{0}</0>"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1228
msgid "Notifications from @{0} will not be filtered from now on."
msgstr "Уведомления от @{0} теперь не будут отфильтрованы."

#: src/pages/notifications.jsx:1233
msgid "Unable to accept notification request"
msgstr "Не удалось принять запрос на уведомление"

#: src/pages/notifications.jsx:1238
msgid "Allow"
msgstr "Разрешить"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1258
msgid "Notifications from @{0} will not show up in Filtered notifications from now on."
msgstr "Уведомления от @{0} теперь не будут отображаться в отфильтрованных уведомлениях."

#: src/pages/notifications.jsx:1263
msgid "Unable to dismiss notification request"
msgstr "Не удалось отклонить запрос на уведомление"

#: src/pages/notifications.jsx:1268
msgid "Dismiss"
msgstr "Отклонить"

#: src/pages/notifications.jsx:1283
msgid "Dismissed"
msgstr "Отклонено"

#: src/pages/public.jsx:28
msgid "Local timeline ({instance})"
msgstr "Локальная лента ({instance})"

#: src/pages/public.jsx:29
msgid "Federated timeline ({instance})"
msgstr "Федеративная лента ({instance})"

#: src/pages/public.jsx:92
msgid "Local timeline"
msgstr "Локальная лента"

#: src/pages/public.jsx:92
msgid "Federated timeline"
msgstr "Федеративная лента"

#: src/pages/public.jsx:98
msgid "No one has posted anything yet."
msgstr "Никто ещё ничего не опубликовал."

#: src/pages/public.jsx:125
msgid "Switch to Federated"
msgstr "Переключиться на Федеративную"

#: src/pages/public.jsx:132
msgid "Switch to Local"
msgstr "Переключиться на локальную"

#: src/pages/scheduled-posts.jsx:110
msgid "No scheduled posts."
msgstr "Нет отложенных постов"

#. Scheduled [in 1 day] ([Thu, Feb 27, 6:30:00 PM])
#. placeholder {0}: niceDateTime(scheduledAt, { formatOpts: { weekday: 'short', second: 'numeric', }, })
#: src/pages/scheduled-posts.jsx:207
msgid "Scheduled <0><1/></0> <2>({0})</2>"
msgstr "Запланировано <0><1/></0> <2>({0})</2>"

#. Scheduled [in 1 day]
#: src/pages/scheduled-posts.jsx:263
msgid "Scheduled <0><1/></0>"
msgstr "Запланировано <0><1/></0>"

#: src/pages/scheduled-posts.jsx:308
msgid "Scheduled post rescheduled"
msgstr "Запланированный пост перепланирован"

#: src/pages/scheduled-posts.jsx:315
msgid "Failed to reschedule post"
msgstr "Не удалось перенести пост"

#: src/pages/scheduled-posts.jsx:338
msgid "Reschedule"
msgstr "Перепланировать"

#: src/pages/scheduled-posts.jsx:344
msgid "Delete scheduled post?"
msgstr "Удалить запланированный пост?"

#: src/pages/scheduled-posts.jsx:352
msgid "Scheduled post deleted"
msgstr "Запланированный пост удален"

#: src/pages/scheduled-posts.jsx:359
msgid "Failed to delete scheduled post"
msgstr "Не удалось удалить запланированный пост"

#: src/pages/search.jsx:50
msgid "Search: {q} (Posts)"
msgstr "Поиск: {q} (Посты)"

#: src/pages/search.jsx:53
msgid "Search: {q} (Accounts)"
msgstr "Поиск: {q} (Учетные записи)"

#: src/pages/search.jsx:56
msgid "Search: {q} (Hashtags)"
msgstr "Поиск: {q} (Хэштеги)"

#: src/pages/search.jsx:59
msgid "Search: {q}"
msgstr "Поиск: {q}"

#: src/pages/search.jsx:339
#: src/pages/search.jsx:421
msgid "Hashtags"
msgstr "Хэштеги"

#: src/pages/search.jsx:371
#: src/pages/search.jsx:425
#: src/pages/search.jsx:495
msgid "See more"
msgstr "Показать больше"

#: src/pages/search.jsx:397
msgid "See more accounts"
msgstr "Показать больше учётных записей"

#: src/pages/search.jsx:411
msgid "No accounts found."
msgstr "Учетные записи не найдены."

#: src/pages/search.jsx:467
msgid "See more hashtags"
msgstr "Показать больше хэштегов"

#: src/pages/search.jsx:481
msgid "No hashtags found."
msgstr "Хэштеги не найдены."

#: src/pages/search.jsx:525
msgid "See more posts"
msgstr "Показать больше постов"

#: src/pages/search.jsx:539
msgid "No posts found."
msgstr "Публикации не найдены."

#: src/pages/search.jsx:583
msgid "Enter your search term or paste a URL above to get started."
msgstr "Введите слово для поиска или вставьте URL выше, чтобы начать."

#: src/pages/settings.jsx:95
msgid "Settings"
msgstr "Параметры"

#: src/pages/settings.jsx:104
msgid "Appearance"
msgstr "Тема"

#: src/pages/settings.jsx:180
msgid "Light"
msgstr "Светлая"

#: src/pages/settings.jsx:191
msgid "Dark"
msgstr "Тёмная"

#: src/pages/settings.jsx:204
msgid "Auto"
msgstr "Устройство"

#: src/pages/settings.jsx:214
msgid "Text size"
msgstr "Размер текста"

#. Preview of one character, in smallest size
#. Preview of one character, in largest size
#: src/pages/settings.jsx:219
#: src/pages/settings.jsx:244
msgid "A"
msgstr "A"

#: src/pages/settings.jsx:258
msgid "Display language"
msgstr "Язык интерфейса"

#: src/pages/settings.jsx:267
msgid "Volunteer translations"
msgstr "Вы можете помочь с переводом"

#: src/pages/settings.jsx:278
msgid "Posting"
msgstr "Публикация сообщений"

#: src/pages/settings.jsx:285
msgid "Default visibility"
msgstr "Видимость поста по умолчанию"

#: src/pages/settings.jsx:286
#: src/pages/settings.jsx:332
msgid "Synced"
msgstr "Синхронизировано"

#: src/pages/settings.jsx:311
msgid "Failed to update posting privacy"
msgstr "Не удалось обновить информацию о постах"

#: src/pages/settings.jsx:334
msgid "Synced to your instance server's settings. <0>Go to your instance ({instance}) for more settings.</0>"
msgstr "Синхронизировано с настройками сервера инстанса. <0>Перейдите к инстансу ({instance}) для дополнительных настроек.</0>"

#: src/pages/settings.jsx:349
msgid "Experiments"
msgstr "Э-э-эксперименты"

#: src/pages/settings.jsx:362
msgid "Auto refresh timeline posts"
msgstr "Автоматически обновлять ленту с постами"

#: src/pages/settings.jsx:374
msgid "Boosts carousel"
msgstr "Карусель продвижений"

#: src/pages/settings.jsx:391
msgid "Post translation"
msgstr "Перевод поста"

#: src/pages/settings.jsx:402
msgid "Translate to "
msgstr "Перевести на "

#: src/pages/settings.jsx:413
msgid "System language ({systemTargetLanguageText})"
msgstr "Системный язык ({systemTargetLanguageText})"

#. placeholder {0}: snapStates.settings.contentTranslationHideLanguages .length
#: src/pages/settings.jsx:439
msgid "{0, plural, =0 {Hide \"Translate\" button for:} other {Hide \"Translate\" button for (#):}}"
msgstr "{0, plural, =0 {Скрыть кнопку \"Перевести\" для:} other {Скрыть кнопку \"Перевести\" для (#):}}"

#: src/pages/settings.jsx:494
msgid "Note: This feature uses external translation services, powered by <0>{TRANSLATION_API_NAME}</0>."
msgstr "Примечание: Эта функция использует внешние службы перевода, на основе <0>{TRANSLATION_API_NAME}</0>."

#: src/pages/settings.jsx:522
msgid "Auto inline translation"
msgstr "Автоматический встроенный перевод"

#: src/pages/settings.jsx:526
msgid "Automatically show translation for posts in timeline. Only works for <0>short</0> posts without content warning, media and poll."
msgstr "Автоматически показывать перевод постов на ленте. Работает только для <0>коротких</0> постов без предупреждения о содержимом, медиа и опросов."

#: src/pages/settings.jsx:547
msgid "GIF Picker for composer"
msgstr "Выбор GIF для исполнителя"

#: src/pages/settings.jsx:551
msgid "Note: This feature uses external GIF search service, powered by <0>GIPHY</0>. G-rated (suitable for viewing by all ages), tracking parameters are stripped, referrer information is omitted from requests, but search queries and IP address information will still reach their servers."
msgstr "Примечание: Эта функция использует внешний сервис поиска GIF, работающий на <0>GIPHY</0>. Имеет рейтинг G (подходит для просмотра всем возрастам), параметры отслеживания удалены, информация о использовании отсутствует в запросах, но поисковые запросы и информация об IP-адресе все равно будут поступать на их серверы."

#: src/pages/settings.jsx:580
msgid "Image description generator"
msgstr "Генератор описания изображения"

#: src/pages/settings.jsx:585
msgid "Only for new images while composing new posts."
msgstr "Только для новых изображений при написании новых постов."

#: src/pages/settings.jsx:592
msgid "Note: This feature uses external AI service, powered by <0>img-alt-api</0>. May not work well. Only for images and in English."
msgstr "Примечание: Эта функция использует внешний сервис искусственного интеллекта, работающий на <0>img-alt-api</0>. Может работать некорректно. Только для изображений и на английском языке."

#: src/pages/settings.jsx:618
msgid "Server-side grouped notifications"
msgstr "Группировка уведомлений на стороне сервера"

#: src/pages/settings.jsx:622
msgid "Alpha-stage feature. Potentially improved grouping window but basic grouping logic."
msgstr "Функция находится в состоянии альфа. Потенциально улучшает группировку, однако логика группировки базовая."

#: src/pages/settings.jsx:643
msgid "\"Cloud\" import/export for shortcuts settings"
msgstr "Облачный \"импорт/экспорт\" для настроек ярлыков"

#: src/pages/settings.jsx:648
msgid "⚠️⚠️⚠️ Very experimental.<0/>Stored in your own profile’s notes. Profile (private) notes are mainly used for other profiles, and hidden for own profile."
msgstr "⚠️⚠️⚠️ Очень экспериментально.<0/>Хранится в заметках вашего профиля. Личные заметки (которые в профиле) в основном используются для других профилей и скрыты для собственного профиля."

#: src/pages/settings.jsx:659
msgid "Note: This feature uses currently-logged-in instance server API."
msgstr "Примечание: Эта функция использует текущий авторизованный API сервера инстанса."

#: src/pages/settings.jsx:676
msgid "Cloak mode <0>(<1>Text</1> → <2>████</2>)</0>"
msgstr "Режим маскировки <0>(<1>Текст</1> → <2>████</2>)</0>"

#: src/pages/settings.jsx:685
msgid "Replace text as blocks, useful when taking screenshots, for privacy reasons."
msgstr "Замените текст блоками, полезными для изготовления скриншотов по соображениям конфиденциальности."

#: src/pages/settings.jsx:710
msgid "About"
msgstr "О приложении"

#: src/pages/settings.jsx:749
msgid "<0>Built</0> by <1>@cheeaun</1>"
msgstr "<0>Собрано</0> <1>@cheeaun</1>"

#: src/pages/settings.jsx:778
msgid "Sponsor"
msgstr "Стать спонсором"

#: src/pages/settings.jsx:786
msgid "Donate"
msgstr "Поддержать монеткой"

#: src/pages/settings.jsx:802
msgid "What's new"
msgstr "Что нового"

#: src/pages/settings.jsx:806
msgid "Privacy Policy"
msgstr "Политика конфиденциальности"

#. placeholder {0}: WEBSITE.replace(/https?:\/\//g, '').replace(/\/$/, '')
#: src/pages/settings.jsx:813
msgid "<0>Site:</0> {0}"
msgstr "<0>Сайт:</0> {0}"

#. placeholder {0}: !__FAKE_COMMIT_HASH__ && ( <span class="ib insignificant"> ( <a href={`https://github.com/cheeaun/phanpy/commit/${__COMMIT_HASH__}`} target="_blank" rel="noopener" > <RelativeTime datetime={new Date(__BUILD_TIME__)} /> </a> ) </span> )
#: src/pages/settings.jsx:820
msgid "<0>Version:</0> <1/> {0}"
msgstr "<0>Версия:</0> <1/> {0}"

#: src/pages/settings.jsx:835
msgid "Version string copied"
msgstr "Строка версии скопирована"

#: src/pages/settings.jsx:838
msgid "Unable to copy version string"
msgstr "Не удалось скопировать строку версии"

#: src/pages/settings.jsx:1103
#: src/pages/settings.jsx:1108
msgid "Failed to update subscription. Please try again."
msgstr "Не удалось обновить подписку. Пожалуйста, попробуйте еще раз."

#: src/pages/settings.jsx:1114
msgid "Failed to remove subscription. Please try again."
msgstr "Не удалось удалить подписку. Пожалуйста, попробуйте еще раз."

#: src/pages/settings.jsx:1121
msgid "Push Notifications (beta)"
msgstr "Push-уведомления (бета)"

#: src/pages/settings.jsx:1143
msgid "Push notifications are blocked. Please enable them in your browser settings."
msgstr "Push-уведомления заблокированы. Пожалуйста, включите их в настройках браузера."

#. placeholder {0}: [ { value: 'all', label: t`anyone`, }, { value: 'followed', label: t`people I follow`, }, { value: 'follower', label: t`followers`, }, ].map((type) => ( <option value={type.value}>{type.label}</option> ))
#: src/pages/settings.jsx:1152
msgid "Allow from <0>{0}</0>"
msgstr "Разрешить от <0>{0}</0>"

#: src/pages/settings.jsx:1161
msgid "anyone"
msgstr "кто угодно"

#: src/pages/settings.jsx:1165
msgid "people I follow"
msgstr "людей, на которых я подписан(а)"

#: src/pages/settings.jsx:1169
msgid "followers"
msgstr "подписчиков"

#: src/pages/settings.jsx:1202
msgid "Follows"
msgstr "Подписки"

#: src/pages/settings.jsx:1210
msgid "Polls"
msgstr "Опросы"

#: src/pages/settings.jsx:1214
msgid "Post edits"
msgstr "Редактирование постов"

#: src/pages/settings.jsx:1235
msgid "Push permission was not granted since your last login. You'll need to <0><1>log in</1> again to grant push permission</0>."
msgstr "Разрешение для Push не было предоставлено с момента вашего последнего входа в систему. Чтобы предоставить разрешение на push-уведомление, вам нужно будет <0><1>войти</1> еще раз</0>."

#: src/pages/settings.jsx:1251
msgid "NOTE: Push notifications only work for <0>one account</0>."
msgstr "ПРИМЕЧАНИЕ: Push-уведомления работают только для <0>одного аккаунта</0>."

#. js-lingui-explicit-id
#: src/pages/status.jsx:657
#: src/pages/status.jsx:1220
msgid "post.title"
msgstr "Пост"

#: src/pages/status.jsx:911
msgid "You're not logged in. Interactions (reply, boost, etc) are not possible."
msgstr "Вы не вошли в систему. Взаимодействия (ответы, продвижения и т. п.) невозможны."

#: src/pages/status.jsx:931
msgid "This post is from another instance (<0>{instance}</0>). Interactions (reply, boost, etc) are not possible."
msgstr "Это сообщение с другого инстанса (<0>{instance}</0>). Взаимодействия (ответ, продвижение и т. д.) невозможны."

#: src/pages/status.jsx:959
msgid "Error: {e}"
msgstr "Ошибка: {e}"

#: src/pages/status.jsx:966
msgid "Switch to my instance to enable interactions"
msgstr "Переключиться на мой инстанс для включения взаимодействий"

#: src/pages/status.jsx:1068
msgid "Unable to load replies."
msgstr "Не удалось загрузить ответы."

#: src/pages/status.jsx:1180
msgid "Back"
msgstr "Назад"

#: src/pages/status.jsx:1211
msgid "Go to main post"
msgstr "Перейти к главному посту"

#. placeholder {0}: ancestors.length
#: src/pages/status.jsx:1234
msgid "{0} posts above ‒ Go to top"
msgstr "{0} постов выше - Перейти к началу"

#: src/pages/status.jsx:1281
#: src/pages/status.jsx:1344
msgid "Switch to Side Peek view"
msgstr "Переключиться на боковую панель"

#: src/pages/status.jsx:1345
msgid "Switch to Full view"
msgstr "Переключиться на полный вид"

#: src/pages/status.jsx:1363
msgid "Show all sensitive content"
msgstr "Показать весь деликатный контент"

#: src/pages/status.jsx:1368
msgid "Experimental"
msgstr "Экспериментальные"

#: src/pages/status.jsx:1377
msgid "Unable to switch"
msgstr "Не удалось переключиться"

#. placeholder {0}: punycode.toUnicode( postInstance, )
#: src/pages/status.jsx:1384
msgid "Switch to post's instance ({0})"
msgstr "Переключиться на инстанс поста ({0})"

#: src/pages/status.jsx:1387
msgid "Switch to post's instance"
msgstr "Переключиться на инстанс поста"

#: src/pages/status.jsx:1445
msgid "Unable to load post"
msgstr "Не удалось загрузить пост"

#. placeholder {0}: replies.length
#. placeholder {1}: shortenNumber(replies.length)
#: src/pages/status.jsx:1581
msgid "{0, plural, one {# reply} other {<0>{1}</0> replies}}"
msgstr "{0, plural, one {# ответ} other {<0>{1}</0> ответ(а/ов)}}"

#. placeholder {0}: shortenNumber(totalComments)
#: src/pages/status.jsx:1599
msgid "{totalComments, plural, one {# comment} other {<0>{0}</0> comments}}"
msgstr "{totalComments, plural, one {# комментарий} other {<0>{0}</0> комментари(я/ев)}}"

#: src/pages/status.jsx:1621
msgid "View post with its replies"
msgstr "Просмотреть пост и ответы на него"

#: src/pages/trending.jsx:77
msgid "Trending ({instance})"
msgstr "Популярное ({instance})"

#: src/pages/trending.jsx:235
msgid "Trending News"
msgstr "Популярные новости"

#. By [Author]
#. placeholder {0}: author ? ( <NameText account={author} showAvatar /> ) : authorUrl ? ( <a href={authorUrl} target="_blank" rel="noopener" > {authorName} </a> ) : ( authorName )
#: src/pages/trending.jsx:350
msgid "By {0}"
msgstr "От {0}"

#: src/pages/trending.jsx:411
msgid "Back to showing trending posts"
msgstr "Назад к показу популярных сообщений"

#. placeholder {0}: currentLink .replace(/^https?:\/\/(www\.)?/i, '') .replace(/\/$/, '')
#: src/pages/trending.jsx:416
msgid "Showing posts mentioning <0>{0}</0>"
msgstr "Сообщения, упоминающие <0>{0}</0>"

#: src/pages/trending.jsx:428
msgid "Trending posts"
msgstr "Популярные посты"

#: src/pages/trending.jsx:451
msgid "No trending posts."
msgstr "Нет популярных постов."

#: src/pages/welcome.jsx:54
msgid "A minimalistic opinionated Mastodon web client."
msgstr "Минималистичный веб-клиент Mastodon со своим видением интерфейса."

#: src/pages/welcome.jsx:65
msgid "Log in with Mastodon"
msgstr "Войти через Mastodon"

#: src/pages/welcome.jsx:71
msgid "Sign up"
msgstr "Регистрация"

#: src/pages/welcome.jsx:78
msgid "Connect your existing Mastodon/Fediverse account.<0/>Your credentials are not stored on this server."
msgstr "Подключите ваш существующий аккаунт Mastodon/Fediverse.<0/>Ваши учетные данные не хранятся на этом сервере."

#: src/pages/welcome.jsx:95
msgid "<0>Built</0> by <1>@cheeaun</1>. <2>Privacy Policy</2>."
msgstr "<0>Собран</0> <1>@cheeaun</1>. <2>Политика конфиденциальности</2>."

#: src/pages/welcome.jsx:126
msgid "Screenshot of Boosts Carousel"
msgstr "Скриншот Карусели продвижений"

#: src/pages/welcome.jsx:130
msgid "Boosts Carousel"
msgstr "Карусель продвижений"

#: src/pages/welcome.jsx:133
msgid "Visually separate original posts and re-shared posts (boosted posts)."
msgstr "Визуально разделять оригинальные посты и повторно распределяемые посты (посты, которые были продвинуты)."

#: src/pages/welcome.jsx:142
msgid "Screenshot of nested comments thread"
msgstr "Скриншот вложенных комментариев обсуждения"

#: src/pages/welcome.jsx:146
msgid "Nested comments thread"
msgstr "Вложенные комментарии обсуждения"

#: src/pages/welcome.jsx:149
msgid "Effortlessly follow conversations. Semi-collapsible replies."
msgstr "Легко следить за разговорами. Сворачиваемые ответы."

#: src/pages/welcome.jsx:157
msgid "Screenshot of grouped notifications"
msgstr "Скриншот сгруппированных уведомлений"

#: src/pages/welcome.jsx:161
msgid "Grouped notifications"
msgstr "Сгруппированные уведомления"

#: src/pages/welcome.jsx:164
msgid "Similar notifications are grouped and collapsed to reduce clutter."
msgstr "Похожие уведомления сгруппированы и свернуты для сокращения путаницы."

#: src/pages/welcome.jsx:173
msgid "Screenshot of multi-column UI"
msgstr "Скриншот многоколоночного интерфейса"

#: src/pages/welcome.jsx:177
msgid "Single or multi-column"
msgstr "Одна или несколько колонок"

#: src/pages/welcome.jsx:180
msgid "By default, single column for zen-mode seekers. Configurable multi-column for power users."
msgstr "По умолчанию один столбец для искателей в режиме дзен. Настраиваемая многоколоночная система для опытных пользователей."

#: src/pages/welcome.jsx:189
msgid "Screenshot of multi-hashtag timeline with a form to add more hashtags"
msgstr "Скриншот мульти-хэштег ленты с формой, чтобы добавить больше хэштегов"

#: src/pages/welcome.jsx:193
msgid "Multi-hashtag timeline"
msgstr "Мульти-хэштег лента"

#: src/pages/welcome.jsx:196
msgid "Up to 5 hashtags combined into a single timeline."
msgstr "До 5 хэштегов, объединенных в одну ленту."

#: src/utils/open-compose.js:24
msgid "Looks like your browser is blocking popups."
msgstr "Похоже, что ваш браузер блокирует всплывающие окна."

#: src/utils/show-compose.js:16
msgid "A draft post is currently minimized. Post or discard it before creating a new one."
msgstr "Черновик сообщения в настоящее время минимизирован. Отправьте или удалите его перед созданием нового."

#: src/utils/show-compose.js:21
msgid "A post is currently open. Post or discard it before creating a new one."
msgstr "В настоящее время открыт пост. Опубликуйте его или отмените его перед созданием нового."

