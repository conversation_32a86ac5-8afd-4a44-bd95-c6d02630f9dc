msgid ""
msgstr ""
"POT-Creation-Date: 2025-05-03 21:08+0800\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: nl\n"
"Project-Id-Version: phanpy\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-07-15 09:08\n"
"Last-Translator: \n"
"Language-Team: Dutch\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: phanpy\n"
"X-Crowdin-Project-ID: 703337\n"
"X-Crowdin-Language: nl\n"
"X-Crowdin-File: /main/src/locales/en.po\n"
"X-Crowdin-File-ID: 18\n"

#: src/components/account-block.jsx:137
msgid "Locked"
msgstr "Vergrendeld"

#. placeholder {0}: shortenNumber(statusesCount)
#: src/components/account-block.jsx:143
msgid "Posts: {0}"
msgstr "Berichten: {0}"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-block.jsx:148
msgid "Last posted: {0}"
msgstr "Laatst geplaatst: {0}"

#: src/components/account-block.jsx:163
#: src/components/account-info.jsx:710
msgid "Automated"
msgstr "Geautomatiseerd"

#: src/components/account-block.jsx:170
#: src/components/account-info.jsx:715
#: src/components/status.jsx:665
msgid "Group"
msgstr "Groep"

#: src/components/account-block.jsx:180
msgid "Mutual"
msgstr "Wederzijds"

#: src/components/account-block.jsx:184
#: src/components/account-info.jsx:1875
msgid "Requested"
msgstr "Aangevraagd"

#: src/components/account-block.jsx:188
#: src/components/account-info.jsx:1866
msgid "Following"
msgstr "Volgend"

#: src/components/account-block.jsx:192
#: src/components/account-info.jsx:1180
msgid "Follows you"
msgstr "Volgt jou"

#: src/components/account-block.jsx:200
msgid "{followersCount, plural, one {# follower} other {# followers}}"
msgstr "{followersCount, plural, one {# volger} other {# volgers}}"

#: src/components/account-block.jsx:209
#: src/components/account-info.jsx:758
msgid "Verified"
msgstr "Geverifieerd"

#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#: src/components/account-block.jsx:224
#: src/components/account-info.jsx:860
msgid "Joined <0>{0}</0>"
msgstr "Lid geworden op <0>{0}</0>"

#: src/components/account-info.jsx:63
msgid "Forever"
msgstr "Altijd"

#: src/components/account-info.jsx:403
msgid "Unable to load account."
msgstr "Kan account niet laden."

#: src/components/account-info.jsx:418
msgid "Go to account page"
msgstr "Ga naar de accountpagina"

#: src/components/account-info.jsx:447
#: src/components/account-info.jsx:780
#: src/components/account-info.jsx:810
msgid "Followers"
msgstr "Volgers"

#. js-lingui-explicit-id
#: src/components/account-info.jsx:451
#: src/components/account-info.jsx:820
#: src/components/account-info.jsx:839
msgid "following.stats"
msgstr ""

#: src/components/account-info.jsx:454
#: src/components/account-info.jsx:856
#: src/pages/account-statuses.jsx:487
#: src/pages/search.jsx:344
#: src/pages/search.jsx:491
msgid "Posts"
msgstr "Berichten"

#: src/components/account-info.jsx:462
#: src/components/account-info.jsx:1236
#: src/components/compose.jsx:2792
#: src/components/media-alt-modal.jsx:55
#: src/components/media-modal.jsx:363
#: src/components/status.jsx:1915
#: src/components/status.jsx:1932
#: src/components/status.jsx:2057
#: src/components/status.jsx:2686
#: src/components/status.jsx:2689
#: src/pages/account-statuses.jsx:531
#: src/pages/accounts.jsx:118
#: src/pages/hashtag.jsx:203
#: src/pages/list.jsx:171
#: src/pages/public.jsx:116
#: src/pages/scheduled-posts.jsx:89
#: src/pages/status.jsx:1305
#: src/pages/trending.jsx:474
msgid "More"
msgstr "meer"

#: src/components/account-info.jsx:474
msgid "<0>{displayName}</0> has indicated that their new account is now:"
msgstr "<0>{displayName}</0> heeft aangegeven dat ze een nieuw account hebben:"

#: src/components/account-info.jsx:619
#: src/components/account-info.jsx:1464
msgid "Handle copied"
msgstr "Kenmerk gekopieerd"

#: src/components/account-info.jsx:622
#: src/components/account-info.jsx:1467
msgid "Unable to copy handle"
msgstr "Kan dit kenmerk niet kopiëren"

#: src/components/account-info.jsx:628
#: src/components/account-info.jsx:1473
msgid "Copy handle"
msgstr "Kopieer kenmerk"

#: src/components/account-info.jsx:634
msgid "Go to original profile page"
msgstr "Ga naar de oorspronkelijke profielpagina"

#: src/components/account-info.jsx:652
msgid "View profile image"
msgstr "Bekijk profielfoto"

#: src/components/account-info.jsx:670
msgid "View profile header"
msgstr "Bekijk profielkop"

#: src/components/account-info.jsx:686
#: src/components/account-info.jsx:1774
#: src/components/account-info.jsx:2300
msgid "Edit profile"
msgstr "Bewerk profiel"

#: src/components/account-info.jsx:705
msgid "In Memoriam"
msgstr "In Memoriam"

#: src/components/account-info.jsx:787
#: src/components/account-info.jsx:830
msgid "This user has chosen to not make this information available."
msgstr "Deze gebruiker heeft ervoor gekozen om deze informatie niet beschikbaar te maken."

#. placeholder {0}: ( postingStats.originals / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {1}: ( postingStats.replies / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {2}: ( postingStats.boosts / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#: src/components/account-info.jsx:885
msgid "{0} original posts, {1} replies, {2} boosts"
msgstr "{0} originele berichten, {1} antwoorden, {2} boosts"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.daysSinceLastPost
#. placeholder {2}: postingStats.daysSinceLastPost
#. placeholder {3}: postingStats.daysSinceLastPost
#. placeholder {4}: postingStats.total
#. placeholder {5}: postingStats.total
#. placeholder {6}: postingStats.daysSinceLastPost
#: src/components/account-info.jsx:901
msgid "{0, plural, one {{1, plural, one {Last 1 post in the past 1 day} other {Last 1 post in the past {2} days}}} other {{3, plural, one {Last {4} posts in the past 1 day} other {Last {5} posts in the past {6} days}}}}"
msgstr "{0, plural, one {{1, plural, one {Laatste bericht van de afgelopen dag} other {Laatste 1 bericht van de afgelopen {2} dagen}}} other {{3, plural, one {Laatste {4} berichten van de afgelopen dag} other {Laatste {5} berichten van de afgelopen {6} dagen}}}}"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.total
#: src/components/account-info.jsx:917
msgid "{0, plural, one {Last 1 post in the past year(s)} other {Last {1} posts in the past year(s)}}"
msgstr "{0, plural, one {Laatste bericht van de afgelopen jaren} other {Laatste {1} berichten van de afgelopen jaren}}"

#: src/components/account-info.jsx:942
#: src/pages/catchup.jsx:70
msgid "Original"
msgstr "Origineel"

#: src/components/account-info.jsx:946
#: src/components/status.jsx:2470
#: src/pages/catchup.jsx:71
#: src/pages/catchup.jsx:1448
#: src/pages/catchup.jsx:2061
#: src/pages/status.jsx:1024
#: src/pages/status.jsx:1651
msgid "Replies"
msgstr "Antwoorden"

#: src/components/account-info.jsx:950
#: src/pages/catchup.jsx:72
#: src/pages/catchup.jsx:1450
#: src/pages/catchup.jsx:2073
#: src/pages/settings.jsx:1198
msgid "Boosts"
msgstr "Boosts"

#: src/components/account-info.jsx:956
msgid "Post stats unavailable."
msgstr "Berichtstatistieken niet beschikbaar."

#: src/components/account-info.jsx:987
msgid "View post stats"
msgstr "Bekijk berichtstatistieken"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-info.jsx:1184
msgid "Last post: <0>{0}</0>"
msgstr "Laatste bericht: <0>{0}</0>"

#: src/components/account-info.jsx:1198
msgid "Muted"
msgstr "Gedempt"

#: src/components/account-info.jsx:1203
msgid "Blocked"
msgstr "Geblokkeerd"

#: src/components/account-info.jsx:1212
msgid "Private note"
msgstr ""

#: src/components/account-info.jsx:1269
msgid "Mention <0>@{username}</0>"
msgstr "Noem <0>@{username}</0>"

#: src/components/account-info.jsx:1281
msgid "Translate bio"
msgstr "Vertaal bio"

#: src/components/account-info.jsx:1292
msgid "Edit private note"
msgstr ""

#: src/components/account-info.jsx:1292
msgid "Add private note"
msgstr ""

#: src/components/account-info.jsx:1312
msgid "Notifications enabled for @{username}'s posts."
msgstr ""

#: src/components/account-info.jsx:1313
msgid " Notifications disabled for @{username}'s posts."
msgstr ""

#: src/components/account-info.jsx:1325
msgid "Disable notifications"
msgstr ""

#: src/components/account-info.jsx:1326
msgid "Enable notifications"
msgstr ""

#: src/components/account-info.jsx:1343
msgid "Boosts from @{username} enabled."
msgstr ""

#: src/components/account-info.jsx:1344
msgid "Boosts from @{username} disabled."
msgstr ""

#: src/components/account-info.jsx:1355
msgid "Disable boosts"
msgstr ""

#: src/components/account-info.jsx:1355
msgid "Enable boosts"
msgstr ""

#: src/components/account-info.jsx:1374
msgid "@{username} is no longer featured on your profile."
msgstr ""

#: src/components/account-info.jsx:1384
msgid "@{username} is now featured on your profile."
msgstr ""

#: src/components/account-info.jsx:1392
msgid "Unable to unfeature @{username} on your profile."
msgstr ""

#: src/components/account-info.jsx:1396
msgid "Unable to feature @{username} on your profile."
msgstr ""

#: src/components/account-info.jsx:1405
msgid "Don't feature on profile"
msgstr ""

#: src/components/account-info.jsx:1406
#: src/pages/hashtag.jsx:333
msgid "Feature on profile"
msgstr ""

#: src/components/account-info.jsx:1415
msgid "Show featured profiles"
msgstr ""

#: src/components/account-info.jsx:1430
#: src/components/account-info.jsx:1440
#: src/components/account-info.jsx:2046
msgid "Add/Remove from Lists"
msgstr "Toevoegen/Verwijderen uit lijsten"

#: src/components/account-info.jsx:1490
#: src/components/status.jsx:1337
msgid "Link copied"
msgstr "Link gekopieerd"

#: src/components/account-info.jsx:1493
#: src/components/status.jsx:1340
msgid "Unable to copy link"
msgstr "Kan deze link niet kopiëren"

#: src/components/account-info.jsx:1499
#: src/components/shortcuts-settings.jsx:1059
#: src/components/status.jsx:1346
#: src/components/status.jsx:3464
msgid "Copy"
msgstr "Kopiëer"

#: src/components/account-info.jsx:1514
#: src/components/shortcuts-settings.jsx:1077
#: src/components/status.jsx:1362
msgid "Sharing doesn't seem to work."
msgstr "Delen lijkt niet te werken."

#: src/components/account-info.jsx:1520
#: src/components/status.jsx:1368
msgid "Share…"
msgstr "Delen…"

#: src/components/account-info.jsx:1540
msgid "Unmuted @{username}"
msgstr ""

#: src/components/account-info.jsx:1552
msgid "Unmute <0>@{username}</0>"
msgstr "Dempen van <0>@{username}</0> opheffen"

#: src/components/account-info.jsx:1568
msgid "Mute <0>@{username}</0>…"
msgstr "Demp <0>@{username}</0>…"

#. placeholder {0}: typeof MUTE_DURATIONS_LABELS[duration] === 'function' ? MUTE_DURATIONS_LABELS[duration]() : _(MUTE_DURATIONS_LABELS[duration])
#: src/components/account-info.jsx:1600
msgid "Muted @{username} for {0}"
msgstr ""

#: src/components/account-info.jsx:1612
msgid "Unable to mute @{username}"
msgstr ""

#: src/components/account-info.jsx:1633
msgid "Remove <0>@{username}</0> from followers?"
msgstr "Verwijder <0>@{username}</0> van volgers?"

#: src/components/account-info.jsx:1653
msgid "@{username} removed from followers"
msgstr ""

#: src/components/account-info.jsx:1665
msgid "Remove follower…"
msgstr "Verwijder volger…"

#: src/components/account-info.jsx:1676
msgid "Block <0>@{username}</0>?"
msgstr "Blokkeer <0>@{username}</0>?"

#: src/components/account-info.jsx:1700
msgid "Unblocked @{username}"
msgstr ""

#: src/components/account-info.jsx:1708
msgid "Blocked @{username}"
msgstr ""

#: src/components/account-info.jsx:1716
msgid "Unable to unblock @{username}"
msgstr ""

#: src/components/account-info.jsx:1718
msgid "Unable to block @{username}"
msgstr ""

#: src/components/account-info.jsx:1728
msgid "Unblock <0>@{username}</0>"
msgstr "Blokkeren van <0>@{username}</0> opheffen"

#: src/components/account-info.jsx:1737
msgid "Block <0>@{username}</0>…"
msgstr "Blokkeer <0>@{username}</0>…"

#: src/components/account-info.jsx:1754
msgid "Report <0>@{username}</0>…"
msgstr "Rapporteer <0>@{username}</0>…"

#: src/components/account-info.jsx:1810
msgid "Withdraw follow request?"
msgstr ""

#. placeholder {0}: info.acct || info.username
#: src/components/account-info.jsx:1811
msgid "Unfollow @{0}?"
msgstr ""

#: src/components/account-info.jsx:1869
msgid "Unfollow…"
msgstr "Ontvolg…"

#: src/components/account-info.jsx:1878
msgid "Withdraw…"
msgstr "Neem terug…"

#: src/components/account-info.jsx:1885
#: src/components/account-info.jsx:1889
#: src/pages/hashtag.jsx:265
msgid "Follow"
msgstr "Volg"

#: src/components/account-info.jsx:1986
#: src/components/account-info.jsx:2041
#: src/components/account-info.jsx:2175
#: src/components/account-info.jsx:2295
#: src/components/account-sheet.jsx:38
#: src/components/compose.jsx:891
#: src/components/compose.jsx:2748
#: src/components/compose.jsx:3228
#: src/components/compose.jsx:3437
#: src/components/compose.jsx:3667
#: src/components/drafts.jsx:59
#: src/components/embed-modal.jsx:13
#: src/components/generic-accounts.jsx:151
#: src/components/keyboard-shortcuts-help.jsx:43
#: src/components/list-add-edit.jsx:37
#: src/components/media-alt-modal.jsx:43
#: src/components/media-modal.jsx:327
#: src/components/notification-service.jsx:157
#: src/components/report-modal.jsx:118
#: src/components/shortcuts-settings.jsx:230
#: src/components/shortcuts-settings.jsx:583
#: src/components/shortcuts-settings.jsx:783
#: src/components/status.jsx:3188
#: src/components/status.jsx:3428
#: src/components/status.jsx:3937
#: src/pages/accounts.jsx:45
#: src/pages/catchup.jsx:1584
#: src/pages/filters.jsx:225
#: src/pages/list.jsx:302
#: src/pages/notifications.jsx:942
#: src/pages/scheduled-posts.jsx:259
#: src/pages/settings.jsx:90
#: src/pages/status.jsx:1392
msgid "Close"
msgstr "Sluit"

#: src/components/account-info.jsx:1991
msgid "Translated Bio"
msgstr "Vertaalde bio"

#: src/components/account-info.jsx:2086
msgid "Unable to remove from list."
msgstr ""

#: src/components/account-info.jsx:2087
msgid "Unable to add to list."
msgstr ""

#: src/components/account-info.jsx:2106
#: src/pages/lists.jsx:131
msgid "Unable to load lists."
msgstr "Kan lijsten niet laden."

#: src/components/account-info.jsx:2110
msgid "No lists."
msgstr "Geen lijsten."

#: src/components/account-info.jsx:2121
#: src/components/list-add-edit.jsx:41
#: src/pages/lists.jsx:62
msgid "New list"
msgstr "Nieuwe lijst"

#. placeholder {0}: account?.username || account?.acct
#: src/components/account-info.jsx:2180
msgid "Private note about <0>@{0}</0>"
msgstr "Privénotitie over <0>@{0}</0>"

#: src/components/account-info.jsx:2210
msgid "Unable to update private note."
msgstr ""

#: src/components/account-info.jsx:2233
#: src/components/account-info.jsx:2531
msgid "Cancel"
msgstr "Annuleren"

#: src/components/account-info.jsx:2238
msgid "Save & close"
msgstr "Opslaan & sluiten"

#: src/components/account-info.jsx:2355
msgid "Unable to update profile."
msgstr ""

#: src/components/account-info.jsx:2362
msgid "Header picture"
msgstr ""

#: src/components/account-info.jsx:2414
msgid "Profile picture"
msgstr ""

#: src/components/account-info.jsx:2466
#: src/components/list-add-edit.jsx:106
msgid "Name"
msgstr "Naam"

#: src/components/account-info.jsx:2479
msgid "Bio"
msgstr "Bio"

#: src/components/account-info.jsx:2492
msgid "Extra fields"
msgstr "Extra velden"

#: src/components/account-info.jsx:2498
msgid "Label"
msgstr "Omschrijving"

#: src/components/account-info.jsx:2501
msgid "Content"
msgstr "Inhoud"

#: src/components/account-info.jsx:2534
#: src/components/list-add-edit.jsx:152
#: src/components/shortcuts-settings.jsx:715
#: src/pages/filters.jsx:570
#: src/pages/notifications.jsx:1008
msgid "Save"
msgstr "Opslaan"

#: src/components/account-info.jsx:2588
msgid "username"
msgstr "gebruikersnaam"

#: src/components/account-info.jsx:2592
msgid "server domain name"
msgstr "server domeinnaam"

#. placeholder {0}: info.username
#: src/components/account-info.jsx:2658
msgid "Profiles featured by @{0}"
msgstr ""

#: src/components/account-info.jsx:2684
msgid "No featured profiles."
msgstr ""

#: src/components/background-service.jsx:158
msgid "Cloak mode disabled"
msgstr "Cloak-modus uitgeschakeld"

#: src/components/background-service.jsx:158
msgid "Cloak mode enabled"
msgstr "Cloak-modus ingeschakeld"

#: src/components/columns.jsx:27
#: src/components/nav-menu.jsx:181
#: src/components/shortcuts-settings.jsx:139
#: src/components/timeline.jsx:469
#: src/pages/catchup.jsx:883
#: src/pages/filters.jsx:90
#: src/pages/followed-hashtags.jsx:41
#: src/pages/home.jsx:54
#: src/pages/notifications.jsx:587
#: src/pages/scheduled-posts.jsx:74
msgid "Home"
msgstr "Startpagina"

#: src/components/compose-button.jsx:150
#: src/compose.jsx:38
msgid "Compose"
msgstr "Opstellen"

#: src/components/compose-button.jsx:177
#: src/components/nav-menu.jsx:265
#: src/pages/scheduled-posts.jsx:31
#: src/pages/scheduled-posts.jsx:78
msgid "Scheduled Posts"
msgstr ""

#: src/components/compose-button.jsx:190
msgid "Add to thread"
msgstr ""

#: src/components/compose.jsx:211
msgid "Take photo or video"
msgstr ""

#: src/components/compose.jsx:212
msgid "Add media"
msgstr ""

#: src/components/compose.jsx:213
msgid "Add custom emoji"
msgstr "Lokale emoji toevoegen"

#: src/components/compose.jsx:214
msgid "Add GIF"
msgstr ""

#: src/components/compose.jsx:215
msgid "Add poll"
msgstr "Voeg peiling toe"

#: src/components/compose.jsx:216
msgid "Schedule post"
msgstr ""

#: src/components/compose.jsx:416
msgid "You have unsaved changes. Discard this post?"
msgstr "Je hebt niet-opgeslagen wijzigingen. Wil je dit bericht weggooien?"

#. placeholder {0}: unsupportedFiles.length
#. placeholder {1}: unsupportedFiles[0].name
#. placeholder {2}: lf.format( unsupportedFiles.map((f) => f.name), )
#: src/components/compose.jsx:654
msgid "{0, plural, one {File {1} is not supported.} other {Files {2} are not supported.}}"
msgstr ""

#: src/components/compose.jsx:664
#: src/components/compose.jsx:682
#: src/components/compose.jsx:1794
#: src/components/compose.jsx:1919
msgid "{maxMediaAttachments, plural, one {You can only attach up to 1 file.} other {You can only attach up to # files.}}"
msgstr "{maxMediaAttachments, plural, one {Je kunt slechts 1 bestand bijvoegen.} other {Je kunt slechts # bestanden bijvoegen.}}"

#: src/components/compose.jsx:872
msgid "Pop out"
msgstr "Uitklappen"

#: src/components/compose.jsx:879
msgid "Minimize"
msgstr "Minimaliseren"

#: src/components/compose.jsx:915
msgid "Looks like you closed the parent window."
msgstr "Het lijkt erop dat je het bovenste venster hebt gesloten."

#: src/components/compose.jsx:922
msgid "Looks like you already have a compose field open in the parent window and currently publishing. Please wait for it to be done and try again later."
msgstr ""

#: src/components/compose.jsx:927
msgid "Looks like you already have a compose field open in the parent window. Popping in this window will discard the changes you made in the parent window. Continue?"
msgstr "Je hebt al een opstelveld open in het bovenliggende venster. Als je in dit venster wilt opstellen, worden de wijzigingen die je in het bovenliggende venster hebt aangebracht ongedaan gemaakt. Doorgaan?"

#: src/components/compose.jsx:970
msgid "Pop in"
msgstr "Inklappen"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#. placeholder {1}: rtf.format(-replyToStatusMonthsAgo, 'month')
#: src/components/compose.jsx:980
msgid "Replying to @{0}’s post (<0>{1}</0>)"
msgstr "Antwoord op @{0}'s bericht (<0>{1}</0>)"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#: src/components/compose.jsx:990
msgid "Replying to @{0}’s post"
msgstr "Antwoord op @{0}'s bericht"

#: src/components/compose.jsx:1003
msgid "Editing source post"
msgstr "Bronbericht bewerken"

#: src/components/compose.jsx:1056
msgid "Poll must have at least 2 options"
msgstr "Peiling moet minstens 2 opties hebben"

#: src/components/compose.jsx:1060
msgid "Some poll choices are empty"
msgstr "Sommige peilingkeuzes zijn leeg"

#: src/components/compose.jsx:1073
msgid "Some media have no descriptions. Continue?"
msgstr ""

#: src/components/compose.jsx:1125
msgid "Attachment #{i} failed"
msgstr "Bijlage #{i} is mislukt"

#: src/components/compose.jsx:1221
#: src/components/status.jsx:2245
#: src/components/timeline.jsx:1023
msgid "Content warning"
msgstr "Inhoudswaarschuwing"

#: src/components/compose.jsx:1237
msgid "Content warning or sensitive media"
msgstr "Inhoudswaarschuwing of gevoelige media"

#: src/components/compose.jsx:1273
#: src/components/status.jsx:99
#: src/pages/settings.jsx:318
msgid "Public"
msgstr "Openbaar"

#: src/components/compose.jsx:1278
#: src/components/nav-menu.jsx:349
#: src/components/shortcuts-settings.jsx:165
#: src/components/status.jsx:100
msgid "Local"
msgstr "Lokaal"

#: src/components/compose.jsx:1282
#: src/components/status.jsx:101
#: src/pages/settings.jsx:321
msgid "Unlisted"
msgstr "Niet openbaar"

#: src/components/compose.jsx:1285
#: src/components/status.jsx:102
#: src/pages/settings.jsx:324
msgid "Followers only"
msgstr "Alleen volgers"

#: src/components/compose.jsx:1288
#: src/components/status.jsx:103
#: src/components/status.jsx:2121
msgid "Private mention"
msgstr "Privévermelding"

#: src/components/compose.jsx:1297
msgid "Post your reply"
msgstr "Plaats je antwoord"

#: src/components/compose.jsx:1299
msgid "Edit your post"
msgstr "Bewerk je bericht"

#: src/components/compose.jsx:1300
msgid "What are you doing?"
msgstr "Wat ben je aan het doen?"

#: src/components/compose.jsx:1379
msgid "Mark media as sensitive"
msgstr "Markeer media als gevoelig"

#: src/components/compose.jsx:1416
msgid "Posting on <0/>"
msgstr ""

#: src/components/compose.jsx:1447
#: src/components/compose.jsx:3286
#: src/components/shortcuts-settings.jsx:715
#: src/pages/list.jsx:388
msgid "Add"
msgstr "Toevoegen"

#: src/components/compose.jsx:1675
msgid "Schedule"
msgstr ""

#: src/components/compose.jsx:1677
#: src/components/keyboard-shortcuts-help.jsx:155
#: src/components/status.jsx:1109
#: src/components/status.jsx:1895
#: src/components/status.jsx:1896
#: src/components/status.jsx:2590
msgid "Reply"
msgstr "Beantwoord"

#: src/components/compose.jsx:1679
msgid "Update"
msgstr "Werk bij"

#: src/components/compose.jsx:1680
msgctxt "Submit button in composer"
msgid "Post"
msgstr "Plaats"

#: src/components/compose.jsx:1806
msgid "Downloading GIF…"
msgstr "Download GIF…"

#: src/components/compose.jsx:1834
msgid "Failed to download GIF"
msgstr "Downloaden GIF mislukt"

#: src/components/compose.jsx:2049
#: src/components/compose.jsx:2126
#: src/components/nav-menu.jsx:244
msgid "More…"
msgstr "meer…"

#: src/components/compose.jsx:2562
msgid "Uploaded"
msgstr "Geüpload"

#: src/components/compose.jsx:2575
msgid "Image description"
msgstr "Afbeeldingsbeschrijving"

#: src/components/compose.jsx:2576
msgid "Video description"
msgstr "Videobeschrijving"

#: src/components/compose.jsx:2577
msgid "Audio description"
msgstr "Audiobeschrijving"

#. placeholder {0}: prettyBytes( imageSize, )
#. placeholder {0}: prettyBytes( videoSize, )
#. placeholder {1}: prettyBytes(imageSizeLimit)
#. placeholder {1}: prettyBytes(videoSizeLimit)
#: src/components/compose.jsx:2612
#: src/components/compose.jsx:2632
msgid "File size too large. Uploading might encounter issues. Try reduce the file size from {0} to {1} or lower."
msgstr "Bestandsgrootte is te groot. Uploaden kan problemen opleveren. Probeer de bestandsgrootte van {0} naar {1} of lager te verminderen."

#. placeholder {0}: i18n.number( width, )
#. placeholder {1}: i18n.number(height)
#. placeholder {2}: i18n.number(newWidth)
#. placeholder {3}: i18n.number( newHeight, )
#: src/components/compose.jsx:2624
#: src/components/compose.jsx:2644
msgid "Dimension too large. Uploading might encounter issues. Try reduce dimension from {0}×{1}px to {2}×{3}px."
msgstr ""

#: src/components/compose.jsx:2652
msgid "Frame rate too high. Uploading might encounter issues."
msgstr "Opnamesnelheid is te hoog. Uploaden kan problemen opleveren."

#: src/components/compose.jsx:2712
#: src/components/compose.jsx:2962
#: src/components/shortcuts-settings.jsx:726
#: src/pages/catchup.jsx:1081
#: src/pages/filters.jsx:413
msgid "Remove"
msgstr "Verwijder"

#: src/components/compose.jsx:2729
#: src/compose.jsx:84
msgid "Error"
msgstr "Error"

#: src/components/compose.jsx:2754
msgid "Edit image description"
msgstr "Bewerk afbeeldingsbeschrijving"

#: src/components/compose.jsx:2755
msgid "Edit video description"
msgstr "Bewerk videobeschrijving"

#: src/components/compose.jsx:2756
msgid "Edit audio description"
msgstr "Bewerk audiobeschrijving"

#: src/components/compose.jsx:2801
#: src/components/compose.jsx:2850
msgid "Generating description. Please wait…"
msgstr "Omschrijving genereren. Even geduld…"

#. placeholder {0}: e.message
#: src/components/compose.jsx:2821
msgid "Failed to generate description: {0}"
msgstr ""

#: src/components/compose.jsx:2822
msgid "Failed to generate description"
msgstr "Genereren van beschrijving mislukt"

#: src/components/compose.jsx:2834
#: src/components/compose.jsx:2840
#: src/components/compose.jsx:2886
msgid "Generate description…"
msgstr "Genereer beschrijving…"

#. placeholder {0}: e?.message ? `: ${e.message}` : ''
#: src/components/compose.jsx:2873
msgid "Failed to generate description{0}"
msgstr ""

#. placeholder {0}: localeCode2Text(lang)
#: src/components/compose.jsx:2888
msgid "({0}) <0>— experimental</0>"
msgstr "({0}) <0>– experimenteel</0>"

#: src/components/compose.jsx:2907
msgid "Done"
msgstr "Gereed"

#. placeholder {0}: i + 1
#: src/components/compose.jsx:2943
msgid "Choice {0}"
msgstr ""

#: src/components/compose.jsx:2990
msgid "Multiple choices"
msgstr "Meerdere keuzes"

#: src/components/compose.jsx:2993
msgid "Duration"
msgstr "Tijdsduur"

#: src/components/compose.jsx:3024
msgid "Remove poll"
msgstr "Verwijder peiling"

#: src/components/compose.jsx:3245
msgid "Search accounts"
msgstr ""

#: src/components/compose.jsx:3299
#: src/components/generic-accounts.jsx:236
msgid "Error loading accounts"
msgstr "Fout bij het laden van accounts"

#: src/components/compose.jsx:3443
msgid "Custom emojis"
msgstr "Aangepaste emoji"

#: src/components/compose.jsx:3463
msgid "Search emoji"
msgstr ""

#: src/components/compose.jsx:3494
msgid "Error loading custom emojis"
msgstr "Fout bij het laden van aangepaste emojis"

#: src/components/compose.jsx:3505
msgid "Recently used"
msgstr ""

#: src/components/compose.jsx:3506
msgid "Others"
msgstr ""

#. placeholder {0}: i18n.number(emojis.length - max)
#: src/components/compose.jsx:3544
msgid "{0} more…"
msgstr "{0} meer…"

#: src/components/compose.jsx:3682
msgid "Search GIFs"
msgstr ""

#: src/components/compose.jsx:3697
msgid "Powered by GIPHY"
msgstr ""

#: src/components/compose.jsx:3705
msgid "Type to search GIFs"
msgstr "Typ om GIF's te zoeken"

#: src/components/compose.jsx:3803
#: src/components/media-modal.jsx:469
#: src/components/timeline.jsx:927
msgid "Previous"
msgstr "Vorige"

#: src/components/compose.jsx:3821
#: src/components/media-modal.jsx:488
#: src/components/timeline.jsx:944
msgid "Next"
msgstr "Volgende"

#: src/components/compose.jsx:3838
msgid "Error loading GIFs"
msgstr "Fout bij laden van GIF's"

#: src/components/drafts.jsx:64
#: src/pages/settings.jsx:702
msgid "Unsent drafts"
msgstr "Niet-verzonden concepten"

#: src/components/drafts.jsx:69
msgid "Looks like you have unsent drafts. Let's continue where you left off."
msgstr "Het lijkt erop dat je niet-verzonden concepten hebt. Laten we verder gaan waar je gebleven bent."

#: src/components/drafts.jsx:103
msgid "Delete this draft?"
msgstr "Verwijder dit concept?"

#: src/components/drafts.jsx:118
msgid "Error deleting draft! Please try again."
msgstr "Fout bij verwijderen concept! Probeer het opnieuw."

#: src/components/drafts.jsx:128
#: src/components/list-add-edit.jsx:188
#: src/components/status.jsx:1512
#: src/pages/filters.jsx:603
#: src/pages/scheduled-posts.jsx:369
msgid "Delete…"
msgstr "Verwijder…"

#: src/components/drafts.jsx:147
msgid "Error fetching reply-to status!"
msgstr "Fout bij ophalen van de antwoordstatus!"

#: src/components/drafts.jsx:172
msgid "Delete all drafts?"
msgstr "Verwijder alle concepten?"

#: src/components/drafts.jsx:190
msgid "Error deleting drafts! Please try again."
msgstr "Fout bij het verwijderen van concepten! Probeer het opnieuw."

#: src/components/drafts.jsx:202
msgid "Delete all…"
msgstr "Verwijder alles…"

#: src/components/drafts.jsx:210
msgid "No drafts found."
msgstr "Geen concepten gevonden."

#: src/components/drafts.jsx:247
#: src/pages/catchup.jsx:1932
msgid "Poll"
msgstr "Peiling"

#: src/components/drafts.jsx:250
#: src/pages/account-statuses.jsx:367
msgid "Media"
msgstr "Media"

#: src/components/embed-modal.jsx:18
msgid "Open in new window"
msgstr "Openen in een nieuw venster"

#: src/components/follow-request-buttons.jsx:43
#: src/pages/notifications.jsx:992
msgid "Accept"
msgstr "Accepteren"

#: src/components/follow-request-buttons.jsx:69
msgid "Reject"
msgstr "Weigeren"

#: src/components/follow-request-buttons.jsx:76
#: src/pages/notifications.jsx:1276
msgid "Accepted"
msgstr "Geaccepteerd"

#: src/components/follow-request-buttons.jsx:80
msgid "Rejected"
msgstr "Geweigerd"

#: src/components/generic-accounts.jsx:154
#: src/components/notification.jsx:455
#: src/pages/accounts.jsx:50
#: src/pages/search.jsx:334
#: src/pages/search.jsx:367
msgid "Accounts"
msgstr "Accounts"

#: src/components/generic-accounts.jsx:214
#: src/components/timeline.jsx:551
#: src/pages/list.jsx:321
#: src/pages/notifications.jsx:922
#: src/pages/search.jsx:561
#: src/pages/status.jsx:1425
msgid "Show more…"
msgstr "Toon meer…"

#: src/components/generic-accounts.jsx:219
#: src/components/timeline.jsx:556
#: src/pages/search.jsx:566
msgid "The end."
msgstr "Het einde."

#: src/components/generic-accounts.jsx:240
msgid "Nothing to show"
msgstr "Niets om te tonen"

#: src/components/keyboard-shortcuts-help.jsx:47
#: src/components/nav-menu.jsx:368
#: src/pages/catchup.jsx:1622
msgid "Keyboard shortcuts"
msgstr "Sneltoetsen"

#: src/components/keyboard-shortcuts-help.jsx:55
msgid "Keyboard shortcuts help"
msgstr "Sneltoetsen help"

#: src/components/keyboard-shortcuts-help.jsx:59
#: src/pages/catchup.jsx:1647
msgid "Next post"
msgstr "Volgend bericht"

#: src/components/keyboard-shortcuts-help.jsx:63
#: src/pages/catchup.jsx:1655
msgid "Previous post"
msgstr "Vorig bericht"

#: src/components/keyboard-shortcuts-help.jsx:67
msgid "Skip carousel to next post"
msgstr "Sla carrousel over naar volgend bericht"

#: src/components/keyboard-shortcuts-help.jsx:69
msgid "<0>Shift</0> + <1>j</1>"
msgstr "<0>Shift</0> + <1>j</1>"

#: src/components/keyboard-shortcuts-help.jsx:75
msgid "Skip carousel to previous post"
msgstr "Sla carrousel over naar vorig bericht"

#: src/components/keyboard-shortcuts-help.jsx:77
msgid "<0>Shift</0> + <1>k</1>"
msgstr "<0>Shift</0> + <1>k</1>"

#: src/components/keyboard-shortcuts-help.jsx:83
msgid "Load new posts"
msgstr "Laad nieuwe berichten"

#: src/components/keyboard-shortcuts-help.jsx:87
#: src/pages/catchup.jsx:1679
msgid "Open post details"
msgstr "Open berichtdetails"

#: src/components/keyboard-shortcuts-help.jsx:89
msgid "<0>Enter</0> or <1>o</1>"
msgstr "<0>Enter</0> of <1>o</1>"

#: src/components/keyboard-shortcuts-help.jsx:96
msgid "Expand content warning or<0/>toggle expanded/collapsed thread"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:105
msgid "Close post or dialogs"
msgstr "Sluit bericht of dialoogvenster"

#: src/components/keyboard-shortcuts-help.jsx:107
msgid "<0>Esc</0> or <1>Backspace</1>"
msgstr "<0>Esc</0> of <1>Backspace</1>"

#: src/components/keyboard-shortcuts-help.jsx:113
msgid "Focus column in multi-column mode"
msgstr "Focus kolom in multi-kolommodus"

#: src/components/keyboard-shortcuts-help.jsx:115
msgid "<0>1</0> to <1>9</1>"
msgstr "<0>1</0> tot <1>9</1>"

#: src/components/keyboard-shortcuts-help.jsx:121
msgid "Focus next column in multi-column mode"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:125
msgid "Focus previous column in multi-column mode"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:129
msgid "Compose new post"
msgstr "Stel nieuw bericht op"

#: src/components/keyboard-shortcuts-help.jsx:133
msgid "Compose new post (new window)"
msgstr "Stel nieuw bericht op (nieuw venster)"

#: src/components/keyboard-shortcuts-help.jsx:136
msgid "<0>Shift</0> + <1>c</1>"
msgstr "<0>Shift</0> + <1>c</1>"

#: src/components/keyboard-shortcuts-help.jsx:142
msgid "Send post"
msgstr "Verstuur bericht"

#: src/components/keyboard-shortcuts-help.jsx:144
msgid "<0>Ctrl</0> + <1>Enter</1> or <2>⌘</2> + <3>Enter</3>"
msgstr "<0>Ctrl</0> + <1>Enter</1> of <2>⌘</2> + <3>Enter</3>"

#: src/components/keyboard-shortcuts-help.jsx:151
#: src/components/nav-menu.jsx:337
#: src/components/search-form.jsx:73
#: src/components/shortcuts-settings.jsx:52
#: src/components/shortcuts-settings.jsx:179
#: src/pages/search.jsx:46
#: src/pages/search.jsx:316
msgid "Search"
msgstr "Zoeken"

#: src/components/keyboard-shortcuts-help.jsx:159
msgid "Reply (new window)"
msgstr "Beantwoord (nieuw venster)"

#: src/components/keyboard-shortcuts-help.jsx:162
msgid "<0>Shift</0> + <1>r</1>"
msgstr "<0>Shift</0> + <1>r</1>"

#: src/components/keyboard-shortcuts-help.jsx:168
msgid "Like (favourite)"
msgstr "Like (favoriet)"

#: src/components/keyboard-shortcuts-help.jsx:170
msgid "<0>l</0> or <1>f</1>"
msgstr "<0>l</0> of <1>f</1>"

#: src/components/keyboard-shortcuts-help.jsx:176
#: src/components/status.jsx:1117
#: src/components/status.jsx:2617
#: src/components/status.jsx:2640
#: src/components/status.jsx:2641
msgid "Boost"
msgstr "Boost"

#: src/components/keyboard-shortcuts-help.jsx:178
msgid "<0>Shift</0> + <1>b</1>"
msgstr "<0>Shift</0> + <1>b</1>"

#: src/components/keyboard-shortcuts-help.jsx:184
#: src/components/status.jsx:1180
#: src/components/status.jsx:2665
#: src/components/status.jsx:2666
msgid "Bookmark"
msgstr "Bladwijzer"

#: src/components/keyboard-shortcuts-help.jsx:188
msgid "Toggle Cloak mode"
msgstr "Schakel Cloak-modus"

#: src/components/keyboard-shortcuts-help.jsx:190
msgid "<0>Shift</0> + <1>Alt</1> + <2>k</2>"
msgstr "<0>Shift</0> + <1>Alt</1> + <2>k</2>"

#: src/components/list-add-edit.jsx:41
msgid "Edit list"
msgstr "Bewerk lijst"

#: src/components/list-add-edit.jsx:97
msgid "Unable to edit list."
msgstr "Kan lijst niet bewerken."

#: src/components/list-add-edit.jsx:98
msgid "Unable to create list."
msgstr "Kan lijst niet aanmaken."

#: src/components/list-add-edit.jsx:126
msgid "Show replies to list members"
msgstr "Toon antwoorden aan ledenlijst"

#: src/components/list-add-edit.jsx:129
msgid "Show replies to people I follow"
msgstr "Toon antwoorden aan mensen die ik volg"

#: src/components/list-add-edit.jsx:132
msgid "Don't show replies"
msgstr "Geen antwoorden tonen"

#: src/components/list-add-edit.jsx:146
msgid "Hide posts on this list from Home/Following"
msgstr ""

#: src/components/list-add-edit.jsx:152
#: src/pages/filters.jsx:570
msgid "Create"
msgstr "Aanmaken"

#: src/components/list-add-edit.jsx:159
msgid "Delete this list?"
msgstr "Deze lijst verwijderen?"

#: src/components/list-add-edit.jsx:178
msgid "Unable to delete list."
msgstr "Kan lijst niet verwijderen."

#: src/components/list-exclusive-badge.jsx:14
#: src/pages/list.jsx:179
#: src/pages/lists.jsx:107
msgid "Posts on this list are hidden from Home/Following"
msgstr ""

#: src/components/media-alt-modal.jsx:48
#: src/components/media.jsx:51
msgid "Media description"
msgstr "Mediabeschrijving"

#: src/components/media-alt-modal.jsx:67
#: src/components/status.jsx:1223
#: src/components/status.jsx:1232
#: src/components/translation-block.jsx:239
msgid "Translate"
msgstr "Vertaal"

#: src/components/media-alt-modal.jsx:78
#: src/components/status.jsx:1251
msgid "Speak"
msgstr "Spreek uit"

#: src/components/media-modal.jsx:374
msgid "Open original media in new window"
msgstr "Open originele media in nieuw venster"

#: src/components/media-modal.jsx:378
msgid "Open original media"
msgstr "Open originele media"

#: src/components/media-modal.jsx:394
msgid "Attempting to describe image. Please wait…"
msgstr ""

#: src/components/media-modal.jsx:409
msgid "Failed to describe image"
msgstr "Fout bij het beschrijven van afbeelding"

#: src/components/media-modal.jsx:419
msgid "Describe image…"
msgstr "Beschrijf afbeelding…"

#: src/components/media-modal.jsx:443
msgid "View post"
msgstr "Bekijk bericht"

#: src/components/media-post.jsx:127
msgid "Sensitive media"
msgstr "Gevoelige media"

#: src/components/media-post.jsx:132
msgid "Filtered: {filterTitleStr}"
msgstr "Gefilterd: {filterTitleStr}"

#: src/components/media-post.jsx:133
#: src/components/status.jsx:3767
#: src/components/status.jsx:3863
#: src/components/status.jsx:3941
#: src/components/timeline.jsx:1012
#: src/pages/catchup.jsx:75
#: src/pages/catchup.jsx:1880
msgid "Filtered"
msgstr "Gefilterd"

#: src/components/media.jsx:477
msgid "Open file"
msgstr ""

#: src/components/modals.jsx:75
msgid "Post scheduled"
msgstr ""

#: src/components/modals.jsx:76
msgid "Post published. Check it out."
msgstr "Bericht geplaatst. Bekijk het eens."

#: src/components/modals.jsx:78
msgid "Reply scheduled"
msgstr ""

#: src/components/modals.jsx:79
msgid "Reply posted. Check it out."
msgstr "Antwoord geplaatst. Bekijk het eens."

#: src/components/modals.jsx:80
msgid "Post updated. Check it out."
msgstr "Bericht bijgewerkt. Bekijk het eens."

#: src/components/nav-menu.jsx:123
msgid "Menu"
msgstr "Menu"

#: src/components/nav-menu.jsx:159
msgid "Reload page now to update?"
msgstr "Pagina herladen om bij te werken?"

#: src/components/nav-menu.jsx:171
msgid "New update available…"
msgstr "Nieuwe update beschikbaar…"

#. js-lingui-explicit-id
#: src/components/nav-menu.jsx:190
#: src/components/shortcuts-settings.jsx:140
#: src/pages/following.jsx:23
#: src/pages/following.jsx:145
msgid "following.title"
msgstr ""

#: src/components/nav-menu.jsx:197
#: src/pages/catchup.jsx:878
msgid "Catch-up"
msgstr "Inhalen"

#: src/components/nav-menu.jsx:204
#: src/components/shortcuts-settings.jsx:58
#: src/components/shortcuts-settings.jsx:146
#: src/pages/home.jsx:240
#: src/pages/mentions.jsx:21
#: src/pages/mentions.jsx:174
#: src/pages/settings.jsx:1190
#: src/pages/trending.jsx:384
msgid "Mentions"
msgstr "Vermeldingen"

#: src/components/nav-menu.jsx:211
#: src/components/shortcuts-settings.jsx:49
#: src/components/shortcuts-settings.jsx:152
#: src/pages/filters.jsx:24
#: src/pages/home.jsx:86
#: src/pages/home.jsx:198
#: src/pages/notifications.jsx:117
#: src/pages/notifications.jsx:591
msgid "Notifications"
msgstr "Meldingen"

#: src/components/nav-menu.jsx:214
msgid "New"
msgstr "Nieuw"

#: src/components/nav-menu.jsx:225
msgid "Profile"
msgstr "Profiel"

#: src/components/nav-menu.jsx:233
#: src/components/shortcuts-settings.jsx:54
#: src/components/shortcuts-settings.jsx:195
#: src/pages/bookmarks.jsx:12
#: src/pages/bookmarks.jsx:26
msgid "Bookmarks"
msgstr "Bladwijzers"

#: src/components/nav-menu.jsx:253
#: src/components/shortcuts-settings.jsx:55
#: src/components/shortcuts-settings.jsx:201
#: src/pages/catchup.jsx:1449
#: src/pages/catchup.jsx:2067
#: src/pages/favourites.jsx:12
#: src/pages/favourites.jsx:26
#: src/pages/settings.jsx:1194
msgid "Likes"
msgstr "Likes"

#: src/components/nav-menu.jsx:259
#: src/pages/followed-hashtags.jsx:15
#: src/pages/followed-hashtags.jsx:45
msgid "Followed Hashtags"
msgstr "Gevolgde Hashtags"

#: src/components/nav-menu.jsx:273
#: src/pages/account-statuses.jsx:333
#: src/pages/filters.jsx:55
#: src/pages/filters.jsx:94
#: src/pages/hashtag.jsx:344
msgid "Filters"
msgstr "Filters"

#: src/components/nav-menu.jsx:281
msgid "Muted users"
msgstr "Gedempte gebruikers"

#: src/components/nav-menu.jsx:289
msgid "Muted users…"
msgstr "Gedempte gebruikers…"

#: src/components/nav-menu.jsx:296
msgid "Blocked users"
msgstr "Geblokkeerde gebruikers"

#: src/components/nav-menu.jsx:304
msgid "Blocked users…"
msgstr "Geblokkeerde gebruikers…"

#: src/components/nav-menu.jsx:316
msgid "Accounts…"
msgstr "Accounts…"

#: src/components/nav-menu.jsx:326
#: src/pages/login.jsx:32
#: src/pages/login.jsx:199
#: src/pages/status.jsx:924
#: src/pages/welcome.jsx:65
msgid "Log in"
msgstr "Inloggen"

#: src/components/nav-menu.jsx:343
#: src/components/shortcuts-settings.jsx:57
#: src/components/shortcuts-settings.jsx:172
#: src/pages/trending.jsx:444
msgid "Trending"
msgstr "Populair"

#: src/components/nav-menu.jsx:355
#: src/components/shortcuts-settings.jsx:165
msgid "Federated"
msgstr "Gefedereerd"

#: src/components/nav-menu.jsx:378
msgid "Shortcuts / Columns…"
msgstr "Snelkoppelingen / Kolommen…"

#: src/components/nav-menu.jsx:388
#: src/components/nav-menu.jsx:402
msgid "Settings…"
msgstr "Instellingen…"

#: src/components/nav-menu.jsx:432
#: src/components/nav-menu.jsx:467
#: src/components/shortcuts-settings.jsx:50
#: src/components/shortcuts-settings.jsx:158
#: src/pages/list.jsx:127
#: src/pages/lists.jsx:18
#: src/pages/lists.jsx:54
msgid "Lists"
msgstr "Lijsten"

#: src/components/nav-menu.jsx:440
#: src/components/shortcuts.jsx:241
#: src/pages/list.jsx:139
msgid "All Lists"
msgstr "Alle lijsten"

#: src/components/notification-service.jsx:161
msgid "Notification"
msgstr "Melding"

#: src/components/notification-service.jsx:167
msgid "This notification is from your other account."
msgstr "Deze melding komt van je andere account."

#: src/components/notification-service.jsx:196
msgid "View all notifications"
msgstr "Toon alle meldingen"

#: src/components/notification.jsx:71
msgid "{account} reacted to your post with {emojiObject}"
msgstr ""

#: src/components/notification.jsx:78
msgid "{account} published a post."
msgstr "{account} heeft een bericht gepubliceerd."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:86
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted your reply.} other {{account} boosted your post.}}} other {{account} boosted {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted your reply.} other {<2><3>{1}</3> people</2> boosted your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} heeft je antwoord gedeeld.} other {{account} heeft je bericht gedeeld.}}} other {{account} heeft {postsCount} van je berichten gedeeld.}}} other {{postType, select, reply {<0><1>{0}</1> mensen</0> hebben je antwoord gedeeld.} other {<2><3>{1}</3> mensen</2> hebben je berichten gedeeld.}}}}"

#. placeholder {0}: shortenNumber(count)
#: src/components/notification.jsx:129
msgid "{count, plural, =1 {{account} followed you.} other {<0><1>{0}</1> people</0> followed you.}}"
msgstr ""

#: src/components/notification.jsx:143
msgid "{account} requested to follow you."
msgstr "{account} heeft verzocht je te volgen."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:152
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} liked your reply.} other {{account} liked your post.}}} other {{account} liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> liked your reply.} other {<2><3>{1}</3> people</2> liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} heeft je antwoord geliket.} other {{account} heeft je bericht geliket.}}} other {{account} heeft {postsCount} van je berichten geliket.}}} other {{postType, select, reply {<0><1>{0}</1> mensen</0> hebben je antwoord geliket.} other {<2><3>{1}</3> mensen</2> hebben je bericht geliket.}}}}"

#: src/components/notification.jsx:194
msgid "A poll you have voted in or created has ended."
msgstr "Een peiling die je hebt aangemaakt of waarin je hebt gestemd is geëindigd."

#: src/components/notification.jsx:195
msgid "A poll you have created has ended."
msgstr "Een peiling die je hebt aangemaakt is geëindigd."

#: src/components/notification.jsx:196
msgid "A poll you have voted in has ended."
msgstr "Een peiling waarin je hebt gestemd is geëindigd."

#: src/components/notification.jsx:197
msgid "A post you interacted with has been edited."
msgstr ""

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:205
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted & liked your reply.} other {{account} boosted & liked your post.}}} other {{account} boosted & liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted & liked your reply.} other {<2><3>{1}</3> people</2> boosted & liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} heeft je antwoord geboost & geliket.} other {{account} heeft je bericht geboost & geliket.}}} other {{account} heeft {postsCount} van je berichten geboost & geliket.}}} other {{postType, select, reply {<0><1>{0}</1> mensen</0> hebben je antwoord geboost & geliket.} other {<2><3>{1}</3> mensen</2> hebben je bericht geboost & geliket.}}}}"

#: src/components/notification.jsx:247
msgid "{account} signed up."
msgstr "{account} heeft zich aangemeld."

#: src/components/notification.jsx:249
msgid "{account} reported {targetAccount}"
msgstr ""

#: src/components/notification.jsx:254
msgid "Lost connections with <0>{name}</0>."
msgstr "Verbindingen verbroken met <0>{name}</0>."

#: src/components/notification.jsx:260
msgid "Moderation warning"
msgstr "Moderatie-waarschuwing"

#: src/components/notification.jsx:265
msgid "Your {year} #Wrapstodon is here!"
msgstr ""

#: src/components/notification.jsx:271
msgid "An admin from <0>{from}</0> has suspended <1>{targetName}</1>, which means you can no longer receive updates from them or interact with them."
msgstr "Een beheerder van <0>{from}</0> heeft <1>{targetName}</1> geschorst, wat betekent dat u geen updates meer van hen kunt ontvangen of met hen kunt communiceren."

#: src/components/notification.jsx:277
msgid "An admin from <0>{from}</0> has blocked <1>{targetName}</1>. Affected followers: {followersCount}, followings: {followingCount}."
msgstr "Een admin van <0>{from}</0> heeft <1>{targetName}</1> geblokkeerd. Getroffen volgers: {followersCount}, volgt: {followingCount}."

#: src/components/notification.jsx:283
msgid "You have blocked <0>{targetName}</0>. Removed followers: {followersCount}, followings: {followingCount}."
msgstr "Je hebt <0>{targetName}</0> geblokkeerd. Verwijderde volgers: {followersCount}, volgt: {followingCount}."

#: src/components/notification.jsx:291
msgid "Your account has received a moderation warning."
msgstr "Jouw account heeft een moderatie-waarschuwing ontvangen."

#: src/components/notification.jsx:292
msgid "Your account has been disabled."
msgstr ""

#: src/components/notification.jsx:293
msgid "Some of your posts have been marked as sensitive."
msgstr ""

#: src/components/notification.jsx:294
msgid "Some of your posts have been deleted."
msgstr ""

#: src/components/notification.jsx:295
msgid "Your posts will be marked as sensitive from now on."
msgstr ""

#: src/components/notification.jsx:296
msgid "Your account has been limited."
msgstr "Je account is beperkt."

#: src/components/notification.jsx:297
msgid "Your account has been suspended."
msgstr "Je account is geschorst."

#: src/components/notification.jsx:374
msgid "[Unknown notification type: {type}]"
msgstr "[Onbekend meldingstype: {type}]"

#: src/components/notification.jsx:451
#: src/components/status.jsx:1194
#: src/components/status.jsx:1204
msgid "Boosted/Liked by…"
msgstr "Geboost/Geliket door…"

#: src/components/notification.jsx:452
msgid "Liked by…"
msgstr "Geliket door…"

#: src/components/notification.jsx:453
msgid "Boosted by…"
msgstr "Geboost door…"

#: src/components/notification.jsx:454
msgid "Followed by…"
msgstr "Gevolgd door…"

#: src/components/notification.jsx:535
#: src/components/notification.jsx:551
msgid "Learn more <0/>"
msgstr "Leer meer <0/>"

#: src/components/notification.jsx:560
msgid "View #Wrapstodon"
msgstr ""

#: src/components/notification.jsx:801
#: src/components/status.jsx:403
msgid "Read more →"
msgstr "Lees meer →"

#: src/components/poll.jsx:113
msgid "Voted"
msgstr "Gestemd"

#: src/components/poll.jsx:119
msgid "{optionVotesCount, plural, one {# vote} other {# votes}}"
msgstr "{optionVotesCount, plural, one {# stem} other {# stemmen}}"

#: src/components/poll.jsx:139
#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Hide results"
msgstr "Verberg uitslagen"

#: src/components/poll.jsx:188
msgid "Vote"
msgstr "Stem"

#: src/components/poll.jsx:208
#: src/components/poll.jsx:210
#: src/pages/scheduled-posts.jsx:100
#: src/pages/status.jsx:1294
#: src/pages/status.jsx:1317
msgid "Refresh"
msgstr "Ververs"

#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Show results"
msgstr "Toon uitslagen"

#. placeholder {0}: shortenNumber(votesCount)
#. placeholder {1}: shortenNumber(votesCount)
#: src/components/poll.jsx:231
msgid "{votesCount, plural, one {<0>{0}</0> vote} other {<1>{1}</1> votes}}"
msgstr "{votesCount, plural, one {<0>{0}</0> stem} other {<1>{1}</1> stemmen}}"

#. placeholder {0}: shortenNumber(votersCount)
#. placeholder {1}: shortenNumber(votersCount)
#: src/components/poll.jsx:248
msgid "{votersCount, plural, one {<0>{0}</0> voter} other {<1>{1}</1> voters}}"
msgstr "{votersCount, plural, one {<0>{0}</0> stemmer} other {<1>{1}</1> stemmers}}"

#: src/components/poll.jsx:268
msgid "Ended <0/>"
msgstr "Eindigde <0/>"

#: src/components/poll.jsx:272
msgid "Ended"
msgstr "Geëindigd"

#: src/components/poll.jsx:275
msgid "Ending <0/>"
msgstr "Eindigt <0/>"

#: src/components/poll.jsx:279
msgid "Ending"
msgstr "Eindigt"

#. Relative time in seconds, as short as possible
#. placeholder {0}: seconds < 1 ? 1 : Math.floor(seconds)
#: src/components/relative-time.jsx:61
msgid "{0}s"
msgstr "{0} s"

#. Relative time in minutes, as short as possible
#. placeholder {0}: Math.floor(seconds / minute)
#: src/components/relative-time.jsx:66
msgid "{0}m"
msgstr "{0} m"

#. Relative time in hours, as short as possible
#. placeholder {0}: Math.floor(seconds / hour)
#: src/components/relative-time.jsx:71
msgid "{0}h"
msgstr "{0} u"

#: src/components/report-modal.jsx:30
msgid "Spam"
msgstr "Spam"

#: src/components/report-modal.jsx:31
msgid "Malicious links, fake engagement, or repetitive replies"
msgstr ""

#: src/components/report-modal.jsx:34
msgid "Illegal"
msgstr "Illegaal"

#: src/components/report-modal.jsx:35
msgid "Violates the law of your or the server's country"
msgstr ""

#: src/components/report-modal.jsx:38
msgid "Server rule violation"
msgstr ""

#: src/components/report-modal.jsx:39
msgid "Breaks specific server rules"
msgstr ""

#: src/components/report-modal.jsx:40
msgid "Violation"
msgstr "Overtreding"

#: src/components/report-modal.jsx:43
msgid "Other"
msgstr "Overig"

#: src/components/report-modal.jsx:44
msgid "Issue doesn't fit other categories"
msgstr ""

#: src/components/report-modal.jsx:111
msgid "Report Post"
msgstr "Rapporteer bericht"

#: src/components/report-modal.jsx:111
msgid "Report @{username}"
msgstr "Rapporteer @{username}"

#: src/components/report-modal.jsx:147
msgid "Pending review"
msgstr "Openstaande review"

#: src/components/report-modal.jsx:189
msgid "Post reported"
msgstr ""

#: src/components/report-modal.jsx:189
msgid "Profile reported"
msgstr ""

#: src/components/report-modal.jsx:197
msgid "Unable to report post"
msgstr ""

#: src/components/report-modal.jsx:198
msgid "Unable to report profile"
msgstr ""

#: src/components/report-modal.jsx:206
msgid "What's the issue with this post?"
msgstr "Wat is het probleem met dit bericht?"

#: src/components/report-modal.jsx:207
msgid "What's the issue with this profile?"
msgstr "Wat is het probleem met dit profiel?"

#: src/components/report-modal.jsx:276
msgid "Additional info"
msgstr "Extra informatie"

#: src/components/report-modal.jsx:299
msgid "Forward to <0>{domain}</0>"
msgstr "Doorsturen naar <0>{domain}</0>"

#: src/components/report-modal.jsx:309
msgid "Send Report"
msgstr "Verzend rapport"

#: src/components/report-modal.jsx:318
msgid "Muted {username}"
msgstr "{username} gedempt"

#: src/components/report-modal.jsx:321
msgid "Unable to mute {username}"
msgstr "Kan {username} niet dempen"

#: src/components/report-modal.jsx:326
msgid "Send Report <0>+ Mute profile</0>"
msgstr "Verzend rapport <0>+ Demp profiel</0>"

#: src/components/report-modal.jsx:337
msgid "Blocked {username}"
msgstr "{username} geblokkeerd"

#: src/components/report-modal.jsx:340
msgid "Unable to block {username}"
msgstr "Kan {username} niet blokkeren"

#: src/components/report-modal.jsx:345
msgid "Send Report <0>+ Block profile</0>"
msgstr "Verzend rapport <0>+ Blokkeer profiel</0>"

#: src/components/search-form.jsx:203
msgid "{query} <0>‒ accounts, hashtags & posts</0>"
msgstr "{query} <0>‒ accounts, hashtags & berichten</0>"

#: src/components/search-form.jsx:216
msgid "Posts with <0>{query}</0>"
msgstr ""

#. placeholder {0}: query.replace(/^#/, '')
#: src/components/search-form.jsx:228
msgid "Posts tagged with <0>#{0}</0>"
msgstr ""

#: src/components/search-form.jsx:242
msgid "Look up <0>{query}</0>"
msgstr "Zoek <0>{query}</0>"

#: src/components/search-form.jsx:253
msgid "Accounts with <0>{query}</0>"
msgstr "Accounts met <0>{query}</0>"

#: src/components/shortcuts-settings.jsx:48
msgid "Home / Following"
msgstr "Startpagina / Volgend"

#: src/components/shortcuts-settings.jsx:51
msgid "Public (Local / Federated)"
msgstr "Openbaar (Lokaal / Gefederaliseerd)"

#: src/components/shortcuts-settings.jsx:53
msgid "Account"
msgstr "Account"

#: src/components/shortcuts-settings.jsx:56
msgid "Hashtag"
msgstr "Hashtag"

#: src/components/shortcuts-settings.jsx:63
msgid "List ID"
msgstr "Lijst-id"

#: src/components/shortcuts-settings.jsx:70
msgid "Local only"
msgstr "Alleen lokaal"

#: src/components/shortcuts-settings.jsx:75
#: src/components/shortcuts-settings.jsx:84
#: src/components/shortcuts-settings.jsx:122
#: src/pages/login.jsx:203
msgid "Instance"
msgstr "Instantie"

#: src/components/shortcuts-settings.jsx:78
#: src/components/shortcuts-settings.jsx:87
#: src/components/shortcuts-settings.jsx:125
msgid "Optional, e.g. mastodon.social"
msgstr "Optioneel, bijv. mastodon.social"

#: src/components/shortcuts-settings.jsx:93
msgid "Search term"
msgstr "Zoekterm"

#: src/components/shortcuts-settings.jsx:96
msgid "Optional, unless for multi-column mode"
msgstr "Optioneel, behalve voor de multi-kolommodus"

#: src/components/shortcuts-settings.jsx:113
msgid "e.g. PixelArt (Max 5, space-separated)"
msgstr "bijv. PixelArt (Max 5, spatie gescheiden)"

#: src/components/shortcuts-settings.jsx:117
#: src/pages/hashtag.jsx:360
msgid "Media only"
msgstr "Alleen media"

#: src/components/shortcuts-settings.jsx:235
#: src/components/shortcuts.jsx:218
msgid "Shortcuts"
msgstr "Snelkoppelingen"

#: src/components/shortcuts-settings.jsx:243
msgid "beta"
msgstr "bèta"

#: src/components/shortcuts-settings.jsx:249
msgid "Specify a list of shortcuts that'll appear as:"
msgstr ""

#: src/components/shortcuts-settings.jsx:255
msgid "Floating button"
msgstr "Zwevende knop"

#: src/components/shortcuts-settings.jsx:260
msgid "Tab/Menu bar"
msgstr "Tab-/Menubalk"

#: src/components/shortcuts-settings.jsx:265
msgid "Multi-column"
msgstr "Multi-kolom"

#: src/components/shortcuts-settings.jsx:332
msgid "Not available in current view mode"
msgstr "Niet beschikbaar in huidige weergavemodus"

#: src/components/shortcuts-settings.jsx:351
#: src/pages/accounts.jsx:188
msgid "Move up"
msgstr "Verplaats omhoog"

#: src/components/shortcuts-settings.jsx:367
#: src/pages/accounts.jsx:203
msgid "Move down"
msgstr "Verplaats omlaag"

#: src/components/shortcuts-settings.jsx:379
#: src/components/status.jsx:1474
#: src/pages/list.jsx:195
msgid "Edit"
msgstr "Bewerk"

#: src/components/shortcuts-settings.jsx:400
msgid "Add more than one shortcut/column to make this work."
msgstr ""

#: src/components/shortcuts-settings.jsx:411
msgid "No columns yet. Tap on the Add column button."
msgstr ""

#: src/components/shortcuts-settings.jsx:412
msgid "No shortcuts yet. Tap on the Add shortcut button."
msgstr ""

#: src/components/shortcuts-settings.jsx:415
msgid "Not sure what to add?<0/>Try adding <1>Home / Following and Notifications</1> first."
msgstr "Geen idee wat je kunt toevoegen?<0/>Probeer eerst <1>Startpagina/Volgend of Meldingen</1>."

#: src/components/shortcuts-settings.jsx:443
msgid "Max {SHORTCUTS_LIMIT} columns"
msgstr "Max. {SHORTCUTS_LIMIT} kolommen"

#: src/components/shortcuts-settings.jsx:444
msgid "Max {SHORTCUTS_LIMIT} shortcuts"
msgstr "Max. {SHORTCUTS_LIMIT} snelkoppelingen"

#: src/components/shortcuts-settings.jsx:458
msgid "Import/export"
msgstr "Importeer/Exporteer"

#: src/components/shortcuts-settings.jsx:468
msgid "Add column…"
msgstr "Voeg kolom toe…"

#: src/components/shortcuts-settings.jsx:469
msgid "Add shortcut…"
msgstr "Voeg snelkoppeling toe…"

#: src/components/shortcuts-settings.jsx:516
msgid "Specific list is optional. For multi-column mode, list is required, else the column will not be shown."
msgstr "Specifieke lijst is optioneel. Voor de multi-kolommodus is een lijst vereist, anders wordt de kolom niet getoond."

#: src/components/shortcuts-settings.jsx:517
msgid "For multi-column mode, search term is required, else the column will not be shown."
msgstr "Voor de multi-kolommodus is een zoekterm vereist, anders wordt de kolom niet getoond."

#: src/components/shortcuts-settings.jsx:518
msgid "Multiple hashtags are supported. Space-separated."
msgstr ""

#: src/components/shortcuts-settings.jsx:587
msgid "Edit shortcut"
msgstr "Bewerk snelkoppeling"

#: src/components/shortcuts-settings.jsx:587
msgid "Add shortcut"
msgstr "Voeg snelkoppeling toe"

#: src/components/shortcuts-settings.jsx:623
msgid "Timeline"
msgstr "Tijdlijn"

#: src/components/shortcuts-settings.jsx:649
msgid "List"
msgstr "Lijst"

#: src/components/shortcuts-settings.jsx:788
msgid "Import/Export <0>Shortcuts</0>"
msgstr "Importeer/Exporteer <0>Snelkoppelingen</0>"

#: src/components/shortcuts-settings.jsx:798
msgid "Import"
msgstr "Importeer"

#: src/components/shortcuts-settings.jsx:806
msgid "Paste shortcuts here"
msgstr "Plak snelkoppelingen hier"

#: src/components/shortcuts-settings.jsx:822
msgid "Downloading saved shortcuts from instance server…"
msgstr "Opgeslagen snelkoppelingen downloaden van instance server…"

#: src/components/shortcuts-settings.jsx:851
msgid "Unable to download shortcuts"
msgstr "Kan snelkoppelingen niet downloaden"

#: src/components/shortcuts-settings.jsx:854
msgid "Download shortcuts from instance server"
msgstr "Download snelkoppelingen van de instance server"

#: src/components/shortcuts-settings.jsx:912
msgid "* Exists in current shortcuts"
msgstr ""

#: src/components/shortcuts-settings.jsx:917
msgid "List may not work if it's from a different account."
msgstr ""

#: src/components/shortcuts-settings.jsx:927
msgid "Invalid settings format"
msgstr ""

#: src/components/shortcuts-settings.jsx:935
msgid "Append to current shortcuts?"
msgstr ""

#: src/components/shortcuts-settings.jsx:938
msgid "Only shortcuts that don’t exist in current shortcuts will be appended."
msgstr ""

#: src/components/shortcuts-settings.jsx:960
msgid "No new shortcuts to import"
msgstr "Geen nieuwe snelkoppelingen om te importeren"

#: src/components/shortcuts-settings.jsx:975
msgid "Shortcuts imported. Exceeded max {SHORTCUTS_LIMIT}, so the rest are not imported."
msgstr "Snelkoppelingen geïmporteerd. De maximum van {SHORTCUTS_LIMIT} is overschreden, de rest wordt niet geïmporteerd."

#: src/components/shortcuts-settings.jsx:976
#: src/components/shortcuts-settings.jsx:1000
msgid "Shortcuts imported"
msgstr "Snelkoppelingen geïmporteerd"

#: src/components/shortcuts-settings.jsx:986
msgid "Import & append…"
msgstr "Importeren & toevoegen…"

#: src/components/shortcuts-settings.jsx:994
msgid "Override current shortcuts?"
msgstr "Huidige snelkoppelingen overschrijven?"

#: src/components/shortcuts-settings.jsx:995
msgid "Import shortcuts?"
msgstr "Snelkoppelingen importeren?"

#: src/components/shortcuts-settings.jsx:1009
msgid "or override…"
msgstr "or overschrijven…"

#: src/components/shortcuts-settings.jsx:1009
msgid "Import…"
msgstr "Importeer…"

#: src/components/shortcuts-settings.jsx:1018
msgid "Export"
msgstr "Exporteer"

#: src/components/shortcuts-settings.jsx:1033
msgid "Shortcuts copied"
msgstr "Snelkoppelingen gekopieerd"

#: src/components/shortcuts-settings.jsx:1036
msgid "Unable to copy shortcuts"
msgstr "Kan snelkoppelingen niet kopiëren"

#: src/components/shortcuts-settings.jsx:1050
msgid "Shortcut settings copied"
msgstr "Snelkoppelingsinstellingen gekopieerd"

#: src/components/shortcuts-settings.jsx:1053
msgid "Unable to copy shortcut settings"
msgstr "Kan snelkoppelingsinstellingen niet kopiëren"

#: src/components/shortcuts-settings.jsx:1083
msgid "Share"
msgstr "Delen"

#: src/components/shortcuts-settings.jsx:1122
msgid "Saving shortcuts to instance server…"
msgstr "Snelkoppelingen opslaan naar instance server…"

#: src/components/shortcuts-settings.jsx:1129
msgid "Shortcuts saved"
msgstr "Snelkoppeling opgeslagen"

#: src/components/shortcuts-settings.jsx:1134
msgid "Unable to save shortcuts"
msgstr "Kan snelkoppelingen niet opslaan"

#: src/components/shortcuts-settings.jsx:1137
msgid "Sync to instance server"
msgstr "Synchroniseer naar de instance server"

#. placeholder {0}: shortcutsStr.length
#: src/components/shortcuts-settings.jsx:1145
msgid "{0, plural, one {# character} other {# characters}}"
msgstr "{0, plural, one {# teken} other {# tekens}}"

#: src/components/shortcuts-settings.jsx:1157
msgid "Raw Shortcuts JSON"
msgstr "Ruwe Snelkoppelingen JSON"

#: src/components/shortcuts-settings.jsx:1170
msgid "Import/export settings from/to instance server (Very experimental)"
msgstr "Importeer/exporteer instellingen van/naar instance server (zeer experimenteel)"

#: src/components/status.jsx:277
msgid "Unable to format math"
msgstr ""

#: src/components/status.jsx:292
msgid "Math expressions found."
msgstr ""

#: src/components/status.jsx:294
msgid "Show markup"
msgstr ""

#: src/components/status.jsx:294
msgid "Format math"
msgstr ""

#: src/components/status.jsx:689
msgid "<0/> <1>boosted</1>"
msgstr "<0/> <1>geboost</1>"

#: src/components/status.jsx:792
msgid "Sorry, your current logged-in instance can't interact with this post from another instance."
msgstr "Sorry, je momentieel ingelogde instantie kan niet communiceren met dit bericht van een andere instantie."

#. placeholder {0}: username || acct
#: src/components/status.jsx:946
msgid "Unliked @{0}'s post"
msgstr "@{0}'s bericht niet langer geliket"

#. placeholder {0}: username || acct
#: src/components/status.jsx:947
msgid "Liked @{0}'s post"
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:986
msgid "Unbookmarked @{0}'s post"
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:987
msgid "Bookmarked @{0}'s post"
msgstr ""

#: src/components/status.jsx:1086
msgid "Some media have no descriptions."
msgstr ""

#. placeholder {0}: rtf.format(-statusMonthsAgo, 'month')
#: src/components/status.jsx:1093
msgid "Old post (<0>{0}</0>)"
msgstr ""

#: src/components/status.jsx:1117
#: src/components/status.jsx:1157
#: src/components/status.jsx:2617
#: src/components/status.jsx:2640
msgid "Unboost"
msgstr "Niet langer boosten"

#: src/components/status.jsx:1133
#: src/components/status.jsx:2632
msgid "Quote"
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:1145
#: src/components/status.jsx:1611
msgid "Unboosted @{0}'s post"
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:1146
#: src/components/status.jsx:1612
msgid "Boosted @{0}'s post"
msgstr ""

#: src/components/status.jsx:1158
msgid "Boost…"
msgstr "Boost…"

#: src/components/status.jsx:1170
#: src/components/status.jsx:1905
#: src/components/status.jsx:2653
msgid "Unlike"
msgstr "Niet langer liken"

#: src/components/status.jsx:1171
#: src/components/status.jsx:1905
#: src/components/status.jsx:1906
#: src/components/status.jsx:2653
#: src/components/status.jsx:2654
msgid "Like"
msgstr "Like"

#: src/components/status.jsx:1180
#: src/components/status.jsx:2665
msgid "Unbookmark"
msgstr ""

#: src/components/status.jsx:1263
msgid "Post text copied"
msgstr ""

#: src/components/status.jsx:1266
msgid "Unable to copy post text"
msgstr ""

#: src/components/status.jsx:1272
msgid "Copy post text"
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:1290
msgid "View post by <0>@{0}</0>"
msgstr ""

#: src/components/status.jsx:1311
msgid "Show Edit History"
msgstr ""

#: src/components/status.jsx:1314
msgid "Edited: {editedDateText}"
msgstr ""

#: src/components/status.jsx:1381
#: src/components/status.jsx:3433
msgid "Embed post"
msgstr ""

#: src/components/status.jsx:1395
msgid "Conversation unmuted"
msgstr ""

#: src/components/status.jsx:1395
msgid "Conversation muted"
msgstr ""

#: src/components/status.jsx:1401
msgid "Unable to unmute conversation"
msgstr ""

#: src/components/status.jsx:1402
msgid "Unable to mute conversation"
msgstr ""

#: src/components/status.jsx:1411
msgid "Unmute conversation"
msgstr ""

#: src/components/status.jsx:1418
msgid "Mute conversation"
msgstr ""

#: src/components/status.jsx:1434
msgid "Post unpinned from profile"
msgstr ""

#: src/components/status.jsx:1435
msgid "Post pinned to profile"
msgstr ""

#: src/components/status.jsx:1440
msgid "Unable to unpin post"
msgstr ""

#: src/components/status.jsx:1440
msgid "Unable to pin post"
msgstr ""

#: src/components/status.jsx:1449
msgid "Unpin from profile"
msgstr ""

#: src/components/status.jsx:1456
msgid "Pin to profile"
msgstr ""

#: src/components/status.jsx:1485
msgid "Delete this post?"
msgstr ""

#: src/components/status.jsx:1501
msgid "Post deleted"
msgstr ""

#: src/components/status.jsx:1504
msgid "Unable to delete post"
msgstr "Kan bericht niet verwijderen"

#: src/components/status.jsx:1532
msgid "Report post…"
msgstr ""

#: src/components/status.jsx:1906
#: src/components/status.jsx:1942
#: src/components/status.jsx:2654
msgid "Liked"
msgstr "Geliket"

#: src/components/status.jsx:1939
#: src/components/status.jsx:2641
msgid "Boosted"
msgstr "Geboost"

#: src/components/status.jsx:1949
#: src/components/status.jsx:2666
msgid "Bookmarked"
msgstr "Bladwijzer opgeslagen"

#: src/components/status.jsx:1953
msgid "Pinned"
msgstr "Vastgezet"

#: src/components/status.jsx:1999
#: src/components/status.jsx:2478
msgid "Deleted"
msgstr "Verwijderd"

#: src/components/status.jsx:2040
msgid "{repliesCount, plural, one {# reply} other {# replies}}"
msgstr "{repliesCount, plural, one {# antwoord} other {# antwoorden}}"

#. placeholder {0}: snapStates.statusThreadNumber[sKey] ? ` ${snapStates.statusThreadNumber[sKey]}/X` : ''
#: src/components/status.jsx:2130
msgid "Thread{0}"
msgstr "Draad{0}"

#: src/components/status.jsx:2208
#: src/components/status.jsx:2270
#: src/components/status.jsx:2374
msgid "Show less"
msgstr "Toon minder"

#: src/components/status.jsx:2208
#: src/components/status.jsx:2270
msgid "Show content"
msgstr "Toon inhoud"

#. placeholder {0}: filterInfo.titlesStr
#. placeholder {0}: filterInfo?.titlesStr
#: src/components/status.jsx:2370
#: src/pages/catchup.jsx:1879
msgid "Filtered: {0}"
msgstr "Gefilterd: {0}"

#: src/components/status.jsx:2374
msgid "Show media"
msgstr "Toon media"

#: src/components/status.jsx:2514
msgid "Edited"
msgstr "Bewerkt"

#: src/components/status.jsx:2591
msgid "Comments"
msgstr "Antwoorden"

#. More from [Author]
#: src/components/status.jsx:2891
msgid "More from <0/>"
msgstr ""

#: src/components/status.jsx:3193
msgid "Edit History"
msgstr "Bewerkingsgeschiedenis"

#: src/components/status.jsx:3197
msgid "Failed to load history"
msgstr "Kon geschiedenis niet laden"

#: src/components/status.jsx:3202
#: src/pages/annual-report.jsx:45
msgid "Loading…"
msgstr "Laden…"

#: src/components/status.jsx:3438
msgid "HTML Code"
msgstr "HTML code"

#: src/components/status.jsx:3455
msgid "HTML code copied"
msgstr "HTML code gekopieerd"

#: src/components/status.jsx:3458
msgid "Unable to copy HTML code"
msgstr "Kan HTML-code niet kopiëren"

#: src/components/status.jsx:3470
msgid "Media attachments:"
msgstr "Mediabijlagen:"

#: src/components/status.jsx:3492
msgid "Account Emojis:"
msgstr "Account emoji's:"

#: src/components/status.jsx:3523
#: src/components/status.jsx:3568
msgid "static URL"
msgstr "statische URL"

#: src/components/status.jsx:3537
msgid "Emojis:"
msgstr "Emoji's:"

#: src/components/status.jsx:3582
msgid "Notes:"
msgstr "Notities:"

#: src/components/status.jsx:3586
msgid "This is static, unstyled and scriptless. You may need to apply your own styles and edit as needed."
msgstr "Dit is statisch, zonder vormgeving en scriptloos. Je moet mogelijk je eigen vormgeving toepassen en bewerken waar nodig."

#: src/components/status.jsx:3592
msgid "Polls are not interactive, becomes a list with vote counts."
msgstr "Peilingen zijn niet interactief, wordt een lijst met uitslagen."

#: src/components/status.jsx:3597
msgid "Media attachments can be images, videos, audios or any file types."
msgstr "Mediabijlagen kunnen afbeeldingen, video's, audiobestanden of andere bestandstypes zijn."

#: src/components/status.jsx:3603
msgid "Post could be edited or deleted later."
msgstr ""

#: src/components/status.jsx:3609
msgid "Preview"
msgstr ""

#: src/components/status.jsx:3618
msgid "Note: This preview is lightly styled."
msgstr "Let op: Deze voorvertoning is licht vormgegeven."

#. [Name] [Visibility icon] boosted
#: src/components/status.jsx:3871
msgid "<0/> <1/> boosted"
msgstr "<0/> <1/> geboost"

#: src/components/status.jsx:3973
msgid "Post hidden by your filters"
msgstr ""

#: src/components/status.jsx:3974
msgid "Post removed by author."
msgstr ""

#: src/components/status.jsx:3975
msgid "You’re not authorized to view this post."
msgstr ""

#: src/components/status.jsx:3976
msgid "Post pending author approval."
msgstr ""

#: src/components/status.jsx:3977
#: src/components/status.jsx:3978
msgid "Quoting not allowed by the author."
msgstr ""

#: src/components/timeline.jsx:485
#: src/pages/settings.jsx:1218
msgid "New posts"
msgstr "Nieuwe berichten"

#: src/components/timeline.jsx:586
#: src/pages/home.jsx:228
#: src/pages/notifications.jsx:898
#: src/pages/status.jsx:1077
#: src/pages/status.jsx:1454
msgid "Try again"
msgstr ""

#. placeholder {0}: fItems.length
#: src/components/timeline.jsx:621
msgid "{0, plural, one {# Boost} other {# Boosts}}"
msgstr "{0, plural, one {# Boost} other {# Boosts}}"

#: src/components/timeline.jsx:626
msgid "Pinned posts"
msgstr ""

#: src/components/timeline.jsx:985
#: src/components/timeline.jsx:992
#: src/pages/catchup.jsx:1897
msgid "Thread"
msgstr ""

#. placeholder {0}: filterInfo.titlesStr
#: src/components/timeline.jsx:1007
msgid "<0>Filtered</0>: <1>{0}</1>"
msgstr ""

#: src/components/translation-block.jsx:196
msgid "Auto-translated from {sourceLangText}"
msgstr ""

#: src/components/translation-block.jsx:234
msgid "Translating…"
msgstr ""

#: src/components/translation-block.jsx:237
msgid "Translate from {sourceLangText} (auto-detected)"
msgstr ""

#: src/components/translation-block.jsx:238
msgid "Translate from {sourceLangText}"
msgstr ""

#. placeholder {0}: detectedLang ?? '…'
#: src/components/translation-block.jsx:266
msgid "Auto ({0})"
msgstr ""

#: src/components/translation-block.jsx:279
msgid "Failed to translate"
msgstr ""

#: src/compose.jsx:33
msgid "Editing source status"
msgstr "Bron status bewerken"

#. placeholder {0}: replyToStatus.account?.acct || replyToStatus.account?.username
#: src/compose.jsx:35
msgid "Replying to @{0}"
msgstr "Antwoorden op @{0}"

#: src/compose.jsx:63
msgid "You may close this page now."
msgstr ""

#: src/compose.jsx:71
msgid "Close window"
msgstr ""

#: src/compose.jsx:87
msgid "Login required."
msgstr ""

#: src/compose.jsx:91
#: src/pages/annual-report.jsx:164
#: src/pages/http-route.jsx:91
#: src/pages/login.jsx:280
msgid "Go home"
msgstr "Naar startpagina"

#: src/pages/account-statuses.jsx:238
msgid "Account posts"
msgstr "Berichten van account"

#: src/pages/account-statuses.jsx:245
msgid "{accountDisplay} (+ Replies)"
msgstr "{accountDisplay} (+ Antwoorden)"

#: src/pages/account-statuses.jsx:247
msgid "{accountDisplay} (- Boosts)"
msgstr "{accountDisplay} (- Boosts)"

#: src/pages/account-statuses.jsx:249
msgid "{accountDisplay} (#{tagged})"
msgstr "{accountDisplay} (#{tagged})"

#: src/pages/account-statuses.jsx:251
msgid "{accountDisplay} (Media)"
msgstr "{accountDisplay} (Media)"

#: src/pages/account-statuses.jsx:257
msgid "{accountDisplay} ({monthYear})"
msgstr "{accountDisplay} ({monthYear})"

#: src/pages/account-statuses.jsx:323
msgid "Clear filters"
msgstr "Wis filters"

#: src/pages/account-statuses.jsx:326
msgid "Clear"
msgstr "Wis"

#: src/pages/account-statuses.jsx:340
msgid "Showing post with replies"
msgstr "Berichten met antwoorden worden nu getoond"

#: src/pages/account-statuses.jsx:345
msgid "+ Replies"
msgstr "+ Antwoorden"

#: src/pages/account-statuses.jsx:351
msgid "Showing posts without boosts"
msgstr "Alle berichten zonder boosts"

#: src/pages/account-statuses.jsx:356
msgid "- Boosts"
msgstr "- Boosts"

#: src/pages/account-statuses.jsx:362
msgid "Showing posts with media"
msgstr "Berichten met media worden nu getoond"

#. placeholder {0}: tag.name
#: src/pages/account-statuses.jsx:379
msgid "Showing posts tagged with #{0}"
msgstr "Alle berichten met tag #{0}"

#. placeholder {0}: date.toLocaleString(i18n.locale, { month: 'long', year: 'numeric', })
#: src/pages/account-statuses.jsx:418
msgid "Showing posts in {0}"
msgstr ""

#: src/pages/account-statuses.jsx:508
msgid "Nothing to see here yet."
msgstr "Nog niets te zien hier."

#: src/pages/account-statuses.jsx:509
#: src/pages/public.jsx:99
#: src/pages/trending.jsx:452
msgid "Unable to load posts"
msgstr "Kan berichten niet laden"

#: src/pages/account-statuses.jsx:550
#: src/pages/account-statuses.jsx:580
msgid "Unable to fetch account info"
msgstr "Kan accountgegevens niet ophalen"

#. placeholder {0}: accountInstance ? ( <> {' '} (<b>{punycode.toUnicode(accountInstance)}</b>) </> ) : null
#: src/pages/account-statuses.jsx:557
msgid "Switch to account's instance {0}"
msgstr "Overschakelen naar instantie van account {0}"

#: src/pages/account-statuses.jsx:587
msgid "Switch to my instance (<0>{currentInstance}</0>)"
msgstr "Overschakelen naar mijn instantie (<0>{currentInstance}</0>)"

#: src/pages/account-statuses.jsx:660
msgid "Month"
msgstr ""

#: src/pages/accounts.jsx:64
msgid "Current"
msgstr ""

#: src/pages/accounts.jsx:110
msgid "Default"
msgstr ""

#: src/pages/accounts.jsx:132
msgid "Switch to this account"
msgstr "Overschakelen naar dit account"

#: src/pages/accounts.jsx:141
msgid "Switch in new tab/window"
msgstr ""

#: src/pages/accounts.jsx:155
msgid "View profile…"
msgstr ""

#: src/pages/accounts.jsx:173
msgid "Set as default"
msgstr ""

#. placeholder {0}: account.info.acct
#: src/pages/accounts.jsx:215
msgid "Log out <0>@{0}</0>?"
msgstr ""

#: src/pages/accounts.jsx:244
msgid "Log out…"
msgstr ""

#. placeholder {0}: niceDateTime(account.createdAt)
#: src/pages/accounts.jsx:251
msgid "Connected on {0} (<0/>)"
msgstr ""

#: src/pages/accounts.jsx:268
msgid "Add an existing account"
msgstr ""

#: src/pages/accounts.jsx:275
msgid "Note: <0>Default</0> account will always be used for first load. Switched accounts will persist during the session."
msgstr "Let op: <0>Standaard</0> account zal altijd bij het het eerste laden worden gebruikt. Overgeschakelde accounts worden onthouden tijdens je sessie."

#: src/pages/bookmarks.jsx:28
msgid "No bookmarks yet. Go bookmark something!"
msgstr ""

#: src/pages/bookmarks.jsx:29
msgid "Unable to load bookmarks."
msgstr "Kan bladwijzers niet laden."

#: src/pages/catchup.jsx:54
msgid "last 1 hour"
msgstr "het afgelopen uur"

#: src/pages/catchup.jsx:55
msgid "last 2 hours"
msgstr "de afgelopen 2 uur"

#: src/pages/catchup.jsx:56
msgid "last 3 hours"
msgstr "de afgelopen 3 uur"

#: src/pages/catchup.jsx:57
msgid "last 4 hours"
msgstr "de afgelopen 4 uur"

#: src/pages/catchup.jsx:58
msgid "last 5 hours"
msgstr "de afgelopen 5 uur"

#: src/pages/catchup.jsx:59
msgid "last 6 hours"
msgstr "de afgelopen 6 uur"

#: src/pages/catchup.jsx:60
msgid "last 7 hours"
msgstr "de afgelopen 7 uur"

#: src/pages/catchup.jsx:61
msgid "last 8 hours"
msgstr "de afgelopen 8 uur"

#: src/pages/catchup.jsx:62
msgid "last 9 hours"
msgstr "de afgelopen 9 uur"

#: src/pages/catchup.jsx:63
msgid "last 10 hours"
msgstr "de afgelopen 10 uur"

#: src/pages/catchup.jsx:64
msgid "last 11 hours"
msgstr "de afgelopen 11 uur"

#: src/pages/catchup.jsx:65
msgid "last 12 hours"
msgstr "de afgelopen 12 uur"

#: src/pages/catchup.jsx:66
msgid "beyond 12 hours"
msgstr "meer dan 12 uur geleden"

#: src/pages/catchup.jsx:73
msgid "Followed tags"
msgstr "Gevolgde hashtags"

#: src/pages/catchup.jsx:74
msgid "Groups"
msgstr "Groepen"

#: src/pages/catchup.jsx:597
msgid "Showing {selectedFilterCategory, select, all {all posts} original {original posts} replies {replies} boosts {boosts} followedTags {followed tags} groups {groups} filtered {filtered posts}}, {sortBy, select, createdAt {{sortOrder, select, asc {oldest} desc {latest}}} reblogsCount {{sortOrder, select, asc {fewest boosts} desc {most boosts}}} favouritesCount {{sortOrder, select, asc {fewest likes} desc {most likes}}} repliesCount {{sortOrder, select, asc {fewest replies} desc {most replies}}} density {{sortOrder, select, asc {least dense} desc {most dense}}}} first{groupBy, select, account {, grouped by authors} other {}}"
msgstr "{selectedFilterCategory, select, all {Alle berichten} original {Alle originele berichten} replies {Alle antwoorden} boosts {Alle boosts} followedTags {Alle gevolgde tags} groups {Alle groepen} filtered {Alle gefilterde berichten}}, {sortBy, select, createdAt {{sortOrder, select, asc {oudste} desc {nieuwste}}} reblogsCount {{sortOrder, select, asc {minste boosts} desc {meeste boosts}}} favouritesCount {{sortOrder, select, asc {minste likes} desc {meeste likes}}} repliesCount {{sortOrder, select, asc {minste antwoorden} desc {meeste antwoorden}}} density {{sortOrder, select, asc {minst dichte} desc {meest dichte}}}} first{groupBy, select, account {, gegroepeerd per schrijver} other {}}"

#: src/pages/catchup.jsx:889
#: src/pages/catchup.jsx:913
msgid "Catch-up <0>beta</0>"
msgstr "Inhalen <0>beta</0>"

#: src/pages/catchup.jsx:903
#: src/pages/catchup.jsx:1588
msgid "Help"
msgstr "Hulp"

#: src/pages/catchup.jsx:919
msgid "What is this?"
msgstr "Wat is dit?"

#: src/pages/catchup.jsx:922
msgid "Catch-up is a separate timeline for your followings, offering a high-level view at a glance, with a simple, email-inspired interface to effortlessly sort and filter through posts."
msgstr "Inhalen is een aparte tijdlijn voor de accounts die je volgt, dat een weergave op hoog niveau in één oogopslag biedt met een eenvoudige, e-mailgeïnspireerde interface om moeiteloos door berichten te sorteren en filteren."

#: src/pages/catchup.jsx:933
msgid "Preview of Catch-up UI"
msgstr "Voorbeeld van de Inhalen UI"

#: src/pages/catchup.jsx:942
msgid "Let's catch up"
msgstr "Laten we inhalen"

#: src/pages/catchup.jsx:947
msgid "Let's catch up on the posts from your followings."
msgstr "Laten we de posts van je de accounts die je volgt inhalen."

#: src/pages/catchup.jsx:951
msgid "Show me all posts from…"
msgstr "Toon me alle berichten van…"

#: src/pages/catchup.jsx:974
msgid "until the max"
msgstr "tot het maximum"

#: src/pages/catchup.jsx:1004
msgid "Catch up"
msgstr "Catch up"

#: src/pages/catchup.jsx:1010
msgid "Overlaps with your last catch-up"
msgstr "Overlapt met je laatste catch-up"

#. placeholder {0}: dtf.format(new Date(lastCatchupEndAt))
#: src/pages/catchup.jsx:1022
msgid "Until the last catch-up ({0})"
msgstr "Tot aan de laatste catch-up ({0})"

#: src/pages/catchup.jsx:1031
msgid "Note: your instance might only show a maximum of 800 posts in the Home timeline regardless of the time range. Could be less or more."
msgstr "Let op: je instantie mag max. 800 berichten in de startpaginatijdlijn tonen, ongeacht het tijdsbereik. Kan minder of meer berichten zijn."

#: src/pages/catchup.jsx:1041
msgid "Previously…"
msgstr "Eerder…"

#. placeholder {0}: pc.count
#: src/pages/catchup.jsx:1059
msgid "{0, plural, one {# post} other {# posts}}"
msgstr "{0, plural, one {# bericht} other {# berichten}}"

#: src/pages/catchup.jsx:1069
msgid "Remove this catch-up?"
msgstr "Deze catch-up verwijderen?"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1072
msgid "Removing Catch-up {0}"
msgstr ""

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1076
msgid "Catch-up {0} removed"
msgstr ""

#: src/pages/catchup.jsx:1090
msgid "Note: Only max 3 will be stored. The rest will be automatically removed."
msgstr "Let op: Max. 3 worden opgeslagen. De rest wordt automatisch verwijderd."

#: src/pages/catchup.jsx:1105
msgid "Fetching posts…"
msgstr "Berichten ophalen…"

#: src/pages/catchup.jsx:1108
msgid "This might take a while."
msgstr "Dit kan even duren."

#: src/pages/catchup.jsx:1143
msgid "Reset filters"
msgstr "Filters terug zetten"

#: src/pages/catchup.jsx:1151
#: src/pages/catchup.jsx:1594
msgid "Top links"
msgstr "Top links"

#. placeholder {0}: sharers.map((s) => { const { avatarStatic, displayName } = s; return ( <button type="button" class="plain" style={{ padding: 0, }} onClick={(e) => { e.preventDefault(); e.stopPropagation(); // Reset and filter to author const { id } = s; setSelectedAuthor(id); setSelectedFilterCategory('all'); }} > <Avatar url={avatarStatic} size="s" alt={displayName} /> </button> ); })
#: src/pages/catchup.jsx:1264
msgid "Shared by {0}"
msgstr "Gedeeld door {0}"

#: src/pages/catchup.jsx:1319
#: src/pages/mentions.jsx:154
#: src/pages/search.jsx:329
msgid "All"
msgstr "Alles"

#. placeholder {0}: authorCountsList.length
#: src/pages/catchup.jsx:1404
msgid "{0, plural, one {# author} other {# authors}}"
msgstr "{0, plural, one {# auteur} other {# auteurs}}"

#: src/pages/catchup.jsx:1416
msgid "Sort"
msgstr "Sorteer"

#: src/pages/catchup.jsx:1447
msgid "Date"
msgstr "Datum"

#: src/pages/catchup.jsx:1451
msgid "Density"
msgstr "Dichtheid"

#. js-lingui-explicit-id
#: src/pages/catchup.jsx:1474
msgid "group.filter"
msgstr ""

#: src/pages/catchup.jsx:1489
msgid "Authors"
msgstr "Auteurs"

#: src/pages/catchup.jsx:1490
msgid "None"
msgstr "Geen"

#: src/pages/catchup.jsx:1506
msgid "Show all authors"
msgstr "Toon alle auteurs"

#: src/pages/catchup.jsx:1557
msgid "You don't have to read everything."
msgstr "Je hoeft niet alles te lezen."

#: src/pages/catchup.jsx:1558
msgid "That's all."
msgstr "Dat was het."

#: src/pages/catchup.jsx:1566
msgid "Back to top"
msgstr "Terug naar boven"

#: src/pages/catchup.jsx:1597
msgid "Links shared by followings, sorted by shared counts, boosts and likes."
msgstr "Links gedeeld door volgenden, gesorteerd op hoe vaak het is gedeeld, geboost en geliket."

#: src/pages/catchup.jsx:1603
msgid "Sort: Density"
msgstr "Sorteer: Dichtheid"

#: src/pages/catchup.jsx:1606
msgid "Posts are sorted by information density or depth. Shorter posts are \"lighter\" while longer posts are \"heavier\". Posts with photos are \"heavier\" than posts without photos."
msgstr ""

#: src/pages/catchup.jsx:1613
msgid "Group: Authors"
msgstr "Groep: Auteurs"

#: src/pages/catchup.jsx:1616
msgid "Posts are grouped by authors, sorted by posts count per author."
msgstr ""

#: src/pages/catchup.jsx:1663
msgid "Next author"
msgstr "Volgende auteur"

#: src/pages/catchup.jsx:1671
msgid "Previous author"
msgstr "Vorige auteur"

#: src/pages/catchup.jsx:1687
msgid "Scroll to top"
msgstr "Scroll naar boven"

#: src/pages/favourites.jsx:28
msgid "No likes yet. Go like something!"
msgstr "Nog geen likes. Ga iets liken!"

#: src/pages/favourites.jsx:29
msgid "Unable to load likes."
msgstr "Kan likes niet laden."

#: src/pages/filters.jsx:23
msgid "Home and lists"
msgstr "Startpagina en lijsten"

#: src/pages/filters.jsx:25
msgid "Public timelines"
msgstr "Openbare tijdlijnen"

#: src/pages/filters.jsx:26
msgid "Conversations"
msgstr "Discussies"

#: src/pages/filters.jsx:27
msgid "Profiles"
msgstr "Profielen"

#: src/pages/filters.jsx:42
msgid "Never"
msgstr "Nooit"

#: src/pages/filters.jsx:104
#: src/pages/filters.jsx:229
msgid "New filter"
msgstr "Nieuw filter"

#. placeholder {0}: filters.length
#: src/pages/filters.jsx:152
msgid "{0, plural, one {# filter} other {# filters}}"
msgstr "{0, plural, one {# filter} other {# filters}}"

#: src/pages/filters.jsx:167
msgid "Unable to load filters."
msgstr "Kan filters niet laden."

#: src/pages/filters.jsx:171
msgid "No filters yet."
msgstr "Nog geen filters."

#: src/pages/filters.jsx:178
msgid "Add filter"
msgstr "Voeg filter toe"

#: src/pages/filters.jsx:229
msgid "Edit filter"
msgstr "Bewerk filter"

#: src/pages/filters.jsx:346
msgid "Unable to edit filter"
msgstr "Kan filter niet bewerken"

#: src/pages/filters.jsx:347
msgid "Unable to create filter"
msgstr "Kan filter niet aanmaken"

#: src/pages/filters.jsx:356
msgid "Title"
msgstr "Titel"

#: src/pages/filters.jsx:397
msgid "Whole word"
msgstr "Volledig woord"

#: src/pages/filters.jsx:423
msgid "No keywords. Add one."
msgstr "Geen trefwoorden. Voeg er één toe."

#: src/pages/filters.jsx:450
msgid "Add keyword"
msgstr "Voeg trefwoord toe"

#. placeholder {0}: filteredEditKeywords.length
#: src/pages/filters.jsx:454
msgid "{0, plural, one {# keyword} other {# keywords}}"
msgstr "{0, plural, one {# trefwoord} other {# trefwoorden}}"

#: src/pages/filters.jsx:467
msgid "Filter from…"
msgstr "Filter op…"

#: src/pages/filters.jsx:493
msgid "* Not implemented yet"
msgstr "* Nog niet geïmplementeerd"

#: src/pages/filters.jsx:499
msgid "Status: <0><1/></0>"
msgstr "Status: <0><1/></0>"

#: src/pages/filters.jsx:508
msgid "Change expiry"
msgstr "Wijzig vervaldatum"

#: src/pages/filters.jsx:508
msgid "Expiry"
msgstr "Vervaldatum"

#: src/pages/filters.jsx:527
msgid "Filtered post will be…"
msgstr "Gefilterd bericht zal worden…"

#: src/pages/filters.jsx:538
msgid "obscured (media only)"
msgstr ""

#: src/pages/filters.jsx:552
msgid "minimized"
msgstr "geminimaliseerd"

#: src/pages/filters.jsx:562
msgid "hidden"
msgstr "verborgen"

#: src/pages/filters.jsx:579
msgid "Delete this filter?"
msgstr "Verwijder dit filter?"

#: src/pages/filters.jsx:592
msgid "Unable to delete filter."
msgstr "Kan filter niet verwijderen."

#: src/pages/filters.jsx:625
msgid "Expired"
msgstr "Vervallen"

#: src/pages/filters.jsx:627
msgid "Expiring <0/>"
msgstr "Vervalt op <0/>"

#: src/pages/filters.jsx:631
msgid "Never expires"
msgstr "Vervalt nooit"

#. placeholder {0}: followedHashtags.length
#: src/pages/followed-hashtags.jsx:71
msgid "{0, plural, one {# hashtag} other {# hashtags}}"
msgstr "{0, plural, one {# hashtag} other {# hashtags}}"

#: src/pages/followed-hashtags.jsx:86
msgid "Unable to load followed hashtags."
msgstr "Kan gevolgde hashtags niet laden."

#: src/pages/followed-hashtags.jsx:90
msgid "No hashtags followed yet."
msgstr "Nog geen hashtags gevolgd."

#: src/pages/following.jsx:147
msgid "Nothing to see here."
msgstr "Niets te zien hier."

#: src/pages/following.jsx:148
#: src/pages/list.jsx:109
msgid "Unable to load posts."
msgstr "Kan berichten niet laden."

#: src/pages/hashtag.jsx:56
msgid "{hashtagTitle} (Media only) on {instance}"
msgstr "{hashtagTitle} (Alleen media) uit {instance}"

#: src/pages/hashtag.jsx:57
msgid "{hashtagTitle} on {instance}"
msgstr "{hashtagTitle} uit {instance}"

#: src/pages/hashtag.jsx:59
msgid "{hashtagTitle} (Media only)"
msgstr "{hashtagTitle} (Alleen media)"

#: src/pages/hashtag.jsx:60
msgid "{hashtagTitle}"
msgstr "{hashtagTitle}"

#: src/pages/hashtag.jsx:185
msgid "No one has posted anything with this tag yet."
msgstr "Nog niemand heeft iets met deze hashtag geplaatst."

#: src/pages/hashtag.jsx:186
msgid "Unable to load posts with this tag"
msgstr "Kan berichten met deze tag niet laden"

#: src/pages/hashtag.jsx:212
msgid "Unfollow #{hashtag}?"
msgstr "Ontvolg #{hashtag}?"

#: src/pages/hashtag.jsx:227
msgid "Unfollowed #{hashtag}"
msgstr "#{hashtag} ontvolgd"

#: src/pages/hashtag.jsx:242
msgid "Followed #{hashtag}"
msgstr "#{hashtag} gevolgd"

#: src/pages/hashtag.jsx:258
msgid "Following…"
msgstr "Volgend…"

#: src/pages/hashtag.jsx:287
msgid "Unfeatured on profile"
msgstr ""

#: src/pages/hashtag.jsx:301
msgid "Unable to unfeature on profile"
msgstr ""

#: src/pages/hashtag.jsx:310
#: src/pages/hashtag.jsx:326
msgid "Featured on profile"
msgstr ""

#: src/pages/hashtag.jsx:398
msgid "{TOTAL_TAGS_LIMIT, plural, other {Max # tags}}"
msgstr ""

#: src/pages/hashtag.jsx:401
msgid "Add hashtag"
msgstr "Voeg hashtag toe"

#: src/pages/hashtag.jsx:433
msgid "Remove hashtag"
msgstr "Verwijder hashtag"

#: src/pages/hashtag.jsx:447
msgid "{SHORTCUTS_LIMIT, plural, one {Max # shortcut reached. Unable to add shortcut.} other {Max # shortcuts reached. Unable to add shortcut.}}"
msgstr ""

#: src/pages/hashtag.jsx:476
msgid "This shortcut already exists"
msgstr ""

#: src/pages/hashtag.jsx:479
msgid "Hashtag shortcut added"
msgstr ""

#: src/pages/hashtag.jsx:485
msgid "Add to Shortcuts"
msgstr "Voeg aan Snelkoppelingen toe"

#: src/pages/hashtag.jsx:491
#: src/pages/public.jsx:141
#: src/pages/trending.jsx:481
msgid "Enter a new instance e.g. \"mastodon.social\""
msgstr "Voer een nieuwe instantie in, bijv. \"mastodon.social\""

#: src/pages/hashtag.jsx:494
#: src/pages/public.jsx:144
#: src/pages/trending.jsx:484
msgid "Invalid instance"
msgstr "Ongeldige instantie"

#: src/pages/hashtag.jsx:508
#: src/pages/public.jsx:158
#: src/pages/trending.jsx:496
msgid "Go to another instance…"
msgstr "Ga naar een andere instantie…"

#: src/pages/hashtag.jsx:521
#: src/pages/public.jsx:171
#: src/pages/trending.jsx:507
msgid "Go to my instance (<0>{currentInstance}</0>)"
msgstr "Ga naar mijn instantie (<0>{currentInstance}</0>)"

#: src/pages/home.jsx:224
msgid "Unable to fetch notifications."
msgstr "Kan meldingen niet ophalen."

#: src/pages/home.jsx:245
msgid "<0>New</0> <1>Follow Requests</1>"
msgstr ""

#: src/pages/home.jsx:251
msgid "See all"
msgstr "Bekijk alles"

#: src/pages/http-route.jsx:68
msgid "Resolving…"
msgstr "Bezig met verwerken…"

#: src/pages/http-route.jsx:79
msgid "Unable to resolve URL"
msgstr "Kan URL niet vinden"

#: src/pages/list.jsx:108
msgid "Nothing yet."
msgstr "Nog niks."

#: src/pages/list.jsx:201
#: src/pages/list.jsx:307
msgid "Manage members"
msgstr "Beheer leden"

#. placeholder {0}: account.username
#: src/pages/list.jsx:342
msgid "Remove <0>@{0}</0> from list?"
msgstr "Verwijder <0>@{0}</0> van lijst?"

#: src/pages/list.jsx:388
msgid "Remove…"
msgstr "Verwijder…"

#. placeholder {0}: lists.length
#: src/pages/lists.jsx:115
msgid "{0, plural, one {# list} other {# lists}}"
msgstr "{0, plural, one {# lijst} other {# lijsten}}"

#: src/pages/lists.jsx:135
msgid "No lists yet."
msgstr "Nog geen lijsten."

#: src/pages/login.jsx:122
#: src/pages/login.jsx:132
msgid "Failed to register application"
msgstr ""

#: src/pages/login.jsx:218
msgid "instance domain"
msgstr "instantiedomein"

#: src/pages/login.jsx:242
msgid "e.g. “mastodon.social”"
msgstr "bijv. “mastodon.social”"

#: src/pages/login.jsx:253
msgid "Failed to log in. Please try again or try another instance."
msgstr "Inloggen mislukt. Probeer het opnieuw of probeer een andere instantie."

#: src/pages/login.jsx:265
msgid "Continue with {selectedInstanceText}"
msgstr "Doorgaan met {selectedInstanceText}"

#: src/pages/login.jsx:266
msgid "Continue"
msgstr "Doorgaan"

#: src/pages/login.jsx:274
msgid "Don't have an account? Create one!"
msgstr "Nog geen account? Maak er een aan!"

#: src/pages/mentions.jsx:21
msgid "Private mentions"
msgstr "Privévermeldingen"

#: src/pages/mentions.jsx:166
msgid "Private"
msgstr "Privé"

#: src/pages/mentions.jsx:176
msgid "No one mentioned you :("
msgstr "Niemand vermeld je :("

#: src/pages/mentions.jsx:177
msgid "Unable to load mentions."
msgstr "Kan vermeldingen niet laden."

#: src/pages/notifications.jsx:108
msgid "You don't follow"
msgstr "Die je niet volgt"

#: src/pages/notifications.jsx:109
msgid "Who don't follow you"
msgstr "Die je niet volgen"

#: src/pages/notifications.jsx:110
msgid "With a new account"
msgstr "Met een nieuw account"

#: src/pages/notifications.jsx:111
msgid "Who unsolicitedly private mention you"
msgstr "Die je ongevraagd privé noemen"

#: src/pages/notifications.jsx:112
msgid "Who are limited by server moderators"
msgstr ""

#: src/pages/notifications.jsx:605
#: src/pages/notifications.jsx:946
msgid "Notifications settings"
msgstr "Meldingsinstellingen"

#: src/pages/notifications.jsx:623
msgid "New notifications"
msgstr "Nieuwe meldingen"

#. placeholder {0}: announcements.length
#: src/pages/notifications.jsx:634
msgid "{0, plural, one {Announcement} other {Announcements}}"
msgstr "{0, plural, one {Mededeling} other {Mededelingen}}"

#: src/pages/notifications.jsx:681
#: src/pages/settings.jsx:1206
msgid "Follow requests"
msgstr "Volgverzoeken"

#. placeholder {0}: followRequests.length
#: src/pages/notifications.jsx:686
msgid "{0, plural, one {# follow request} other {# follow requests}}"
msgstr "{0, plural, one {# volgverzoek} other {# volgverzoeken}}"

#. placeholder {0}: notificationsPolicy.summary.pendingRequestsCount
#: src/pages/notifications.jsx:741
msgid "{0, plural, one {Filtered notifications from # person} other {Filtered notifications from # people}}"
msgstr ""

#: src/pages/notifications.jsx:814
msgid "Only mentions"
msgstr "Alleen vermeldingen"

#: src/pages/notifications.jsx:818
msgid "Today"
msgstr "Vandaag"

#: src/pages/notifications.jsx:823
msgid "You're all caught up."
msgstr "Je bent weer helemaal bij."

#: src/pages/notifications.jsx:846
msgid "Yesterday"
msgstr "Gisteren"

#: src/pages/notifications.jsx:894
msgid "Unable to load notifications"
msgstr "Kan meldingen niet laden"

#: src/pages/notifications.jsx:973
msgid "Notifications settings updated"
msgstr "Meldingsinstellingen bijgewerkt"

#: src/pages/notifications.jsx:981
msgid "Filter out notifications from people:"
msgstr ""

#: src/pages/notifications.jsx:995
msgid "Filter"
msgstr "Filter"

#: src/pages/notifications.jsx:998
msgid "Ignore"
msgstr "Negeren"

#. placeholder {0}: niceDateTime(updatedAtDate)
#: src/pages/notifications.jsx:1071
msgid "Updated <0>{0}</0>"
msgstr ""

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1139
msgid "View notifications from <0>@{0}</0>"
msgstr ""

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1160
msgid "Notifications from <0>@{0}</0>"
msgstr ""

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1228
msgid "Notifications from @{0} will not be filtered from now on."
msgstr ""

#: src/pages/notifications.jsx:1233
msgid "Unable to accept notification request"
msgstr ""

#: src/pages/notifications.jsx:1238
msgid "Allow"
msgstr "Sta toe"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1258
msgid "Notifications from @{0} will not show up in Filtered notifications from now on."
msgstr ""

#: src/pages/notifications.jsx:1263
msgid "Unable to dismiss notification request"
msgstr ""

#: src/pages/notifications.jsx:1268
msgid "Dismiss"
msgstr "Negeer"

#: src/pages/notifications.jsx:1283
msgid "Dismissed"
msgstr "Genegeerd"

#: src/pages/public.jsx:28
msgid "Local timeline ({instance})"
msgstr "Lokale tijdlijn ({instance})"

#: src/pages/public.jsx:29
msgid "Federated timeline ({instance})"
msgstr "Federale tijdlijn ({instance})"

#: src/pages/public.jsx:92
msgid "Local timeline"
msgstr "Lokale tijdlijn"

#: src/pages/public.jsx:92
msgid "Federated timeline"
msgstr "Federale tijdlijn"

#: src/pages/public.jsx:98
msgid "No one has posted anything yet."
msgstr "Niemand heeft nog iets geplaatst."

#: src/pages/public.jsx:125
msgid "Switch to Federated"
msgstr "Overschakelen naar Federatie"

#: src/pages/public.jsx:132
msgid "Switch to Local"
msgstr "Overschakelen naar Lokaal"

#: src/pages/scheduled-posts.jsx:110
msgid "No scheduled posts."
msgstr ""

#. Scheduled [in 1 day] ([Thu, Feb 27, 6:30:00 PM])
#. placeholder {0}: niceDateTime(scheduledAt, { formatOpts: { weekday: 'short', second: 'numeric', }, })
#: src/pages/scheduled-posts.jsx:207
msgid "Scheduled <0><1/></0> <2>({0})</2>"
msgstr ""

#. Scheduled [in 1 day]
#: src/pages/scheduled-posts.jsx:263
msgid "Scheduled <0><1/></0>"
msgstr ""

#: src/pages/scheduled-posts.jsx:308
msgid "Scheduled post rescheduled"
msgstr ""

#: src/pages/scheduled-posts.jsx:315
msgid "Failed to reschedule post"
msgstr ""

#: src/pages/scheduled-posts.jsx:338
msgid "Reschedule"
msgstr ""

#: src/pages/scheduled-posts.jsx:344
msgid "Delete scheduled post?"
msgstr ""

#: src/pages/scheduled-posts.jsx:352
msgid "Scheduled post deleted"
msgstr ""

#: src/pages/scheduled-posts.jsx:359
msgid "Failed to delete scheduled post"
msgstr ""

#: src/pages/search.jsx:50
msgid "Search: {q} (Posts)"
msgstr "Zoek: {q} (Berichten)"

#: src/pages/search.jsx:53
msgid "Search: {q} (Accounts)"
msgstr "Zoek: {q} (Accounts)"

#: src/pages/search.jsx:56
msgid "Search: {q} (Hashtags)"
msgstr "Zoek: {q} (Hashtags)"

#: src/pages/search.jsx:59
msgid "Search: {q}"
msgstr "Zoeken: {q}"

#: src/pages/search.jsx:339
#: src/pages/search.jsx:421
msgid "Hashtags"
msgstr "Hashtags"

#: src/pages/search.jsx:371
#: src/pages/search.jsx:425
#: src/pages/search.jsx:495
msgid "See more"
msgstr "Bekijk meer"

#: src/pages/search.jsx:397
msgid "See more accounts"
msgstr "Bekijk meer accounts"

#: src/pages/search.jsx:411
msgid "No accounts found."
msgstr "Geen accounts gevonden."

#: src/pages/search.jsx:467
msgid "See more hashtags"
msgstr "Bekijk meer hashtags"

#: src/pages/search.jsx:481
msgid "No hashtags found."
msgstr "Geen hashtags gevonden."

#: src/pages/search.jsx:525
msgid "See more posts"
msgstr "Bekijk meer berichten"

#: src/pages/search.jsx:539
msgid "No posts found."
msgstr "Geen berichten gevonden."

#: src/pages/search.jsx:583
msgid "Enter your search term or paste a URL above to get started."
msgstr ""

#: src/pages/settings.jsx:95
msgid "Settings"
msgstr "Instellingen"

#: src/pages/settings.jsx:104
msgid "Appearance"
msgstr "Weergave"

#: src/pages/settings.jsx:180
msgid "Light"
msgstr "Licht"

#: src/pages/settings.jsx:191
msgid "Dark"
msgstr "Donker"

#: src/pages/settings.jsx:204
msgid "Auto"
msgstr "Automatisch"

#: src/pages/settings.jsx:214
msgid "Text size"
msgstr "Lettergrootte"

#. Preview of one character, in smallest size
#. Preview of one character, in largest size
#: src/pages/settings.jsx:219
#: src/pages/settings.jsx:244
msgid "A"
msgstr "A"

#: src/pages/settings.jsx:258
msgid "Display language"
msgstr "Weergavetaal"

#: src/pages/settings.jsx:267
msgid "Volunteer translations"
msgstr "Vertalingen van vrijwilligers"

#: src/pages/settings.jsx:278
msgid "Posting"
msgstr "Plaatsen"

#: src/pages/settings.jsx:285
msgid "Default visibility"
msgstr "Standaard zichtbaarheid"

#: src/pages/settings.jsx:286
#: src/pages/settings.jsx:332
msgid "Synced"
msgstr "Gesynchroniseerd"

#: src/pages/settings.jsx:311
msgid "Failed to update posting privacy"
msgstr ""

#: src/pages/settings.jsx:334
msgid "Synced to your instance server's settings. <0>Go to your instance ({instance}) for more settings.</0>"
msgstr "Gesynchroniseerd met de instellingen van je instance server. <0>Ga naar je instance ({instance}) voor meer instellingen.</0>"

#: src/pages/settings.jsx:349
msgid "Experiments"
msgstr "Experimenten"

#: src/pages/settings.jsx:362
msgid "Auto refresh timeline posts"
msgstr ""

#: src/pages/settings.jsx:374
msgid "Boosts carousel"
msgstr "Boosts carrousel"

#: src/pages/settings.jsx:391
msgid "Post translation"
msgstr "Berichtvertaling"

#: src/pages/settings.jsx:402
msgid "Translate to "
msgstr ""

#: src/pages/settings.jsx:413
msgid "System language ({systemTargetLanguageText})"
msgstr "Systeemtaal ({systemTargetLanguageText})"

#. placeholder {0}: snapStates.settings.contentTranslationHideLanguages .length
#: src/pages/settings.jsx:439
msgid "{0, plural, =0 {Hide \"Translate\" button for:} other {Hide \"Translate\" button for (#):}}"
msgstr ""

#: src/pages/settings.jsx:494
msgid "Note: This feature uses external translation services, powered by <0>{TRANSLATION_API_NAME}</0>."
msgstr ""

#: src/pages/settings.jsx:522
msgid "Auto inline translation"
msgstr ""

#: src/pages/settings.jsx:526
msgid "Automatically show translation for posts in timeline. Only works for <0>short</0> posts without content warning, media and poll."
msgstr ""

#: src/pages/settings.jsx:547
msgid "GIF Picker for composer"
msgstr ""

#: src/pages/settings.jsx:551
msgid "Note: This feature uses external GIF search service, powered by <0>GIPHY</0>. G-rated (suitable for viewing by all ages), tracking parameters are stripped, referrer information is omitted from requests, but search queries and IP address information will still reach their servers."
msgstr "Let op: Deze functie maakt gebruik van een externe GIF zoekdienst, mede mogelijk gemaakt door <0>GIPHY</0>. Geschikt voor alle leeftijden, tracking parameters worden geschrapt, verwijzerinformatie wordt weggelaten uit verzoeken, maar zoekopdrachten en het IP-adres zullen hun servers nog steeds bereiken."

#: src/pages/settings.jsx:580
msgid "Image description generator"
msgstr ""

#: src/pages/settings.jsx:585
msgid "Only for new images while composing new posts."
msgstr ""

#: src/pages/settings.jsx:592
msgid "Note: This feature uses external AI service, powered by <0>img-alt-api</0>. May not work well. Only for images and in English."
msgstr "Let op: Deze functie maakt gebruik van externe AI-service, gevoed door <0>img-alt-api</0>. Kan mogelijk niet goed werken. Alleen voor afbeeldingen in het Engels."

#: src/pages/settings.jsx:618
msgid "Server-side grouped notifications"
msgstr ""

#: src/pages/settings.jsx:622
msgid "Alpha-stage feature. Potentially improved grouping window but basic grouping logic."
msgstr ""

#: src/pages/settings.jsx:643
msgid "\"Cloud\" import/export for shortcuts settings"
msgstr "\"Cloud\" import/export voor snelkoppelingsinstellingen"

#: src/pages/settings.jsx:648
msgid "⚠️⚠️⚠️ Very experimental.<0/>Stored in your own profile’s notes. Profile (private) notes are mainly used for other profiles, and hidden for own profile."
msgstr "⚠️⚠️⚠️ Zeer experimenteel.<0/>Opgeslagen in je eigen profielnotities. (Privé) profielnotities worden voornamelijk gebruikt voor andere profielen en verborgen voor je eigen profiel."

#: src/pages/settings.jsx:659
msgid "Note: This feature uses currently-logged-in instance server API."
msgstr "Let op: Deze functie maakt gebruik van de API van de instance server waar je momenteel bent ingelogd."

#: src/pages/settings.jsx:676
msgid "Cloak mode <0>(<1>Text</1> → <2>████</2>)</0>"
msgstr ""

#: src/pages/settings.jsx:685
msgid "Replace text as blocks, useful when taking screenshots, for privacy reasons."
msgstr ""

#: src/pages/settings.jsx:710
msgid "About"
msgstr "Over"

#: src/pages/settings.jsx:749
msgid "<0>Built</0> by <1>@cheeaun</1>"
msgstr ""

#: src/pages/settings.jsx:778
msgid "Sponsor"
msgstr "Sponsor"

#: src/pages/settings.jsx:786
msgid "Donate"
msgstr "Doneer"

#: src/pages/settings.jsx:802
msgid "What's new"
msgstr ""

#: src/pages/settings.jsx:806
msgid "Privacy Policy"
msgstr "Privacybeleid"

#. placeholder {0}: WEBSITE.replace(/https?:\/\//g, '').replace(/\/$/, '')
#: src/pages/settings.jsx:813
msgid "<0>Site:</0> {0}"
msgstr "<0>Site:</0> {0}"

#. placeholder {0}: !__FAKE_COMMIT_HASH__ && ( <span class="ib insignificant"> ( <a href={`https://github.com/cheeaun/phanpy/commit/${__COMMIT_HASH__}`} target="_blank" rel="noopener" > <RelativeTime datetime={new Date(__BUILD_TIME__)} /> </a> ) </span> )
#: src/pages/settings.jsx:820
msgid "<0>Version:</0> <1/> {0}"
msgstr "<0>Versie:</0> <1/> {0}"

#: src/pages/settings.jsx:835
msgid "Version string copied"
msgstr "Versiestring gekopieerd"

#: src/pages/settings.jsx:838
msgid "Unable to copy version string"
msgstr ""

#: src/pages/settings.jsx:1103
#: src/pages/settings.jsx:1108
msgid "Failed to update subscription. Please try again."
msgstr ""

#: src/pages/settings.jsx:1114
msgid "Failed to remove subscription. Please try again."
msgstr ""

#: src/pages/settings.jsx:1121
msgid "Push Notifications (beta)"
msgstr "Pushmeldingen (bèta)"

#: src/pages/settings.jsx:1143
msgid "Push notifications are blocked. Please enable them in your browser settings."
msgstr ""

#. placeholder {0}: [ { value: 'all', label: t`anyone`, }, { value: 'followed', label: t`people I follow`, }, { value: 'follower', label: t`followers`, }, ].map((type) => ( <option value={type.value}>{type.label}</option> ))
#: src/pages/settings.jsx:1152
msgid "Allow from <0>{0}</0>"
msgstr "Sta toe van <0>{0}</0>"

#: src/pages/settings.jsx:1161
msgid "anyone"
msgstr "iedereen"

#: src/pages/settings.jsx:1165
msgid "people I follow"
msgstr "mensen die ik volg"

#: src/pages/settings.jsx:1169
msgid "followers"
msgstr "volgers"

#: src/pages/settings.jsx:1202
msgid "Follows"
msgstr "Volgend"

#: src/pages/settings.jsx:1210
msgid "Polls"
msgstr "Peilingen"

#: src/pages/settings.jsx:1214
msgid "Post edits"
msgstr "Plaats bewerkingen"

#: src/pages/settings.jsx:1235
msgid "Push permission was not granted since your last login. You'll need to <0><1>log in</1> again to grant push permission</0>."
msgstr ""

#: src/pages/settings.jsx:1251
msgid "NOTE: Push notifications only work for <0>one account</0>."
msgstr "LET OP: Push meldingen werken alleen voor <0>één account</0>."

#. js-lingui-explicit-id
#: src/pages/status.jsx:657
#: src/pages/status.jsx:1220
msgid "post.title"
msgstr ""

#: src/pages/status.jsx:911
msgid "You're not logged in. Interactions (reply, boost, etc) are not possible."
msgstr "Je bent niet ingelogd. Interacties (antwoorden, boosten, etc) zijn niet mogelijk."

#: src/pages/status.jsx:931
msgid "This post is from another instance (<0>{instance}</0>). Interactions (reply, boost, etc) are not possible."
msgstr "Dit bericht komt uit een andere instantie (<0>{instance}</0>). Interacties (antwoorden, boosten, etc.) zijn niet mogelijk."

#: src/pages/status.jsx:959
msgid "Error: {e}"
msgstr "Fout: {e}"

#: src/pages/status.jsx:966
msgid "Switch to my instance to enable interactions"
msgstr "Overschakelen naar mijn instantie om interacties in te schakelen"

#: src/pages/status.jsx:1068
msgid "Unable to load replies."
msgstr "Kan antwoorden niet laden."

#: src/pages/status.jsx:1180
msgid "Back"
msgstr "Terug"

#: src/pages/status.jsx:1211
msgid "Go to main post"
msgstr "Ga naar het hoofdbericht"

#. placeholder {0}: ancestors.length
#: src/pages/status.jsx:1234
msgid "{0} posts above ‒ Go to top"
msgstr "{0} berichten boven - Go naar boven"

#: src/pages/status.jsx:1281
#: src/pages/status.jsx:1344
msgid "Switch to Side Peek view"
msgstr ""

#: src/pages/status.jsx:1345
msgid "Switch to Full view"
msgstr ""

#: src/pages/status.jsx:1363
msgid "Show all sensitive content"
msgstr "Toon alle gevoelige inhoud"

#: src/pages/status.jsx:1368
msgid "Experimental"
msgstr "Experimenteel"

#: src/pages/status.jsx:1377
msgid "Unable to switch"
msgstr "Kan niet overschakelen"

#. placeholder {0}: punycode.toUnicode( postInstance, )
#: src/pages/status.jsx:1384
msgid "Switch to post's instance ({0})"
msgstr ""

#: src/pages/status.jsx:1387
msgid "Switch to post's instance"
msgstr "Overschakelen naar instantie van bericht"

#: src/pages/status.jsx:1445
msgid "Unable to load post"
msgstr "Kan bericht niet laden"

#. placeholder {0}: replies.length
#. placeholder {1}: shortenNumber(replies.length)
#: src/pages/status.jsx:1581
msgid "{0, plural, one {# reply} other {<0>{1}</0> replies}}"
msgstr "{0, plural, one {# antwoord} other {<0>{1}</0> antwoorden}}"

#. placeholder {0}: shortenNumber(totalComments)
#: src/pages/status.jsx:1599
msgid "{totalComments, plural, one {# comment} other {<0>{0}</0> comments}}"
msgstr "{totalComments, plural, one {# opmerking} other {<0>{0}</0> opmerkingen}}"

#: src/pages/status.jsx:1621
msgid "View post with its replies"
msgstr "Bekijk bericht met antwoorden"

#: src/pages/trending.jsx:77
msgid "Trending ({instance})"
msgstr "Populair ({instance})"

#: src/pages/trending.jsx:235
msgid "Trending News"
msgstr "Populair nieuws"

#. By [Author]
#. placeholder {0}: author ? ( <NameText account={author} showAvatar /> ) : authorUrl ? ( <a href={authorUrl} target="_blank" rel="noopener" > {authorName} </a> ) : ( authorName )
#: src/pages/trending.jsx:350
msgid "By {0}"
msgstr ""

#: src/pages/trending.jsx:411
msgid "Back to showing trending posts"
msgstr "Terug naar alle populaire berichten"

#. placeholder {0}: currentLink .replace(/^https?:\/\/(www\.)?/i, '') .replace(/\/$/, '')
#: src/pages/trending.jsx:416
msgid "Showing posts mentioning <0>{0}</0>"
msgstr "Alle berichten over <0>{0}</0>"

#: src/pages/trending.jsx:428
msgid "Trending posts"
msgstr "Populaire berichten"

#: src/pages/trending.jsx:451
msgid "No trending posts."
msgstr "Geen populaire berichten."

#: src/pages/welcome.jsx:54
msgid "A minimalistic opinionated Mastodon web client."
msgstr ""

#: src/pages/welcome.jsx:65
msgid "Log in with Mastodon"
msgstr "Aanmelden met Mastodon"

#: src/pages/welcome.jsx:71
msgid "Sign up"
msgstr "Inschrijven"

#: src/pages/welcome.jsx:78
msgid "Connect your existing Mastodon/Fediverse account.<0/>Your credentials are not stored on this server."
msgstr ""

#: src/pages/welcome.jsx:95
msgid "<0>Built</0> by <1>@cheeaun</1>. <2>Privacy Policy</2>."
msgstr ""

#: src/pages/welcome.jsx:126
msgid "Screenshot of Boosts Carousel"
msgstr "Screenshot van Boosts carrousel"

#: src/pages/welcome.jsx:130
msgid "Boosts Carousel"
msgstr "Boosts carrousel"

#: src/pages/welcome.jsx:133
msgid "Visually separate original posts and re-shared posts (boosted posts)."
msgstr "Visueel scheiden van originele berichten en opnieuw gedeelde berichten (gebooste berichten)."

#: src/pages/welcome.jsx:142
msgid "Screenshot of nested comments thread"
msgstr ""

#: src/pages/welcome.jsx:146
msgid "Nested comments thread"
msgstr ""

#: src/pages/welcome.jsx:149
msgid "Effortlessly follow conversations. Semi-collapsible replies."
msgstr "Moeiteloos gesprekken volgen. Semi-inklapbare antwoorden."

#: src/pages/welcome.jsx:157
msgid "Screenshot of grouped notifications"
msgstr ""

#: src/pages/welcome.jsx:161
msgid "Grouped notifications"
msgstr "Gegroepeerde meldingen"

#: src/pages/welcome.jsx:164
msgid "Similar notifications are grouped and collapsed to reduce clutter."
msgstr ""

#: src/pages/welcome.jsx:173
msgid "Screenshot of multi-column UI"
msgstr "Schermafbeelding van multi-kolommodus"

#: src/pages/welcome.jsx:177
msgid "Single or multi-column"
msgstr "Enkel- of multi-kolom"

#: src/pages/welcome.jsx:180
msgid "By default, single column for zen-mode seekers. Configurable multi-column for power users."
msgstr "Standaard enkelvoudige kolom voor zen-moduszoekers. Instelbare multi-kolommodus voor power users."

#: src/pages/welcome.jsx:189
msgid "Screenshot of multi-hashtag timeline with a form to add more hashtags"
msgstr ""

#: src/pages/welcome.jsx:193
msgid "Multi-hashtag timeline"
msgstr ""

#: src/pages/welcome.jsx:196
msgid "Up to 5 hashtags combined into a single timeline."
msgstr ""

#: src/utils/open-compose.js:24
msgid "Looks like your browser is blocking popups."
msgstr "Het lijkt erop dat je browser popups blokkeert."

#: src/utils/show-compose.js:16
msgid "A draft post is currently minimized. Post or discard it before creating a new one."
msgstr ""

#: src/utils/show-compose.js:21
msgid "A post is currently open. Post or discard it before creating a new one."
msgstr ""

