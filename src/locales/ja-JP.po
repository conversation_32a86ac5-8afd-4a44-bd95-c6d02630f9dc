msgid ""
msgstr ""
"POT-Creation-Date: 2025-05-03 21:08+0800\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: ja\n"
"Project-Id-Version: phanpy\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-07-15 09:08\n"
"Last-Translator: \n"
"Language-Team: Japanese\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Crowdin-Project: phanpy\n"
"X-Crowdin-Project-ID: 703337\n"
"X-Crowdin-Language: ja\n"
"X-Crowdin-File: /main/src/locales/en.po\n"
"X-Crowdin-File-ID: 18\n"

#: src/components/account-block.jsx:137
msgid "Locked"
msgstr "ロック中"

#. placeholder {0}: shortenNumber(statusesCount)
#: src/components/account-block.jsx:143
msgid "Posts: {0}"
msgstr "投稿: {0}"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-block.jsx:148
msgid "Last posted: {0}"
msgstr "最終投稿: {0}"

#: src/components/account-block.jsx:163
#: src/components/account-info.jsx:710
msgid "Automated"
msgstr ""

#: src/components/account-block.jsx:170
#: src/components/account-info.jsx:715
#: src/components/status.jsx:665
msgid "Group"
msgstr "グループ"

#: src/components/account-block.jsx:180
msgid "Mutual"
msgstr "相互フォロー"

#: src/components/account-block.jsx:184
#: src/components/account-info.jsx:1875
msgid "Requested"
msgstr "リクエスト済み"

#: src/components/account-block.jsx:188
#: src/components/account-info.jsx:1866
msgid "Following"
msgstr "フォロー中"

#: src/components/account-block.jsx:192
#: src/components/account-info.jsx:1180
msgid "Follows you"
msgstr "フォローされています"

#: src/components/account-block.jsx:200
msgid "{followersCount, plural, one {# follower} other {# followers}}"
msgstr ""

#: src/components/account-block.jsx:209
#: src/components/account-info.jsx:758
msgid "Verified"
msgstr "認証済み"

#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#: src/components/account-block.jsx:224
#: src/components/account-info.jsx:860
msgid "Joined <0>{0}</0>"
msgstr "<0>{0}</0> に参加しました"

#: src/components/account-info.jsx:63
msgid "Forever"
msgstr ""

#: src/components/account-info.jsx:403
msgid "Unable to load account."
msgstr "アカウントを読み込めません。"

#: src/components/account-info.jsx:418
msgid "Go to account page"
msgstr "アカウントページに移動"

#: src/components/account-info.jsx:447
#: src/components/account-info.jsx:780
#: src/components/account-info.jsx:810
msgid "Followers"
msgstr "フォロワー"

#. js-lingui-explicit-id
#: src/components/account-info.jsx:451
#: src/components/account-info.jsx:820
#: src/components/account-info.jsx:839
msgid "following.stats"
msgstr ""

#: src/components/account-info.jsx:454
#: src/components/account-info.jsx:856
#: src/pages/account-statuses.jsx:487
#: src/pages/search.jsx:344
#: src/pages/search.jsx:491
msgid "Posts"
msgstr "投稿"

#: src/components/account-info.jsx:462
#: src/components/account-info.jsx:1236
#: src/components/compose.jsx:2792
#: src/components/media-alt-modal.jsx:55
#: src/components/media-modal.jsx:363
#: src/components/status.jsx:1915
#: src/components/status.jsx:1932
#: src/components/status.jsx:2057
#: src/components/status.jsx:2686
#: src/components/status.jsx:2689
#: src/pages/account-statuses.jsx:531
#: src/pages/accounts.jsx:118
#: src/pages/hashtag.jsx:203
#: src/pages/list.jsx:171
#: src/pages/public.jsx:116
#: src/pages/scheduled-posts.jsx:89
#: src/pages/status.jsx:1305
#: src/pages/trending.jsx:474
msgid "More"
msgstr "その他"

#: src/components/account-info.jsx:474
msgid "<0>{displayName}</0> has indicated that their new account is now:"
msgstr ""

#: src/components/account-info.jsx:619
#: src/components/account-info.jsx:1464
msgid "Handle copied"
msgstr "ハンドルをコピーしました"

#: src/components/account-info.jsx:622
#: src/components/account-info.jsx:1467
msgid "Unable to copy handle"
msgstr "ハンドルをコピーできません"

#: src/components/account-info.jsx:628
#: src/components/account-info.jsx:1473
msgid "Copy handle"
msgstr "ハンドルをコピー"

#: src/components/account-info.jsx:634
msgid "Go to original profile page"
msgstr "元のプロフィールページに移動"

#: src/components/account-info.jsx:652
msgid "View profile image"
msgstr "プロフィール画像を表示"

#: src/components/account-info.jsx:670
msgid "View profile header"
msgstr "プロフィールヘッダーを表示"

#: src/components/account-info.jsx:686
#: src/components/account-info.jsx:1774
#: src/components/account-info.jsx:2300
msgid "Edit profile"
msgstr "プロフィールを編集"

#: src/components/account-info.jsx:705
msgid "In Memoriam"
msgstr ""

#: src/components/account-info.jsx:787
#: src/components/account-info.jsx:830
msgid "This user has chosen to not make this information available."
msgstr "このユーザーはこの情報を利用できないように選択しました。"

#. placeholder {0}: ( postingStats.originals / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {1}: ( postingStats.replies / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {2}: ( postingStats.boosts / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#: src/components/account-info.jsx:885
msgid "{0} original posts, {1} replies, {2} boosts"
msgstr ""

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.daysSinceLastPost
#. placeholder {2}: postingStats.daysSinceLastPost
#. placeholder {3}: postingStats.daysSinceLastPost
#. placeholder {4}: postingStats.total
#. placeholder {5}: postingStats.total
#. placeholder {6}: postingStats.daysSinceLastPost
#: src/components/account-info.jsx:901
msgid "{0, plural, one {{1, plural, one {Last 1 post in the past 1 day} other {Last 1 post in the past {2} days}}} other {{3, plural, one {Last {4} posts in the past 1 day} other {Last {5} posts in the past {6} days}}}}"
msgstr ""

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.total
#: src/components/account-info.jsx:917
msgid "{0, plural, one {Last 1 post in the past year(s)} other {Last {1} posts in the past year(s)}}"
msgstr "{0, plural, one {1 年以内に 1 回の投稿} other {1 年以内に {1} 回の投稿}}"

#: src/components/account-info.jsx:942
#: src/pages/catchup.jsx:70
msgid "Original"
msgstr "オリジナル"

#: src/components/account-info.jsx:946
#: src/components/status.jsx:2470
#: src/pages/catchup.jsx:71
#: src/pages/catchup.jsx:1448
#: src/pages/catchup.jsx:2061
#: src/pages/status.jsx:1024
#: src/pages/status.jsx:1651
msgid "Replies"
msgstr "返信"

#: src/components/account-info.jsx:950
#: src/pages/catchup.jsx:72
#: src/pages/catchup.jsx:1450
#: src/pages/catchup.jsx:2073
#: src/pages/settings.jsx:1198
msgid "Boosts"
msgstr "ブースト"

#: src/components/account-info.jsx:956
msgid "Post stats unavailable."
msgstr "投稿の統計情報を利用できません。"

#: src/components/account-info.jsx:987
msgid "View post stats"
msgstr "投稿の統計を見る"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-info.jsx:1184
msgid "Last post: <0>{0}</0>"
msgstr "最終投稿: <0>{0}</0>"

#: src/components/account-info.jsx:1198
msgid "Muted"
msgstr "ミュート済み"

#: src/components/account-info.jsx:1203
msgid "Blocked"
msgstr "ブロック済み"

#: src/components/account-info.jsx:1212
msgid "Private note"
msgstr ""

#: src/components/account-info.jsx:1269
msgid "Mention <0>@{username}</0>"
msgstr ""

#: src/components/account-info.jsx:1281
msgid "Translate bio"
msgstr "プロフィールを翻訳"

#: src/components/account-info.jsx:1292
msgid "Edit private note"
msgstr ""

#: src/components/account-info.jsx:1292
msgid "Add private note"
msgstr ""

#: src/components/account-info.jsx:1312
msgid "Notifications enabled for @{username}'s posts."
msgstr ""

#: src/components/account-info.jsx:1313
msgid " Notifications disabled for @{username}'s posts."
msgstr ""

#: src/components/account-info.jsx:1325
msgid "Disable notifications"
msgstr ""

#: src/components/account-info.jsx:1326
msgid "Enable notifications"
msgstr ""

#: src/components/account-info.jsx:1343
msgid "Boosts from @{username} enabled."
msgstr ""

#: src/components/account-info.jsx:1344
msgid "Boosts from @{username} disabled."
msgstr ""

#: src/components/account-info.jsx:1355
msgid "Disable boosts"
msgstr ""

#: src/components/account-info.jsx:1355
msgid "Enable boosts"
msgstr ""

#: src/components/account-info.jsx:1374
msgid "@{username} is no longer featured on your profile."
msgstr ""

#: src/components/account-info.jsx:1384
msgid "@{username} is now featured on your profile."
msgstr ""

#: src/components/account-info.jsx:1392
msgid "Unable to unfeature @{username} on your profile."
msgstr ""

#: src/components/account-info.jsx:1396
msgid "Unable to feature @{username} on your profile."
msgstr ""

#: src/components/account-info.jsx:1405
msgid "Don't feature on profile"
msgstr ""

#: src/components/account-info.jsx:1406
#: src/pages/hashtag.jsx:333
msgid "Feature on profile"
msgstr ""

#: src/components/account-info.jsx:1415
msgid "Show featured profiles"
msgstr ""

#: src/components/account-info.jsx:1430
#: src/components/account-info.jsx:1440
#: src/components/account-info.jsx:2046
msgid "Add/Remove from Lists"
msgstr "リストから追加/削除"

#: src/components/account-info.jsx:1490
#: src/components/status.jsx:1337
msgid "Link copied"
msgstr "リンクをコピーしました"

#: src/components/account-info.jsx:1493
#: src/components/status.jsx:1340
msgid "Unable to copy link"
msgstr "リンクをコピーできません"

#: src/components/account-info.jsx:1499
#: src/components/shortcuts-settings.jsx:1059
#: src/components/status.jsx:1346
#: src/components/status.jsx:3464
msgid "Copy"
msgstr "コピー"

#: src/components/account-info.jsx:1514
#: src/components/shortcuts-settings.jsx:1077
#: src/components/status.jsx:1362
msgid "Sharing doesn't seem to work."
msgstr "共有は機能しないようです。"

#: src/components/account-info.jsx:1520
#: src/components/status.jsx:1368
msgid "Share…"
msgstr "共有…"

#: src/components/account-info.jsx:1540
msgid "Unmuted @{username}"
msgstr ""

#: src/components/account-info.jsx:1552
msgid "Unmute <0>@{username}</0>"
msgstr "<0>@{username}</0> をミュート解除"

#: src/components/account-info.jsx:1568
msgid "Mute <0>@{username}</0>…"
msgstr "<0>@{username}</0> をミュート…"

#. placeholder {0}: typeof MUTE_DURATIONS_LABELS[duration] === 'function' ? MUTE_DURATIONS_LABELS[duration]() : _(MUTE_DURATIONS_LABELS[duration])
#: src/components/account-info.jsx:1600
msgid "Muted @{username} for {0}"
msgstr ""

#: src/components/account-info.jsx:1612
msgid "Unable to mute @{username}"
msgstr ""

#: src/components/account-info.jsx:1633
msgid "Remove <0>@{username}</0> from followers?"
msgstr "<0>@{username}</0> をフォロワーから削除しますか？"

#: src/components/account-info.jsx:1653
msgid "@{username} removed from followers"
msgstr ""

#: src/components/account-info.jsx:1665
msgid "Remove follower…"
msgstr "フォロワーを削除…"

#: src/components/account-info.jsx:1676
msgid "Block <0>@{username}</0>?"
msgstr "<0>@{username}</0> をブロックしますか？"

#: src/components/account-info.jsx:1700
msgid "Unblocked @{username}"
msgstr ""

#: src/components/account-info.jsx:1708
msgid "Blocked @{username}"
msgstr ""

#: src/components/account-info.jsx:1716
msgid "Unable to unblock @{username}"
msgstr ""

#: src/components/account-info.jsx:1718
msgid "Unable to block @{username}"
msgstr ""

#: src/components/account-info.jsx:1728
msgid "Unblock <0>@{username}</0>"
msgstr "<0>@{username}</0> のブロックを解除"

#: src/components/account-info.jsx:1737
msgid "Block <0>@{username}</0>…"
msgstr "<0>@{username}</0> をブロック…"

#: src/components/account-info.jsx:1754
msgid "Report <0>@{username}</0>…"
msgstr "<0>@{username}</0> を報告…"

#: src/components/account-info.jsx:1810
msgid "Withdraw follow request?"
msgstr ""

#. placeholder {0}: info.acct || info.username
#: src/components/account-info.jsx:1811
msgid "Unfollow @{0}?"
msgstr ""

#: src/components/account-info.jsx:1869
msgid "Unfollow…"
msgstr "フォロー解除…"

#: src/components/account-info.jsx:1878
msgid "Withdraw…"
msgstr "取り消し…"

#: src/components/account-info.jsx:1885
#: src/components/account-info.jsx:1889
#: src/pages/hashtag.jsx:265
msgid "Follow"
msgstr "フォロー"

#: src/components/account-info.jsx:1986
#: src/components/account-info.jsx:2041
#: src/components/account-info.jsx:2175
#: src/components/account-info.jsx:2295
#: src/components/account-sheet.jsx:38
#: src/components/compose.jsx:891
#: src/components/compose.jsx:2748
#: src/components/compose.jsx:3228
#: src/components/compose.jsx:3437
#: src/components/compose.jsx:3667
#: src/components/drafts.jsx:59
#: src/components/embed-modal.jsx:13
#: src/components/generic-accounts.jsx:151
#: src/components/keyboard-shortcuts-help.jsx:43
#: src/components/list-add-edit.jsx:37
#: src/components/media-alt-modal.jsx:43
#: src/components/media-modal.jsx:327
#: src/components/notification-service.jsx:157
#: src/components/report-modal.jsx:118
#: src/components/shortcuts-settings.jsx:230
#: src/components/shortcuts-settings.jsx:583
#: src/components/shortcuts-settings.jsx:783
#: src/components/status.jsx:3188
#: src/components/status.jsx:3428
#: src/components/status.jsx:3937
#: src/pages/accounts.jsx:45
#: src/pages/catchup.jsx:1584
#: src/pages/filters.jsx:225
#: src/pages/list.jsx:302
#: src/pages/notifications.jsx:942
#: src/pages/scheduled-posts.jsx:259
#: src/pages/settings.jsx:90
#: src/pages/status.jsx:1392
msgid "Close"
msgstr "閉じる"

#: src/components/account-info.jsx:1991
msgid "Translated Bio"
msgstr "翻訳されたプロフィール"

#: src/components/account-info.jsx:2086
msgid "Unable to remove from list."
msgstr ""

#: src/components/account-info.jsx:2087
msgid "Unable to add to list."
msgstr ""

#: src/components/account-info.jsx:2106
#: src/pages/lists.jsx:131
msgid "Unable to load lists."
msgstr ""

#: src/components/account-info.jsx:2110
msgid "No lists."
msgstr "リストはありません。"

#: src/components/account-info.jsx:2121
#: src/components/list-add-edit.jsx:41
#: src/pages/lists.jsx:62
msgid "New list"
msgstr "新しいリスト"

#. placeholder {0}: account?.username || account?.acct
#: src/components/account-info.jsx:2180
msgid "Private note about <0>@{0}</0>"
msgstr "<0>@{0}</0> に関するプライベートメモ"

#: src/components/account-info.jsx:2210
msgid "Unable to update private note."
msgstr ""

#: src/components/account-info.jsx:2233
#: src/components/account-info.jsx:2531
msgid "Cancel"
msgstr "キャンセル"

#: src/components/account-info.jsx:2238
msgid "Save & close"
msgstr "保存して終了"

#: src/components/account-info.jsx:2355
msgid "Unable to update profile."
msgstr ""

#: src/components/account-info.jsx:2362
msgid "Header picture"
msgstr ""

#: src/components/account-info.jsx:2414
msgid "Profile picture"
msgstr ""

#: src/components/account-info.jsx:2466
#: src/components/list-add-edit.jsx:106
msgid "Name"
msgstr "名前"

#: src/components/account-info.jsx:2479
msgid "Bio"
msgstr "プロフィール"

#: src/components/account-info.jsx:2492
msgid "Extra fields"
msgstr "補足情報"

#: src/components/account-info.jsx:2498
msgid "Label"
msgstr "ラベル"

#: src/components/account-info.jsx:2501
msgid "Content"
msgstr "内容"

#: src/components/account-info.jsx:2534
#: src/components/list-add-edit.jsx:152
#: src/components/shortcuts-settings.jsx:715
#: src/pages/filters.jsx:570
#: src/pages/notifications.jsx:1008
msgid "Save"
msgstr "保存"

#: src/components/account-info.jsx:2588
msgid "username"
msgstr "ユーザー名"

#: src/components/account-info.jsx:2592
msgid "server domain name"
msgstr "サーバーのドメイン名"

#. placeholder {0}: info.username
#: src/components/account-info.jsx:2658
msgid "Profiles featured by @{0}"
msgstr ""

#: src/components/account-info.jsx:2684
msgid "No featured profiles."
msgstr ""

#: src/components/background-service.jsx:158
msgid "Cloak mode disabled"
msgstr "クロークモードは無効化されています"

#: src/components/background-service.jsx:158
msgid "Cloak mode enabled"
msgstr "クロークモードは有効化されています"

#: src/components/columns.jsx:27
#: src/components/nav-menu.jsx:181
#: src/components/shortcuts-settings.jsx:139
#: src/components/timeline.jsx:469
#: src/pages/catchup.jsx:883
#: src/pages/filters.jsx:90
#: src/pages/followed-hashtags.jsx:41
#: src/pages/home.jsx:54
#: src/pages/notifications.jsx:587
#: src/pages/scheduled-posts.jsx:74
msgid "Home"
msgstr "ホーム"

#: src/components/compose-button.jsx:150
#: src/compose.jsx:38
msgid "Compose"
msgstr "投稿"

#: src/components/compose-button.jsx:177
#: src/components/nav-menu.jsx:265
#: src/pages/scheduled-posts.jsx:31
#: src/pages/scheduled-posts.jsx:78
msgid "Scheduled Posts"
msgstr ""

#: src/components/compose-button.jsx:190
msgid "Add to thread"
msgstr ""

#: src/components/compose.jsx:211
msgid "Take photo or video"
msgstr ""

#: src/components/compose.jsx:212
msgid "Add media"
msgstr ""

#: src/components/compose.jsx:213
msgid "Add custom emoji"
msgstr "カスタム絵文字を追加"

#: src/components/compose.jsx:214
msgid "Add GIF"
msgstr ""

#: src/components/compose.jsx:215
msgid "Add poll"
msgstr "投票を追加"

#: src/components/compose.jsx:216
msgid "Schedule post"
msgstr ""

#: src/components/compose.jsx:416
msgid "You have unsaved changes. Discard this post?"
msgstr ""

#. placeholder {0}: unsupportedFiles.length
#. placeholder {1}: unsupportedFiles[0].name
#. placeholder {2}: lf.format( unsupportedFiles.map((f) => f.name), )
#: src/components/compose.jsx:654
msgid "{0, plural, one {File {1} is not supported.} other {Files {2} are not supported.}}"
msgstr ""

#: src/components/compose.jsx:664
#: src/components/compose.jsx:682
#: src/components/compose.jsx:1794
#: src/components/compose.jsx:1919
msgid "{maxMediaAttachments, plural, one {You can only attach up to 1 file.} other {You can only attach up to # files.}}"
msgstr ""

#: src/components/compose.jsx:872
msgid "Pop out"
msgstr "ポップアウト"

#: src/components/compose.jsx:879
msgid "Minimize"
msgstr "最小化"

#: src/components/compose.jsx:915
msgid "Looks like you closed the parent window."
msgstr ""

#: src/components/compose.jsx:922
msgid "Looks like you already have a compose field open in the parent window and currently publishing. Please wait for it to be done and try again later."
msgstr ""

#: src/components/compose.jsx:927
msgid "Looks like you already have a compose field open in the parent window. Popping in this window will discard the changes you made in the parent window. Continue?"
msgstr ""

#: src/components/compose.jsx:970
msgid "Pop in"
msgstr "ポップイン"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#. placeholder {1}: rtf.format(-replyToStatusMonthsAgo, 'month')
#: src/components/compose.jsx:980
msgid "Replying to @{0}’s post (<0>{1}</0>)"
msgstr ""

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#: src/components/compose.jsx:990
msgid "Replying to @{0}’s post"
msgstr ""

#: src/components/compose.jsx:1003
msgid "Editing source post"
msgstr ""

#: src/components/compose.jsx:1056
msgid "Poll must have at least 2 options"
msgstr ""

#: src/components/compose.jsx:1060
msgid "Some poll choices are empty"
msgstr ""

#: src/components/compose.jsx:1073
msgid "Some media have no descriptions. Continue?"
msgstr ""

#: src/components/compose.jsx:1125
msgid "Attachment #{i} failed"
msgstr ""

#: src/components/compose.jsx:1221
#: src/components/status.jsx:2245
#: src/components/timeline.jsx:1023
msgid "Content warning"
msgstr "コンテンツに関する警告"

#: src/components/compose.jsx:1237
msgid "Content warning or sensitive media"
msgstr ""

#: src/components/compose.jsx:1273
#: src/components/status.jsx:99
#: src/pages/settings.jsx:318
msgid "Public"
msgstr "公開"

#: src/components/compose.jsx:1278
#: src/components/nav-menu.jsx:349
#: src/components/shortcuts-settings.jsx:165
#: src/components/status.jsx:100
msgid "Local"
msgstr "ローカル"

#: src/components/compose.jsx:1282
#: src/components/status.jsx:101
#: src/pages/settings.jsx:321
msgid "Unlisted"
msgstr "未収載"

#: src/components/compose.jsx:1285
#: src/components/status.jsx:102
#: src/pages/settings.jsx:324
msgid "Followers only"
msgstr "フォロワーのみ"

#: src/components/compose.jsx:1288
#: src/components/status.jsx:103
#: src/components/status.jsx:2121
msgid "Private mention"
msgstr "非公開の返信"

#: src/components/compose.jsx:1297
msgid "Post your reply"
msgstr ""

#: src/components/compose.jsx:1299
msgid "Edit your post"
msgstr ""

#: src/components/compose.jsx:1300
msgid "What are you doing?"
msgstr ""

#: src/components/compose.jsx:1379
msgid "Mark media as sensitive"
msgstr ""

#: src/components/compose.jsx:1416
msgid "Posting on <0/>"
msgstr ""

#: src/components/compose.jsx:1447
#: src/components/compose.jsx:3286
#: src/components/shortcuts-settings.jsx:715
#: src/pages/list.jsx:388
msgid "Add"
msgstr "追加"

#: src/components/compose.jsx:1675
msgid "Schedule"
msgstr ""

#: src/components/compose.jsx:1677
#: src/components/keyboard-shortcuts-help.jsx:155
#: src/components/status.jsx:1109
#: src/components/status.jsx:1895
#: src/components/status.jsx:1896
#: src/components/status.jsx:2590
msgid "Reply"
msgstr "返信"

#: src/components/compose.jsx:1679
msgid "Update"
msgstr "更新"

#: src/components/compose.jsx:1680
msgctxt "Submit button in composer"
msgid "Post"
msgstr "投稿"

#: src/components/compose.jsx:1806
msgid "Downloading GIF…"
msgstr "GIF をダウンロード中…"

#: src/components/compose.jsx:1834
msgid "Failed to download GIF"
msgstr "GIF のダウンロードに失敗しました"

#: src/components/compose.jsx:2049
#: src/components/compose.jsx:2126
#: src/components/nav-menu.jsx:244
msgid "More…"
msgstr "その他…"

#: src/components/compose.jsx:2562
msgid "Uploaded"
msgstr ""

#: src/components/compose.jsx:2575
msgid "Image description"
msgstr ""

#: src/components/compose.jsx:2576
msgid "Video description"
msgstr ""

#: src/components/compose.jsx:2577
msgid "Audio description"
msgstr ""

#. placeholder {0}: prettyBytes( imageSize, )
#. placeholder {0}: prettyBytes( videoSize, )
#. placeholder {1}: prettyBytes(imageSizeLimit)
#. placeholder {1}: prettyBytes(videoSizeLimit)
#: src/components/compose.jsx:2612
#: src/components/compose.jsx:2632
msgid "File size too large. Uploading might encounter issues. Try reduce the file size from {0} to {1} or lower."
msgstr ""

#. placeholder {0}: i18n.number( width, )
#. placeholder {1}: i18n.number(height)
#. placeholder {2}: i18n.number(newWidth)
#. placeholder {3}: i18n.number( newHeight, )
#: src/components/compose.jsx:2624
#: src/components/compose.jsx:2644
msgid "Dimension too large. Uploading might encounter issues. Try reduce dimension from {0}×{1}px to {2}×{3}px."
msgstr ""

#: src/components/compose.jsx:2652
msgid "Frame rate too high. Uploading might encounter issues."
msgstr "フレームレートが高すぎます。アップロード時に問題が発生する可能性があります。"

#: src/components/compose.jsx:2712
#: src/components/compose.jsx:2962
#: src/components/shortcuts-settings.jsx:726
#: src/pages/catchup.jsx:1081
#: src/pages/filters.jsx:413
msgid "Remove"
msgstr "削除"

#: src/components/compose.jsx:2729
#: src/compose.jsx:84
msgid "Error"
msgstr ""

#: src/components/compose.jsx:2754
msgid "Edit image description"
msgstr "画像の説明を編集"

#: src/components/compose.jsx:2755
msgid "Edit video description"
msgstr "動画の説明を編集"

#: src/components/compose.jsx:2756
msgid "Edit audio description"
msgstr "音声の説明を編集"

#: src/components/compose.jsx:2801
#: src/components/compose.jsx:2850
msgid "Generating description. Please wait…"
msgstr "説明を生成しています。しばらくお待ちください…"

#. placeholder {0}: e.message
#: src/components/compose.jsx:2821
msgid "Failed to generate description: {0}"
msgstr ""

#: src/components/compose.jsx:2822
msgid "Failed to generate description"
msgstr "説明の生成に失敗しました"

#: src/components/compose.jsx:2834
#: src/components/compose.jsx:2840
#: src/components/compose.jsx:2886
msgid "Generate description…"
msgstr "説明の生成…"

#. placeholder {0}: e?.message ? `: ${e.message}` : ''
#: src/components/compose.jsx:2873
msgid "Failed to generate description{0}"
msgstr ""

#. placeholder {0}: localeCode2Text(lang)
#: src/components/compose.jsx:2888
msgid "({0}) <0>— experimental</0>"
msgstr "({0}) <0>実験的</0>"

#: src/components/compose.jsx:2907
msgid "Done"
msgstr "完了"

#. placeholder {0}: i + 1
#: src/components/compose.jsx:2943
msgid "Choice {0}"
msgstr ""

#: src/components/compose.jsx:2990
msgid "Multiple choices"
msgstr ""

#: src/components/compose.jsx:2993
msgid "Duration"
msgstr ""

#: src/components/compose.jsx:3024
msgid "Remove poll"
msgstr ""

#: src/components/compose.jsx:3245
msgid "Search accounts"
msgstr ""

#: src/components/compose.jsx:3299
#: src/components/generic-accounts.jsx:236
msgid "Error loading accounts"
msgstr ""

#: src/components/compose.jsx:3443
msgid "Custom emojis"
msgstr ""

#: src/components/compose.jsx:3463
msgid "Search emoji"
msgstr ""

#: src/components/compose.jsx:3494
msgid "Error loading custom emojis"
msgstr ""

#: src/components/compose.jsx:3505
msgid "Recently used"
msgstr ""

#: src/components/compose.jsx:3506
msgid "Others"
msgstr ""

#. placeholder {0}: i18n.number(emojis.length - max)
#: src/components/compose.jsx:3544
msgid "{0} more…"
msgstr ""

#: src/components/compose.jsx:3682
msgid "Search GIFs"
msgstr ""

#: src/components/compose.jsx:3697
msgid "Powered by GIPHY"
msgstr ""

#: src/components/compose.jsx:3705
msgid "Type to search GIFs"
msgstr ""

#: src/components/compose.jsx:3803
#: src/components/media-modal.jsx:469
#: src/components/timeline.jsx:927
msgid "Previous"
msgstr "前へ"

#: src/components/compose.jsx:3821
#: src/components/media-modal.jsx:488
#: src/components/timeline.jsx:944
msgid "Next"
msgstr "次へ"

#: src/components/compose.jsx:3838
msgid "Error loading GIFs"
msgstr "GIF の読み込みに失敗しました"

#: src/components/drafts.jsx:64
#: src/pages/settings.jsx:702
msgid "Unsent drafts"
msgstr "未送信の下書き"

#: src/components/drafts.jsx:69
msgid "Looks like you have unsent drafts. Let's continue where you left off."
msgstr ""

#: src/components/drafts.jsx:103
msgid "Delete this draft?"
msgstr ""

#: src/components/drafts.jsx:118
msgid "Error deleting draft! Please try again."
msgstr "下書きの削除中にエラーが発生しました！もう一度やり直してください。"

#: src/components/drafts.jsx:128
#: src/components/list-add-edit.jsx:188
#: src/components/status.jsx:1512
#: src/pages/filters.jsx:603
#: src/pages/scheduled-posts.jsx:369
msgid "Delete…"
msgstr "削除…"

#: src/components/drafts.jsx:147
msgid "Error fetching reply-to status!"
msgstr "返信の取得中にエラーが発生しました！"

#: src/components/drafts.jsx:172
msgid "Delete all drafts?"
msgstr "すべての下書きを削除しますか？"

#: src/components/drafts.jsx:190
msgid "Error deleting drafts! Please try again."
msgstr "下書きの削除中にエラーが発生しました。もう一度やり直してください。"

#: src/components/drafts.jsx:202
msgid "Delete all…"
msgstr "すべて削除…"

#: src/components/drafts.jsx:210
msgid "No drafts found."
msgstr "下書きはありません。"

#: src/components/drafts.jsx:247
#: src/pages/catchup.jsx:1932
msgid "Poll"
msgstr "アンケート"

#: src/components/drafts.jsx:250
#: src/pages/account-statuses.jsx:367
msgid "Media"
msgstr "メディア"

#: src/components/embed-modal.jsx:18
msgid "Open in new window"
msgstr "新しいウィンドウで開く"

#: src/components/follow-request-buttons.jsx:43
#: src/pages/notifications.jsx:992
msgid "Accept"
msgstr "承認する"

#: src/components/follow-request-buttons.jsx:69
msgid "Reject"
msgstr "拒否する"

#: src/components/follow-request-buttons.jsx:76
#: src/pages/notifications.jsx:1276
msgid "Accepted"
msgstr "承認済み"

#: src/components/follow-request-buttons.jsx:80
msgid "Rejected"
msgstr "拒否されました"

#: src/components/generic-accounts.jsx:154
#: src/components/notification.jsx:455
#: src/pages/accounts.jsx:50
#: src/pages/search.jsx:334
#: src/pages/search.jsx:367
msgid "Accounts"
msgstr "アカウント"

#: src/components/generic-accounts.jsx:214
#: src/components/timeline.jsx:551
#: src/pages/list.jsx:321
#: src/pages/notifications.jsx:922
#: src/pages/search.jsx:561
#: src/pages/status.jsx:1425
msgid "Show more…"
msgstr "もっと見る…"

#: src/components/generic-accounts.jsx:219
#: src/components/timeline.jsx:556
#: src/pages/search.jsx:566
msgid "The end."
msgstr "終わりです。"

#: src/components/generic-accounts.jsx:240
msgid "Nothing to show"
msgstr "表示するものがありません"

#: src/components/keyboard-shortcuts-help.jsx:47
#: src/components/nav-menu.jsx:368
#: src/pages/catchup.jsx:1622
msgid "Keyboard shortcuts"
msgstr "キーボードショートカット"

#: src/components/keyboard-shortcuts-help.jsx:55
msgid "Keyboard shortcuts help"
msgstr "キーボードショートカットのヘルプ"

#: src/components/keyboard-shortcuts-help.jsx:59
#: src/pages/catchup.jsx:1647
msgid "Next post"
msgstr "次の投稿"

#: src/components/keyboard-shortcuts-help.jsx:63
#: src/pages/catchup.jsx:1655
msgid "Previous post"
msgstr "前のポスト"

#: src/components/keyboard-shortcuts-help.jsx:67
msgid "Skip carousel to next post"
msgstr "カルーセルをスキップして次のポスト"

#: src/components/keyboard-shortcuts-help.jsx:69
msgid "<0>Shift</0> + <1>j</1>"
msgstr "<0>Shift</0> + <1>j</1>"

#: src/components/keyboard-shortcuts-help.jsx:75
msgid "Skip carousel to previous post"
msgstr "カルーセルをスキップして前のポスト"

#: src/components/keyboard-shortcuts-help.jsx:77
msgid "<0>Shift</0> + <1>k</1>"
msgstr "<0>Shift</0> + <1>k</1>"

#: src/components/keyboard-shortcuts-help.jsx:83
msgid "Load new posts"
msgstr "新しいポストを読み込む"

#: src/components/keyboard-shortcuts-help.jsx:87
#: src/pages/catchup.jsx:1679
msgid "Open post details"
msgstr "ポストの詳細を開く"

#: src/components/keyboard-shortcuts-help.jsx:89
msgid "<0>Enter</0> or <1>o</1>"
msgstr "<0>Enter</0> または <1>o</1>"

#: src/components/keyboard-shortcuts-help.jsx:96
msgid "Expand content warning or<0/>toggle expanded/collapsed thread"
msgstr "CWを表示/隠す または スレッドを開く/閉じる"

#: src/components/keyboard-shortcuts-help.jsx:105
msgid "Close post or dialogs"
msgstr "ポストまたはダイアログを閉じる"

#: src/components/keyboard-shortcuts-help.jsx:107
msgid "<0>Esc</0> or <1>Backspace</1>"
msgstr "<0>Esc</0>または<1>Backspace</1>"

#: src/components/keyboard-shortcuts-help.jsx:113
msgid "Focus column in multi-column mode"
msgstr "複数列モードで列にフォーカス"

#: src/components/keyboard-shortcuts-help.jsx:115
msgid "<0>1</0> to <1>9</1>"
msgstr "<0>1</0> から <1>9</1>"

#: src/components/keyboard-shortcuts-help.jsx:121
msgid "Focus next column in multi-column mode"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:125
msgid "Focus previous column in multi-column mode"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:129
msgid "Compose new post"
msgstr "新しいポスト"

#: src/components/keyboard-shortcuts-help.jsx:133
msgid "Compose new post (new window)"
msgstr "新しいポスト(新しいウィンドウ)"

#: src/components/keyboard-shortcuts-help.jsx:136
msgid "<0>Shift</0> + <1>c</1>"
msgstr "<0>Shift</0> + <1>c</1>"

#: src/components/keyboard-shortcuts-help.jsx:142
msgid "Send post"
msgstr "ポストの送信"

#: src/components/keyboard-shortcuts-help.jsx:144
msgid "<0>Ctrl</0> + <1>Enter</1> or <2>⌘</2> + <3>Enter</3>"
msgstr "<0>Ctrl</0> + <1>Enter</1> または <2>⌘</2> + <3>Enter</3>"

#: src/components/keyboard-shortcuts-help.jsx:151
#: src/components/nav-menu.jsx:337
#: src/components/search-form.jsx:73
#: src/components/shortcuts-settings.jsx:52
#: src/components/shortcuts-settings.jsx:179
#: src/pages/search.jsx:46
#: src/pages/search.jsx:316
msgid "Search"
msgstr "検索"

#: src/components/keyboard-shortcuts-help.jsx:159
msgid "Reply (new window)"
msgstr "返信 (新しいウィンドウ)"

#: src/components/keyboard-shortcuts-help.jsx:162
msgid "<0>Shift</0> + <1>r</1>"
msgstr "<0>Shift</0> + <1>r</1>"

#: src/components/keyboard-shortcuts-help.jsx:168
msgid "Like (favourite)"
msgstr "いいね (お気に入り)"

#: src/components/keyboard-shortcuts-help.jsx:170
msgid "<0>l</0> or <1>f</1>"
msgstr "<0>l</0> または <1>f</1>"

#: src/components/keyboard-shortcuts-help.jsx:176
#: src/components/status.jsx:1117
#: src/components/status.jsx:2617
#: src/components/status.jsx:2640
#: src/components/status.jsx:2641
msgid "Boost"
msgstr "ブースト"

#: src/components/keyboard-shortcuts-help.jsx:178
msgid "<0>Shift</0> + <1>b</1>"
msgstr "<0>Shift</0> + <1>b</1>"

#: src/components/keyboard-shortcuts-help.jsx:184
#: src/components/status.jsx:1180
#: src/components/status.jsx:2665
#: src/components/status.jsx:2666
msgid "Bookmark"
msgstr "ブックマーク"

#: src/components/keyboard-shortcuts-help.jsx:188
msgid "Toggle Cloak mode"
msgstr "クロークモードの切り替え"

#: src/components/keyboard-shortcuts-help.jsx:190
msgid "<0>Shift</0> + <1>Alt</1> + <2>k</2>"
msgstr "<0>Shift</0> + <1>Alt</1> + <2>k</2>"

#: src/components/list-add-edit.jsx:41
msgid "Edit list"
msgstr "リストの編集"

#: src/components/list-add-edit.jsx:97
msgid "Unable to edit list."
msgstr "リストを編集できません。"

#: src/components/list-add-edit.jsx:98
msgid "Unable to create list."
msgstr "リストを作成できません。"

#: src/components/list-add-edit.jsx:126
msgid "Show replies to list members"
msgstr "リストメンバーへの返信を表示"

#: src/components/list-add-edit.jsx:129
msgid "Show replies to people I follow"
msgstr "フォロー中の人への返信を表示"

#: src/components/list-add-edit.jsx:132
msgid "Don't show replies"
msgstr "返信を表示しない"

#: src/components/list-add-edit.jsx:146
msgid "Hide posts on this list from Home/Following"
msgstr "このリストの投稿をホーム/フォローから隠す"

#: src/components/list-add-edit.jsx:152
#: src/pages/filters.jsx:570
msgid "Create"
msgstr "作成"

#: src/components/list-add-edit.jsx:159
msgid "Delete this list?"
msgstr "このリストを削除しますか？"

#: src/components/list-add-edit.jsx:178
msgid "Unable to delete list."
msgstr "リストを削除できません。"

#: src/components/list-exclusive-badge.jsx:14
#: src/pages/list.jsx:179
#: src/pages/lists.jsx:107
msgid "Posts on this list are hidden from Home/Following"
msgstr ""

#: src/components/media-alt-modal.jsx:48
#: src/components/media.jsx:51
msgid "Media description"
msgstr "メディアの説明"

#: src/components/media-alt-modal.jsx:67
#: src/components/status.jsx:1223
#: src/components/status.jsx:1232
#: src/components/translation-block.jsx:239
msgid "Translate"
msgstr "翻訳"

#: src/components/media-alt-modal.jsx:78
#: src/components/status.jsx:1251
msgid "Speak"
msgstr ""

#: src/components/media-modal.jsx:374
msgid "Open original media in new window"
msgstr ""

#: src/components/media-modal.jsx:378
msgid "Open original media"
msgstr ""

#: src/components/media-modal.jsx:394
msgid "Attempting to describe image. Please wait…"
msgstr ""

#: src/components/media-modal.jsx:409
msgid "Failed to describe image"
msgstr ""

#: src/components/media-modal.jsx:419
msgid "Describe image…"
msgstr ""

#: src/components/media-modal.jsx:443
msgid "View post"
msgstr ""

#: src/components/media-post.jsx:127
msgid "Sensitive media"
msgstr ""

#: src/components/media-post.jsx:132
msgid "Filtered: {filterTitleStr}"
msgstr ""

#: src/components/media-post.jsx:133
#: src/components/status.jsx:3767
#: src/components/status.jsx:3863
#: src/components/status.jsx:3941
#: src/components/timeline.jsx:1012
#: src/pages/catchup.jsx:75
#: src/pages/catchup.jsx:1880
msgid "Filtered"
msgstr "フィルター済み"

#: src/components/media.jsx:477
msgid "Open file"
msgstr ""

#: src/components/modals.jsx:75
msgid "Post scheduled"
msgstr ""

#: src/components/modals.jsx:76
msgid "Post published. Check it out."
msgstr ""

#: src/components/modals.jsx:78
msgid "Reply scheduled"
msgstr ""

#: src/components/modals.jsx:79
msgid "Reply posted. Check it out."
msgstr ""

#: src/components/modals.jsx:80
msgid "Post updated. Check it out."
msgstr ""

#: src/components/nav-menu.jsx:123
msgid "Menu"
msgstr "メニュー"

#: src/components/nav-menu.jsx:159
msgid "Reload page now to update?"
msgstr "今すぐページを再読み込みして更新しますか？"

#: src/components/nav-menu.jsx:171
msgid "New update available…"
msgstr "新しいアップデートが利用可能です…"

#. js-lingui-explicit-id
#: src/components/nav-menu.jsx:190
#: src/components/shortcuts-settings.jsx:140
#: src/pages/following.jsx:23
#: src/pages/following.jsx:145
msgid "following.title"
msgstr ""

#: src/components/nav-menu.jsx:197
#: src/pages/catchup.jsx:878
msgid "Catch-up"
msgstr "キャッチアップ"

#: src/components/nav-menu.jsx:204
#: src/components/shortcuts-settings.jsx:58
#: src/components/shortcuts-settings.jsx:146
#: src/pages/home.jsx:240
#: src/pages/mentions.jsx:21
#: src/pages/mentions.jsx:174
#: src/pages/settings.jsx:1190
#: src/pages/trending.jsx:384
msgid "Mentions"
msgstr "メンション"

#: src/components/nav-menu.jsx:211
#: src/components/shortcuts-settings.jsx:49
#: src/components/shortcuts-settings.jsx:152
#: src/pages/filters.jsx:24
#: src/pages/home.jsx:86
#: src/pages/home.jsx:198
#: src/pages/notifications.jsx:117
#: src/pages/notifications.jsx:591
msgid "Notifications"
msgstr "通知"

#: src/components/nav-menu.jsx:214
msgid "New"
msgstr "New"

#: src/components/nav-menu.jsx:225
msgid "Profile"
msgstr "プロフィール"

#: src/components/nav-menu.jsx:233
#: src/components/shortcuts-settings.jsx:54
#: src/components/shortcuts-settings.jsx:195
#: src/pages/bookmarks.jsx:12
#: src/pages/bookmarks.jsx:26
msgid "Bookmarks"
msgstr "ブックマーク"

#: src/components/nav-menu.jsx:253
#: src/components/shortcuts-settings.jsx:55
#: src/components/shortcuts-settings.jsx:201
#: src/pages/catchup.jsx:1449
#: src/pages/catchup.jsx:2067
#: src/pages/favourites.jsx:12
#: src/pages/favourites.jsx:26
#: src/pages/settings.jsx:1194
msgid "Likes"
msgstr "いいね！"

#: src/components/nav-menu.jsx:259
#: src/pages/followed-hashtags.jsx:15
#: src/pages/followed-hashtags.jsx:45
msgid "Followed Hashtags"
msgstr "フォロー中のハッシュタグ"

#: src/components/nav-menu.jsx:273
#: src/pages/account-statuses.jsx:333
#: src/pages/filters.jsx:55
#: src/pages/filters.jsx:94
#: src/pages/hashtag.jsx:344
msgid "Filters"
msgstr "フィルター"

#: src/components/nav-menu.jsx:281
msgid "Muted users"
msgstr "ミュートしたユーザー"

#: src/components/nav-menu.jsx:289
msgid "Muted users…"
msgstr "ミュートしたユーザー…"

#: src/components/nav-menu.jsx:296
msgid "Blocked users"
msgstr "ブロックしたユーザー"

#: src/components/nav-menu.jsx:304
msgid "Blocked users…"
msgstr "ブロックしたユーザー…"

#: src/components/nav-menu.jsx:316
msgid "Accounts…"
msgstr "アカウント…"

#: src/components/nav-menu.jsx:326
#: src/pages/login.jsx:32
#: src/pages/login.jsx:199
#: src/pages/status.jsx:924
#: src/pages/welcome.jsx:65
msgid "Log in"
msgstr "ログイン"

#: src/components/nav-menu.jsx:343
#: src/components/shortcuts-settings.jsx:57
#: src/components/shortcuts-settings.jsx:172
#: src/pages/trending.jsx:444
msgid "Trending"
msgstr "トレンド"

#: src/components/nav-menu.jsx:355
#: src/components/shortcuts-settings.jsx:165
msgid "Federated"
msgstr "連合"

#: src/components/nav-menu.jsx:378
msgid "Shortcuts / Columns…"
msgstr "ショートカット/列…"

#: src/components/nav-menu.jsx:388
#: src/components/nav-menu.jsx:402
msgid "Settings…"
msgstr "設定…"

#: src/components/nav-menu.jsx:432
#: src/components/nav-menu.jsx:467
#: src/components/shortcuts-settings.jsx:50
#: src/components/shortcuts-settings.jsx:158
#: src/pages/list.jsx:127
#: src/pages/lists.jsx:18
#: src/pages/lists.jsx:54
msgid "Lists"
msgstr "リスト"

#: src/components/nav-menu.jsx:440
#: src/components/shortcuts.jsx:241
#: src/pages/list.jsx:139
msgid "All Lists"
msgstr "すべてのリスト"

#: src/components/notification-service.jsx:161
msgid "Notification"
msgstr "通知"

#: src/components/notification-service.jsx:167
msgid "This notification is from your other account."
msgstr "この通知はあなたの他のアカウントからです。"

#: src/components/notification-service.jsx:196
msgid "View all notifications"
msgstr "すべての通知を表示"

#: src/components/notification.jsx:71
msgid "{account} reacted to your post with {emojiObject}"
msgstr "{account} があなたの投稿に {emojiObject} でリアクションしました"

#: src/components/notification.jsx:78
msgid "{account} published a post."
msgstr "{account} が投稿を公開しました。"

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:86
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted your reply.} other {{account} boosted your post.}}} other {{account} boosted {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted your reply.} other {<2><3>{1}</3> people</2> boosted your post.}}}}"
msgstr ""

#. placeholder {0}: shortenNumber(count)
#: src/components/notification.jsx:129
msgid "{count, plural, =1 {{account} followed you.} other {<0><1>{0}</1> people</0> followed you.}}"
msgstr ""

#: src/components/notification.jsx:143
msgid "{account} requested to follow you."
msgstr "{account} からフォローリクエストがきています"

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:152
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} liked your reply.} other {{account} liked your post.}}} other {{account} liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> liked your reply.} other {<2><3>{1}</3> people</2> liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} から返信にいいねされました。} other {{account} からポストをいいねされました。}}} other {{account} から {postsCount} 件のポストにいいねされました。}}} other {{postType, select, reply {<0><1>{0}</1> 人</0>から返信にいいねされました。} other {<2><3>{1}</3> 人</2> からポストにいいねされました。}}}}"

#: src/components/notification.jsx:194
msgid "A poll you have voted in or created has ended."
msgstr "投票または作成したアンケートが終了しました。"

#: src/components/notification.jsx:195
msgid "A poll you have created has ended."
msgstr "作成したアンケートが終了しました。"

#: src/components/notification.jsx:196
msgid "A poll you have voted in has ended."
msgstr "投票したアンケートが終了しました。"

#: src/components/notification.jsx:197
msgid "A post you interacted with has been edited."
msgstr "あなたが交流した投稿が編集されました。"

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:205
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted & liked your reply.} other {{account} boosted & liked your post.}}} other {{account} boosted & liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted & liked your reply.} other {<2><3>{1}</3> people</2> boosted & liked your post.}}}}"
msgstr ""

#: src/components/notification.jsx:247
msgid "{account} signed up."
msgstr "{account} が登録しました。"

#: src/components/notification.jsx:249
msgid "{account} reported {targetAccount}"
msgstr "{account} が {targetAccount} を報告しました"

#: src/components/notification.jsx:254
msgid "Lost connections with <0>{name}</0>."
msgstr "<0>{name}</0>との接続が切れました。"

#: src/components/notification.jsx:260
msgid "Moderation warning"
msgstr "モデレーション警告"

#: src/components/notification.jsx:265
msgid "Your {year} #Wrapstodon is here!"
msgstr ""

#: src/components/notification.jsx:271
msgid "An admin from <0>{from}</0> has suspended <1>{targetName}</1>, which means you can no longer receive updates from them or interact with them."
msgstr "<0>{from}</0>の管理者が<1>{targetName}</1>を停止しました。これにより、更新を受け取ったり、交流したりすることができなくなります。"

#: src/components/notification.jsx:277
msgid "An admin from <0>{from}</0> has blocked <1>{targetName}</1>. Affected followers: {followersCount}, followings: {followingCount}."
msgstr "<0>{from}</0>の管理者が<1>{targetName}</1>をブロックしました。影響を受けるフォロワー： {followersCount}、フォロー： {followingCount}。"

#: src/components/notification.jsx:283
msgid "You have blocked <0>{targetName}</0>. Removed followers: {followersCount}, followings: {followingCount}."
msgstr ""

#: src/components/notification.jsx:291
msgid "Your account has received a moderation warning."
msgstr "あなたのアカウントはモデレーション警告を受けました。"

#: src/components/notification.jsx:292
msgid "Your account has been disabled."
msgstr ""

#: src/components/notification.jsx:293
msgid "Some of your posts have been marked as sensitive."
msgstr ""

#: src/components/notification.jsx:294
msgid "Some of your posts have been deleted."
msgstr ""

#: src/components/notification.jsx:295
msgid "Your posts will be marked as sensitive from now on."
msgstr ""

#: src/components/notification.jsx:296
msgid "Your account has been limited."
msgstr ""

#: src/components/notification.jsx:297
msgid "Your account has been suspended."
msgstr ""

#: src/components/notification.jsx:374
msgid "[Unknown notification type: {type}]"
msgstr ""

#: src/components/notification.jsx:451
#: src/components/status.jsx:1194
#: src/components/status.jsx:1204
msgid "Boosted/Liked by…"
msgstr ""

#: src/components/notification.jsx:452
msgid "Liked by…"
msgstr ""

#: src/components/notification.jsx:453
msgid "Boosted by…"
msgstr ""

#: src/components/notification.jsx:454
msgid "Followed by…"
msgstr ""

#: src/components/notification.jsx:535
#: src/components/notification.jsx:551
msgid "Learn more <0/>"
msgstr ""

#: src/components/notification.jsx:560
msgid "View #Wrapstodon"
msgstr ""

#: src/components/notification.jsx:801
#: src/components/status.jsx:403
msgid "Read more →"
msgstr "続きを見る →"

#: src/components/poll.jsx:113
msgid "Voted"
msgstr ""

#: src/components/poll.jsx:119
msgid "{optionVotesCount, plural, one {# vote} other {# votes}}"
msgstr ""

#: src/components/poll.jsx:139
#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Hide results"
msgstr "結果を隠す"

#: src/components/poll.jsx:188
msgid "Vote"
msgstr ""

#: src/components/poll.jsx:208
#: src/components/poll.jsx:210
#: src/pages/scheduled-posts.jsx:100
#: src/pages/status.jsx:1294
#: src/pages/status.jsx:1317
msgid "Refresh"
msgstr "更新"

#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Show results"
msgstr "結果を表示"

#. placeholder {0}: shortenNumber(votesCount)
#. placeholder {1}: shortenNumber(votesCount)
#: src/components/poll.jsx:231
msgid "{votesCount, plural, one {<0>{0}</0> vote} other {<1>{1}</1> votes}}"
msgstr "{votesCount, plural, one {<0>{0}</0> 票} other {<1>{1}</1> 票}}"

#. placeholder {0}: shortenNumber(votersCount)
#. placeholder {1}: shortenNumber(votersCount)
#: src/components/poll.jsx:248
msgid "{votersCount, plural, one {<0>{0}</0> voter} other {<1>{1}</1> voters}}"
msgstr ""

#: src/components/poll.jsx:268
msgid "Ended <0/>"
msgstr ""

#: src/components/poll.jsx:272
msgid "Ended"
msgstr ""

#: src/components/poll.jsx:275
msgid "Ending <0/>"
msgstr ""

#: src/components/poll.jsx:279
msgid "Ending"
msgstr ""

#. Relative time in seconds, as short as possible
#. placeholder {0}: seconds < 1 ? 1 : Math.floor(seconds)
#: src/components/relative-time.jsx:61
msgid "{0}s"
msgstr "{0} 秒"

#. Relative time in minutes, as short as possible
#. placeholder {0}: Math.floor(seconds / minute)
#: src/components/relative-time.jsx:66
msgid "{0}m"
msgstr "{0} 分"

#. Relative time in hours, as short as possible
#. placeholder {0}: Math.floor(seconds / hour)
#: src/components/relative-time.jsx:71
msgid "{0}h"
msgstr "{0} 時間"

#: src/components/report-modal.jsx:30
msgid "Spam"
msgstr ""

#: src/components/report-modal.jsx:31
msgid "Malicious links, fake engagement, or repetitive replies"
msgstr ""

#: src/components/report-modal.jsx:34
msgid "Illegal"
msgstr ""

#: src/components/report-modal.jsx:35
msgid "Violates the law of your or the server's country"
msgstr ""

#: src/components/report-modal.jsx:38
msgid "Server rule violation"
msgstr ""

#: src/components/report-modal.jsx:39
msgid "Breaks specific server rules"
msgstr ""

#: src/components/report-modal.jsx:40
msgid "Violation"
msgstr ""

#: src/components/report-modal.jsx:43
msgid "Other"
msgstr ""

#: src/components/report-modal.jsx:44
msgid "Issue doesn't fit other categories"
msgstr ""

#: src/components/report-modal.jsx:111
msgid "Report Post"
msgstr "ポストを報告"

#: src/components/report-modal.jsx:111
msgid "Report @{username}"
msgstr "@{username} を報告"

#: src/components/report-modal.jsx:147
msgid "Pending review"
msgstr ""

#: src/components/report-modal.jsx:189
msgid "Post reported"
msgstr "ポストを報告しました"

#: src/components/report-modal.jsx:189
msgid "Profile reported"
msgstr "プロファイルを報告しました"

#: src/components/report-modal.jsx:197
msgid "Unable to report post"
msgstr "ポストを報告できません"

#: src/components/report-modal.jsx:198
msgid "Unable to report profile"
msgstr "プロフィールを報告できません"

#: src/components/report-modal.jsx:206
msgid "What's the issue with this post?"
msgstr ""

#: src/components/report-modal.jsx:207
msgid "What's the issue with this profile?"
msgstr ""

#: src/components/report-modal.jsx:276
msgid "Additional info"
msgstr ""

#: src/components/report-modal.jsx:299
msgid "Forward to <0>{domain}</0>"
msgstr ""

#: src/components/report-modal.jsx:309
msgid "Send Report"
msgstr "報告を送信"

#: src/components/report-modal.jsx:318
msgid "Muted {username}"
msgstr "{username} をミュートしました"

#: src/components/report-modal.jsx:321
msgid "Unable to mute {username}"
msgstr "{username} をミュートできません"

#: src/components/report-modal.jsx:326
msgid "Send Report <0>+ Mute profile</0>"
msgstr ""

#: src/components/report-modal.jsx:337
msgid "Blocked {username}"
msgstr ""

#: src/components/report-modal.jsx:340
msgid "Unable to block {username}"
msgstr ""

#: src/components/report-modal.jsx:345
msgid "Send Report <0>+ Block profile</0>"
msgstr ""

#: src/components/search-form.jsx:203
msgid "{query} <0>‒ accounts, hashtags & posts</0>"
msgstr ""

#: src/components/search-form.jsx:216
msgid "Posts with <0>{query}</0>"
msgstr ""

#. placeholder {0}: query.replace(/^#/, '')
#: src/components/search-form.jsx:228
msgid "Posts tagged with <0>#{0}</0>"
msgstr ""

#: src/components/search-form.jsx:242
msgid "Look up <0>{query}</0>"
msgstr ""

#: src/components/search-form.jsx:253
msgid "Accounts with <0>{query}</0>"
msgstr ""

#: src/components/shortcuts-settings.jsx:48
msgid "Home / Following"
msgstr "ホーム / フォロー中"

#: src/components/shortcuts-settings.jsx:51
msgid "Public (Local / Federated)"
msgstr "公開 (ローカル/連合)"

#: src/components/shortcuts-settings.jsx:53
msgid "Account"
msgstr "アカウント"

#: src/components/shortcuts-settings.jsx:56
msgid "Hashtag"
msgstr "ハッシュタグ"

#: src/components/shortcuts-settings.jsx:63
msgid "List ID"
msgstr "リスト ID"

#: src/components/shortcuts-settings.jsx:70
msgid "Local only"
msgstr "ローカルのみ"

#: src/components/shortcuts-settings.jsx:75
#: src/components/shortcuts-settings.jsx:84
#: src/components/shortcuts-settings.jsx:122
#: src/pages/login.jsx:203
msgid "Instance"
msgstr "インスタンス"

#: src/components/shortcuts-settings.jsx:78
#: src/components/shortcuts-settings.jsx:87
#: src/components/shortcuts-settings.jsx:125
msgid "Optional, e.g. mastodon.social"
msgstr "(オプション) 例: mastodon.social"

#: src/components/shortcuts-settings.jsx:93
msgid "Search term"
msgstr "検索キーワード"

#: src/components/shortcuts-settings.jsx:96
msgid "Optional, unless for multi-column mode"
msgstr "マルチカラムモード以外、オプション"

#: src/components/shortcuts-settings.jsx:113
msgid "e.g. PixelArt (Max 5, space-separated)"
msgstr "例: PixelArt (最大 5 個、スペース区切り)"

#: src/components/shortcuts-settings.jsx:117
#: src/pages/hashtag.jsx:360
msgid "Media only"
msgstr "メディアのみ対象"

#: src/components/shortcuts-settings.jsx:235
#: src/components/shortcuts.jsx:218
msgid "Shortcuts"
msgstr "ショートカット"

#: src/components/shortcuts-settings.jsx:243
msgid "beta"
msgstr "ベータ"

#: src/components/shortcuts-settings.jsx:249
msgid "Specify a list of shortcuts that'll appear as:"
msgstr ""

#: src/components/shortcuts-settings.jsx:255
msgid "Floating button"
msgstr "フローティングボタン"

#: src/components/shortcuts-settings.jsx:260
msgid "Tab/Menu bar"
msgstr "タブ/メニューバー"

#: src/components/shortcuts-settings.jsx:265
msgid "Multi-column"
msgstr "複数列表示"

#: src/components/shortcuts-settings.jsx:332
msgid "Not available in current view mode"
msgstr "現在のビューモードでは使用できません"

#: src/components/shortcuts-settings.jsx:351
#: src/pages/accounts.jsx:188
msgid "Move up"
msgstr "上へ移動"

#: src/components/shortcuts-settings.jsx:367
#: src/pages/accounts.jsx:203
msgid "Move down"
msgstr "下へ移動"

#: src/components/shortcuts-settings.jsx:379
#: src/components/status.jsx:1474
#: src/pages/list.jsx:195
msgid "Edit"
msgstr "編集"

#: src/components/shortcuts-settings.jsx:400
msgid "Add more than one shortcut/column to make this work."
msgstr ""

#: src/components/shortcuts-settings.jsx:411
msgid "No columns yet. Tap on the Add column button."
msgstr ""

#: src/components/shortcuts-settings.jsx:412
msgid "No shortcuts yet. Tap on the Add shortcut button."
msgstr ""

#: src/components/shortcuts-settings.jsx:415
msgid "Not sure what to add?<0/>Try adding <1>Home / Following and Notifications</1> first."
msgstr ""

#: src/components/shortcuts-settings.jsx:443
msgid "Max {SHORTCUTS_LIMIT} columns"
msgstr "最大 {SHORTCUTS_LIMIT} 列まで"

#: src/components/shortcuts-settings.jsx:444
msgid "Max {SHORTCUTS_LIMIT} shortcuts"
msgstr "最大 {SHORTCUTS_LIMIT} ショートカットまで"

#: src/components/shortcuts-settings.jsx:458
msgid "Import/export"
msgstr "インポート/エクスポート"

#: src/components/shortcuts-settings.jsx:468
msgid "Add column…"
msgstr "列を追加…"

#: src/components/shortcuts-settings.jsx:469
msgid "Add shortcut…"
msgstr "ショートカットを追加…"

#: src/components/shortcuts-settings.jsx:516
msgid "Specific list is optional. For multi-column mode, list is required, else the column will not be shown."
msgstr ""

#: src/components/shortcuts-settings.jsx:517
msgid "For multi-column mode, search term is required, else the column will not be shown."
msgstr ""

#: src/components/shortcuts-settings.jsx:518
msgid "Multiple hashtags are supported. Space-separated."
msgstr ""

#: src/components/shortcuts-settings.jsx:587
msgid "Edit shortcut"
msgstr "ショートカットを編集"

#: src/components/shortcuts-settings.jsx:587
msgid "Add shortcut"
msgstr "ショートカットを追加"

#: src/components/shortcuts-settings.jsx:623
msgid "Timeline"
msgstr "タイムライン"

#: src/components/shortcuts-settings.jsx:649
msgid "List"
msgstr "リスト"

#: src/components/shortcuts-settings.jsx:788
msgid "Import/Export <0>Shortcuts</0>"
msgstr "<0>ショートカット</0>のインポート/エクスポート"

#: src/components/shortcuts-settings.jsx:798
msgid "Import"
msgstr "インポート"

#: src/components/shortcuts-settings.jsx:806
msgid "Paste shortcuts here"
msgstr "ここにショートカットを貼り付け"

#: src/components/shortcuts-settings.jsx:822
msgid "Downloading saved shortcuts from instance server…"
msgstr ""

#: src/components/shortcuts-settings.jsx:851
msgid "Unable to download shortcuts"
msgstr ""

#: src/components/shortcuts-settings.jsx:854
msgid "Download shortcuts from instance server"
msgstr ""

#: src/components/shortcuts-settings.jsx:912
msgid "* Exists in current shortcuts"
msgstr ""

#: src/components/shortcuts-settings.jsx:917
msgid "List may not work if it's from a different account."
msgstr ""

#: src/components/shortcuts-settings.jsx:927
msgid "Invalid settings format"
msgstr ""

#: src/components/shortcuts-settings.jsx:935
msgid "Append to current shortcuts?"
msgstr ""

#: src/components/shortcuts-settings.jsx:938
msgid "Only shortcuts that don’t exist in current shortcuts will be appended."
msgstr ""

#: src/components/shortcuts-settings.jsx:960
msgid "No new shortcuts to import"
msgstr ""

#: src/components/shortcuts-settings.jsx:975
msgid "Shortcuts imported. Exceeded max {SHORTCUTS_LIMIT}, so the rest are not imported."
msgstr ""

#: src/components/shortcuts-settings.jsx:976
#: src/components/shortcuts-settings.jsx:1000
msgid "Shortcuts imported"
msgstr ""

#: src/components/shortcuts-settings.jsx:986
msgid "Import & append…"
msgstr ""

#: src/components/shortcuts-settings.jsx:994
msgid "Override current shortcuts?"
msgstr ""

#: src/components/shortcuts-settings.jsx:995
msgid "Import shortcuts?"
msgstr ""

#: src/components/shortcuts-settings.jsx:1009
msgid "or override…"
msgstr ""

#: src/components/shortcuts-settings.jsx:1009
msgid "Import…"
msgstr "インポート…"

#: src/components/shortcuts-settings.jsx:1018
msgid "Export"
msgstr "エクスポート"

#: src/components/shortcuts-settings.jsx:1033
msgid "Shortcuts copied"
msgstr "ショートカットをコピーしました"

#: src/components/shortcuts-settings.jsx:1036
msgid "Unable to copy shortcuts"
msgstr "ショートカットをコピーできません"

#: src/components/shortcuts-settings.jsx:1050
msgid "Shortcut settings copied"
msgstr "ショートカット設定をコピーしました"

#: src/components/shortcuts-settings.jsx:1053
msgid "Unable to copy shortcut settings"
msgstr "ショートカットの設定をコピーできません"

#: src/components/shortcuts-settings.jsx:1083
msgid "Share"
msgstr "共有"

#: src/components/shortcuts-settings.jsx:1122
msgid "Saving shortcuts to instance server…"
msgstr ""

#: src/components/shortcuts-settings.jsx:1129
msgid "Shortcuts saved"
msgstr ""

#: src/components/shortcuts-settings.jsx:1134
msgid "Unable to save shortcuts"
msgstr ""

#: src/components/shortcuts-settings.jsx:1137
msgid "Sync to instance server"
msgstr ""

#. placeholder {0}: shortcutsStr.length
#: src/components/shortcuts-settings.jsx:1145
msgid "{0, plural, one {# character} other {# characters}}"
msgstr "{0, plural, one {# 文字} other {# 文字}}"

#: src/components/shortcuts-settings.jsx:1157
msgid "Raw Shortcuts JSON"
msgstr ""

#: src/components/shortcuts-settings.jsx:1170
msgid "Import/export settings from/to instance server (Very experimental)"
msgstr ""

#: src/components/status.jsx:277
msgid "Unable to format math"
msgstr ""

#: src/components/status.jsx:292
msgid "Math expressions found."
msgstr ""

#: src/components/status.jsx:294
msgid "Show markup"
msgstr ""

#: src/components/status.jsx:294
msgid "Format math"
msgstr ""

#: src/components/status.jsx:689
msgid "<0/> <1>boosted</1>"
msgstr ""

#: src/components/status.jsx:792
msgid "Sorry, your current logged-in instance can't interact with this post from another instance."
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:946
msgid "Unliked @{0}'s post"
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:947
msgid "Liked @{0}'s post"
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:986
msgid "Unbookmarked @{0}'s post"
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:987
msgid "Bookmarked @{0}'s post"
msgstr ""

#: src/components/status.jsx:1086
msgid "Some media have no descriptions."
msgstr ""

#. placeholder {0}: rtf.format(-statusMonthsAgo, 'month')
#: src/components/status.jsx:1093
msgid "Old post (<0>{0}</0>)"
msgstr ""

#: src/components/status.jsx:1117
#: src/components/status.jsx:1157
#: src/components/status.jsx:2617
#: src/components/status.jsx:2640
msgid "Unboost"
msgstr ""

#: src/components/status.jsx:1133
#: src/components/status.jsx:2632
msgid "Quote"
msgstr "引用"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1145
#: src/components/status.jsx:1611
msgid "Unboosted @{0}'s post"
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:1146
#: src/components/status.jsx:1612
msgid "Boosted @{0}'s post"
msgstr ""

#: src/components/status.jsx:1158
msgid "Boost…"
msgstr "ブースト…"

#: src/components/status.jsx:1170
#: src/components/status.jsx:1905
#: src/components/status.jsx:2653
msgid "Unlike"
msgstr ""

#: src/components/status.jsx:1171
#: src/components/status.jsx:1905
#: src/components/status.jsx:1906
#: src/components/status.jsx:2653
#: src/components/status.jsx:2654
msgid "Like"
msgstr ""

#: src/components/status.jsx:1180
#: src/components/status.jsx:2665
msgid "Unbookmark"
msgstr "ブックマーク解除"

#: src/components/status.jsx:1263
msgid "Post text copied"
msgstr ""

#: src/components/status.jsx:1266
msgid "Unable to copy post text"
msgstr ""

#: src/components/status.jsx:1272
msgid "Copy post text"
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:1290
msgid "View post by <0>@{0}</0>"
msgstr ""

#: src/components/status.jsx:1311
msgid "Show Edit History"
msgstr "編集履歴を表示"

#: src/components/status.jsx:1314
msgid "Edited: {editedDateText}"
msgstr "編集済み： {editedDateText}"

#: src/components/status.jsx:1381
#: src/components/status.jsx:3433
msgid "Embed post"
msgstr "ポストを埋め込む"

#: src/components/status.jsx:1395
msgid "Conversation unmuted"
msgstr ""

#: src/components/status.jsx:1395
msgid "Conversation muted"
msgstr ""

#: src/components/status.jsx:1401
msgid "Unable to unmute conversation"
msgstr ""

#: src/components/status.jsx:1402
msgid "Unable to mute conversation"
msgstr ""

#: src/components/status.jsx:1411
msgid "Unmute conversation"
msgstr "会話のミュートを解除"

#: src/components/status.jsx:1418
msgid "Mute conversation"
msgstr "会話をミュート"

#: src/components/status.jsx:1434
msgid "Post unpinned from profile"
msgstr ""

#: src/components/status.jsx:1435
msgid "Post pinned to profile"
msgstr ""

#: src/components/status.jsx:1440
msgid "Unable to unpin post"
msgstr ""

#: src/components/status.jsx:1440
msgid "Unable to pin post"
msgstr ""

#: src/components/status.jsx:1449
msgid "Unpin from profile"
msgstr ""

#: src/components/status.jsx:1456
msgid "Pin to profile"
msgstr ""

#: src/components/status.jsx:1485
msgid "Delete this post?"
msgstr ""

#: src/components/status.jsx:1501
msgid "Post deleted"
msgstr "ポストを削除しました"

#: src/components/status.jsx:1504
msgid "Unable to delete post"
msgstr "ポストを削除できません"

#: src/components/status.jsx:1532
msgid "Report post…"
msgstr ""

#: src/components/status.jsx:1906
#: src/components/status.jsx:1942
#: src/components/status.jsx:2654
msgid "Liked"
msgstr "いいね！しました"

#: src/components/status.jsx:1939
#: src/components/status.jsx:2641
msgid "Boosted"
msgstr "ブーストしました"

#: src/components/status.jsx:1949
#: src/components/status.jsx:2666
msgid "Bookmarked"
msgstr "ブックマークしました"

#: src/components/status.jsx:1953
msgid "Pinned"
msgstr "ピン留めしました"

#: src/components/status.jsx:1999
#: src/components/status.jsx:2478
msgid "Deleted"
msgstr "削除しました"

#: src/components/status.jsx:2040
msgid "{repliesCount, plural, one {# reply} other {# replies}}"
msgstr "{repliesCount, plural, one {# 件の返信} other {# 件の返信}}"

#. placeholder {0}: snapStates.statusThreadNumber[sKey] ? ` ${snapStates.statusThreadNumber[sKey]}/X` : ''
#: src/components/status.jsx:2130
msgid "Thread{0}"
msgstr ""

#: src/components/status.jsx:2208
#: src/components/status.jsx:2270
#: src/components/status.jsx:2374
msgid "Show less"
msgstr ""

#: src/components/status.jsx:2208
#: src/components/status.jsx:2270
msgid "Show content"
msgstr "コンテンツを表示"

#. placeholder {0}: filterInfo.titlesStr
#. placeholder {0}: filterInfo?.titlesStr
#: src/components/status.jsx:2370
#: src/pages/catchup.jsx:1879
msgid "Filtered: {0}"
msgstr "フィルター: {0}"

#: src/components/status.jsx:2374
msgid "Show media"
msgstr "メディアを表示"

#: src/components/status.jsx:2514
msgid "Edited"
msgstr ""

#: src/components/status.jsx:2591
msgid "Comments"
msgstr ""

#. More from [Author]
#: src/components/status.jsx:2891
msgid "More from <0/>"
msgstr ""

#: src/components/status.jsx:3193
msgid "Edit History"
msgstr ""

#: src/components/status.jsx:3197
msgid "Failed to load history"
msgstr ""

#: src/components/status.jsx:3202
#: src/pages/annual-report.jsx:45
msgid "Loading…"
msgstr ""

#: src/components/status.jsx:3438
msgid "HTML Code"
msgstr "HTML コード"

#: src/components/status.jsx:3455
msgid "HTML code copied"
msgstr "HTMLコードをコピーしました"

#: src/components/status.jsx:3458
msgid "Unable to copy HTML code"
msgstr ""

#: src/components/status.jsx:3470
msgid "Media attachments:"
msgstr ""

#: src/components/status.jsx:3492
msgid "Account Emojis:"
msgstr ""

#: src/components/status.jsx:3523
#: src/components/status.jsx:3568
msgid "static URL"
msgstr ""

#: src/components/status.jsx:3537
msgid "Emojis:"
msgstr ""

#: src/components/status.jsx:3582
msgid "Notes:"
msgstr ""

#: src/components/status.jsx:3586
msgid "This is static, unstyled and scriptless. You may need to apply your own styles and edit as needed."
msgstr ""

#: src/components/status.jsx:3592
msgid "Polls are not interactive, becomes a list with vote counts."
msgstr ""

#: src/components/status.jsx:3597
msgid "Media attachments can be images, videos, audios or any file types."
msgstr ""

#: src/components/status.jsx:3603
msgid "Post could be edited or deleted later."
msgstr ""

#: src/components/status.jsx:3609
msgid "Preview"
msgstr ""

#: src/components/status.jsx:3618
msgid "Note: This preview is lightly styled."
msgstr ""

#. [Name] [Visibility icon] boosted
#: src/components/status.jsx:3871
msgid "<0/> <1/> boosted"
msgstr ""

#: src/components/status.jsx:3973
msgid "Post hidden by your filters"
msgstr ""

#: src/components/status.jsx:3974
msgid "Post removed by author."
msgstr ""

#: src/components/status.jsx:3975
msgid "You’re not authorized to view this post."
msgstr ""

#: src/components/status.jsx:3976
msgid "Post pending author approval."
msgstr ""

#: src/components/status.jsx:3977
#: src/components/status.jsx:3978
msgid "Quoting not allowed by the author."
msgstr ""

#: src/components/timeline.jsx:485
#: src/pages/settings.jsx:1218
msgid "New posts"
msgstr "新しい投稿"

#: src/components/timeline.jsx:586
#: src/pages/home.jsx:228
#: src/pages/notifications.jsx:898
#: src/pages/status.jsx:1077
#: src/pages/status.jsx:1454
msgid "Try again"
msgstr ""

#. placeholder {0}: fItems.length
#: src/components/timeline.jsx:621
msgid "{0, plural, one {# Boost} other {# Boosts}}"
msgstr ""

#: src/components/timeline.jsx:626
msgid "Pinned posts"
msgstr ""

#: src/components/timeline.jsx:985
#: src/components/timeline.jsx:992
#: src/pages/catchup.jsx:1897
msgid "Thread"
msgstr "スレッド"

#. placeholder {0}: filterInfo.titlesStr
#: src/components/timeline.jsx:1007
msgid "<0>Filtered</0>: <1>{0}</1>"
msgstr ""

#: src/components/translation-block.jsx:196
msgid "Auto-translated from {sourceLangText}"
msgstr ""

#: src/components/translation-block.jsx:234
msgid "Translating…"
msgstr ""

#: src/components/translation-block.jsx:237
msgid "Translate from {sourceLangText} (auto-detected)"
msgstr ""

#: src/components/translation-block.jsx:238
msgid "Translate from {sourceLangText}"
msgstr ""

#. placeholder {0}: detectedLang ?? '…'
#: src/components/translation-block.jsx:266
msgid "Auto ({0})"
msgstr ""

#: src/components/translation-block.jsx:279
msgid "Failed to translate"
msgstr "翻訳に失敗しました"

#: src/compose.jsx:33
msgid "Editing source status"
msgstr ""

#. placeholder {0}: replyToStatus.account?.acct || replyToStatus.account?.username
#: src/compose.jsx:35
msgid "Replying to @{0}"
msgstr ""

#: src/compose.jsx:63
msgid "You may close this page now."
msgstr ""

#: src/compose.jsx:71
msgid "Close window"
msgstr ""

#: src/compose.jsx:87
msgid "Login required."
msgstr ""

#: src/compose.jsx:91
#: src/pages/annual-report.jsx:164
#: src/pages/http-route.jsx:91
#: src/pages/login.jsx:280
msgid "Go home"
msgstr "ホームに戻る"

#: src/pages/account-statuses.jsx:238
msgid "Account posts"
msgstr ""

#: src/pages/account-statuses.jsx:245
msgid "{accountDisplay} (+ Replies)"
msgstr ""

#: src/pages/account-statuses.jsx:247
msgid "{accountDisplay} (- Boosts)"
msgstr ""

#: src/pages/account-statuses.jsx:249
msgid "{accountDisplay} (#{tagged})"
msgstr ""

#: src/pages/account-statuses.jsx:251
msgid "{accountDisplay} (Media)"
msgstr ""

#: src/pages/account-statuses.jsx:257
msgid "{accountDisplay} ({monthYear})"
msgstr ""

#: src/pages/account-statuses.jsx:323
msgid "Clear filters"
msgstr ""

#: src/pages/account-statuses.jsx:326
msgid "Clear"
msgstr ""

#: src/pages/account-statuses.jsx:340
msgid "Showing post with replies"
msgstr ""

#: src/pages/account-statuses.jsx:345
msgid "+ Replies"
msgstr ""

#: src/pages/account-statuses.jsx:351
msgid "Showing posts without boosts"
msgstr ""

#: src/pages/account-statuses.jsx:356
msgid "- Boosts"
msgstr ""

#: src/pages/account-statuses.jsx:362
msgid "Showing posts with media"
msgstr ""

#. placeholder {0}: tag.name
#: src/pages/account-statuses.jsx:379
msgid "Showing posts tagged with #{0}"
msgstr ""

#. placeholder {0}: date.toLocaleString(i18n.locale, { month: 'long', year: 'numeric', })
#: src/pages/account-statuses.jsx:418
msgid "Showing posts in {0}"
msgstr ""

#: src/pages/account-statuses.jsx:508
msgid "Nothing to see here yet."
msgstr "ここに表示するものはまだありません。"

#: src/pages/account-statuses.jsx:509
#: src/pages/public.jsx:99
#: src/pages/trending.jsx:452
msgid "Unable to load posts"
msgstr "ポストを読み込むことができません"

#: src/pages/account-statuses.jsx:550
#: src/pages/account-statuses.jsx:580
msgid "Unable to fetch account info"
msgstr "アカウント情報を取得できませんでした"

#. placeholder {0}: accountInstance ? ( <> {' '} (<b>{punycode.toUnicode(accountInstance)}</b>) </> ) : null
#: src/pages/account-statuses.jsx:557
msgid "Switch to account's instance {0}"
msgstr "このアカウントのインスタンス {0} の表示に切り替える"

#: src/pages/account-statuses.jsx:587
msgid "Switch to my instance (<0>{currentInstance}</0>)"
msgstr "自分のインスタンス (<0>{currentInstance}</0>) の表示に切り替える"

#: src/pages/account-statuses.jsx:660
msgid "Month"
msgstr ""

#: src/pages/accounts.jsx:64
msgid "Current"
msgstr "現在のアカウント"

#: src/pages/accounts.jsx:110
msgid "Default"
msgstr "デフォルト"

#: src/pages/accounts.jsx:132
msgid "Switch to this account"
msgstr ""

#: src/pages/accounts.jsx:141
msgid "Switch in new tab/window"
msgstr ""

#: src/pages/accounts.jsx:155
msgid "View profile…"
msgstr "プロフィールの表示…"

#: src/pages/accounts.jsx:173
msgid "Set as default"
msgstr "デフォルトに設定"

#. placeholder {0}: account.info.acct
#: src/pages/accounts.jsx:215
msgid "Log out <0>@{0}</0>?"
msgstr ""

#: src/pages/accounts.jsx:244
msgid "Log out…"
msgstr "ログアウト…"

#. placeholder {0}: niceDateTime(account.createdAt)
#: src/pages/accounts.jsx:251
msgid "Connected on {0} (<0/>)"
msgstr ""

#: src/pages/accounts.jsx:268
msgid "Add an existing account"
msgstr "アカウントを追加"

#: src/pages/accounts.jsx:275
msgid "Note: <0>Default</0> account will always be used for first load. Switched accounts will persist during the session."
msgstr "注意: <0>デフォルト</0>アカウントは常に最初に読み込まれます。セッション中に切り替えたアカウントは維持されます。"

#: src/pages/bookmarks.jsx:28
msgid "No bookmarks yet. Go bookmark something!"
msgstr ""

#: src/pages/bookmarks.jsx:29
msgid "Unable to load bookmarks."
msgstr ""

#: src/pages/catchup.jsx:54
msgid "last 1 hour"
msgstr "1時間以内"

#: src/pages/catchup.jsx:55
msgid "last 2 hours"
msgstr "2時間以内"

#: src/pages/catchup.jsx:56
msgid "last 3 hours"
msgstr "3時間以内"

#: src/pages/catchup.jsx:57
msgid "last 4 hours"
msgstr "4時間以内"

#: src/pages/catchup.jsx:58
msgid "last 5 hours"
msgstr "5時間以内"

#: src/pages/catchup.jsx:59
msgid "last 6 hours"
msgstr "6時間以内"

#: src/pages/catchup.jsx:60
msgid "last 7 hours"
msgstr "7時間以内"

#: src/pages/catchup.jsx:61
msgid "last 8 hours"
msgstr "8時間以内"

#: src/pages/catchup.jsx:62
msgid "last 9 hours"
msgstr "9時間以内"

#: src/pages/catchup.jsx:63
msgid "last 10 hours"
msgstr "10時間以内"

#: src/pages/catchup.jsx:64
msgid "last 11 hours"
msgstr "11時間以内"

#: src/pages/catchup.jsx:65
msgid "last 12 hours"
msgstr "12時間以内"

#: src/pages/catchup.jsx:66
msgid "beyond 12 hours"
msgstr "12時間以上"

#: src/pages/catchup.jsx:73
msgid "Followed tags"
msgstr "フォロー中のタグ"

#: src/pages/catchup.jsx:74
msgid "Groups"
msgstr "グループ"

#: src/pages/catchup.jsx:597
msgid "Showing {selectedFilterCategory, select, all {all posts} original {original posts} replies {replies} boosts {boosts} followedTags {followed tags} groups {groups} filtered {filtered posts}}, {sortBy, select, createdAt {{sortOrder, select, asc {oldest} desc {latest}}} reblogsCount {{sortOrder, select, asc {fewest boosts} desc {most boosts}}} favouritesCount {{sortOrder, select, asc {fewest likes} desc {most likes}}} repliesCount {{sortOrder, select, asc {fewest replies} desc {most replies}}} density {{sortOrder, select, asc {least dense} desc {most dense}}}} first{groupBy, select, account {, grouped by authors} other {}}"
msgstr "{selectedFilterCategory, select, all {すべてのポスト} original {オリジナルポスト} replies {返信} boosts {ブースト} followedTags {フォロー中のタグ} groups {グループ} filtered {でフィルターされたポスト}} を {sortBy, select, createdAt {{sortOrder, select, asc {古い} desc {新しい}}} reblogsCount {{sortOrder, select, asc {ブーストが少ない} desc {ブーストが多い}}} favouritesCount {{sortOrder, select, asc {いいね！が少ない} desc {いいね！が多い}}} repliesCount {{sortOrder, select, asc {返信が少ない} desc {返信が多い}}} density {{sortOrder, select, asc {密度が低い} desc {密度が高い}}}} 順で {groupBy, select, account {ユーザーごとにグループ化して} other {}}表示"

#: src/pages/catchup.jsx:889
#: src/pages/catchup.jsx:913
msgid "Catch-up <0>beta</0>"
msgstr "キャッチアップ <0>β</0>"

#: src/pages/catchup.jsx:903
#: src/pages/catchup.jsx:1588
msgid "Help"
msgstr "ヘルプ"

#: src/pages/catchup.jsx:919
msgid "What is this?"
msgstr "これは何？"

#: src/pages/catchup.jsx:922
msgid "Catch-up is a separate timeline for your followings, offering a high-level view at a glance, with a simple, email-inspired interface to effortlessly sort and filter through posts."
msgstr "「キャッチアップ」はあなたのための特別なタイムラインです。メールにヒントを得たシンプルなインターフェイスで、ポストを簡単に並べ替えたりフィルターしながら、概要を一目で確認できます。"

#: src/pages/catchup.jsx:933
msgid "Preview of Catch-up UI"
msgstr "キャッチアップUIのプレビュー"

#: src/pages/catchup.jsx:942
msgid "Let's catch up"
msgstr "キャッチアップ！"

#: src/pages/catchup.jsx:947
msgid "Let's catch up on the posts from your followings."
msgstr "フォローしている人のポストをキャッチアップしよう！"

#: src/pages/catchup.jsx:951
msgid "Show me all posts from…"
msgstr "… からのすべての投稿を表示"

#: src/pages/catchup.jsx:974
msgid "until the max"
msgstr "最大限まで"

#: src/pages/catchup.jsx:1004
msgid "Catch up"
msgstr "キャッチアップ"

#: src/pages/catchup.jsx:1010
msgid "Overlaps with your last catch-up"
msgstr "前回のキャッチアップと重複します"

#. placeholder {0}: dtf.format(new Date(lastCatchupEndAt))
#: src/pages/catchup.jsx:1022
msgid "Until the last catch-up ({0})"
msgstr "前回のキャッチアップまで ({0})"

#: src/pages/catchup.jsx:1031
msgid "Note: your instance might only show a maximum of 800 posts in the Home timeline regardless of the time range. Could be less or more."
msgstr "注：あなたのインスタンスでは時間範囲に関係なく、ホームタイムラインの最大800個のポストしか表示されない可能性があります。\n"
"これより少ない場合も、多い場合もあります。"

#: src/pages/catchup.jsx:1041
msgid "Previously…"
msgstr "以前のキャッチアップ…"

#. placeholder {0}: pc.count
#: src/pages/catchup.jsx:1059
msgid "{0, plural, one {# post} other {# posts}}"
msgstr "{0, plural, other {# 件のポスト}}"

#: src/pages/catchup.jsx:1069
msgid "Remove this catch-up?"
msgstr "このキャッチアップを削除しますか？"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1072
msgid "Removing Catch-up {0}"
msgstr ""

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1076
msgid "Catch-up {0} removed"
msgstr ""

#: src/pages/catchup.jsx:1090
msgid "Note: Only max 3 will be stored. The rest will be automatically removed."
msgstr "注意：最大3つのみが保存されます。残りは自動的に削除されます。"

#: src/pages/catchup.jsx:1105
msgid "Fetching posts…"
msgstr "ポストの取得中…"

#: src/pages/catchup.jsx:1108
msgid "This might take a while."
msgstr "少々お待ち下さい。"

#: src/pages/catchup.jsx:1143
msgid "Reset filters"
msgstr "フィルターをリセット"

#: src/pages/catchup.jsx:1151
#: src/pages/catchup.jsx:1594
msgid "Top links"
msgstr "トップリンク"

#. placeholder {0}: sharers.map((s) => { const { avatarStatic, displayName } = s; return ( <button type="button" class="plain" style={{ padding: 0, }} onClick={(e) => { e.preventDefault(); e.stopPropagation(); // Reset and filter to author const { id } = s; setSelectedAuthor(id); setSelectedFilterCategory('all'); }} > <Avatar url={avatarStatic} size="s" alt={displayName} /> </button> ); })
#: src/pages/catchup.jsx:1264
msgid "Shared by {0}"
msgstr "{0} が共有しました"

#: src/pages/catchup.jsx:1319
#: src/pages/mentions.jsx:154
#: src/pages/search.jsx:329
msgid "All"
msgstr "すべて"

#. placeholder {0}: authorCountsList.length
#: src/pages/catchup.jsx:1404
msgid "{0, plural, one {# author} other {# authors}}"
msgstr "{0, plural, other {# 人}}"

#: src/pages/catchup.jsx:1416
msgid "Sort"
msgstr "ソート"

#: src/pages/catchup.jsx:1447
msgid "Date"
msgstr "日時"

#: src/pages/catchup.jsx:1451
msgid "Density"
msgstr "密度"

#. js-lingui-explicit-id
#: src/pages/catchup.jsx:1474
msgid "group.filter"
msgstr ""

#: src/pages/catchup.jsx:1489
msgid "Authors"
msgstr "ユーザー"

#: src/pages/catchup.jsx:1490
msgid "None"
msgstr "なし"

#: src/pages/catchup.jsx:1506
msgid "Show all authors"
msgstr "すべての投稿者を表示"

#: src/pages/catchup.jsx:1557
msgid "You don't have to read everything."
msgstr "すべてを読む必要はありません。"

#: src/pages/catchup.jsx:1558
msgid "That's all."
msgstr "これで全部です。"

#: src/pages/catchup.jsx:1566
msgid "Back to top"
msgstr "トップへ戻る"

#: src/pages/catchup.jsx:1597
msgid "Links shared by followings, sorted by shared counts, boosts and likes."
msgstr "フォロー中のユーザーによって共有されたリンクが表示されます。共有された回数や、ブースト数、いいね！数でソートされます。"

#: src/pages/catchup.jsx:1603
msgid "Sort: Density"
msgstr "ソート: 密度"

#: src/pages/catchup.jsx:1606
msgid "Posts are sorted by information density or depth. Shorter posts are \"lighter\" while longer posts are \"heavier\". Posts with photos are \"heavier\" than posts without photos."
msgstr "ポストは情報密度によってソートされます。短いポストは「低く」、長いポストは「高く」なります。また、画像付きポストは画像なしポストより「高く」なります。"

#: src/pages/catchup.jsx:1613
msgid "Group: Authors"
msgstr "グループ: ユーザー"

#: src/pages/catchup.jsx:1616
msgid "Posts are grouped by authors, sorted by posts count per author."
msgstr "ポストはユーザーごとにグループ化され、ユーザーごとのポスト数でソートされます"

#: src/pages/catchup.jsx:1663
msgid "Next author"
msgstr "次のユーザー"

#: src/pages/catchup.jsx:1671
msgid "Previous author"
msgstr "前のユーザー"

#: src/pages/catchup.jsx:1687
msgid "Scroll to top"
msgstr "一番上までスクロール"

#: src/pages/favourites.jsx:28
msgid "No likes yet. Go like something!"
msgstr ""

#: src/pages/favourites.jsx:29
msgid "Unable to load likes."
msgstr ""

#: src/pages/filters.jsx:23
msgid "Home and lists"
msgstr ""

#: src/pages/filters.jsx:25
msgid "Public timelines"
msgstr ""

#: src/pages/filters.jsx:26
msgid "Conversations"
msgstr ""

#: src/pages/filters.jsx:27
msgid "Profiles"
msgstr ""

#: src/pages/filters.jsx:42
msgid "Never"
msgstr ""

#: src/pages/filters.jsx:104
#: src/pages/filters.jsx:229
msgid "New filter"
msgstr ""

#. placeholder {0}: filters.length
#: src/pages/filters.jsx:152
msgid "{0, plural, one {# filter} other {# filters}}"
msgstr ""

#: src/pages/filters.jsx:167
msgid "Unable to load filters."
msgstr ""

#: src/pages/filters.jsx:171
msgid "No filters yet."
msgstr ""

#: src/pages/filters.jsx:178
msgid "Add filter"
msgstr ""

#: src/pages/filters.jsx:229
msgid "Edit filter"
msgstr ""

#: src/pages/filters.jsx:346
msgid "Unable to edit filter"
msgstr ""

#: src/pages/filters.jsx:347
msgid "Unable to create filter"
msgstr ""

#: src/pages/filters.jsx:356
msgid "Title"
msgstr ""

#: src/pages/filters.jsx:397
msgid "Whole word"
msgstr ""

#: src/pages/filters.jsx:423
msgid "No keywords. Add one."
msgstr ""

#: src/pages/filters.jsx:450
msgid "Add keyword"
msgstr ""

#. placeholder {0}: filteredEditKeywords.length
#: src/pages/filters.jsx:454
msgid "{0, plural, one {# keyword} other {# keywords}}"
msgstr ""

#: src/pages/filters.jsx:467
msgid "Filter from…"
msgstr ""

#: src/pages/filters.jsx:493
msgid "* Not implemented yet"
msgstr ""

#: src/pages/filters.jsx:499
msgid "Status: <0><1/></0>"
msgstr ""

#: src/pages/filters.jsx:508
msgid "Change expiry"
msgstr ""

#: src/pages/filters.jsx:508
msgid "Expiry"
msgstr ""

#: src/pages/filters.jsx:527
msgid "Filtered post will be…"
msgstr ""

#: src/pages/filters.jsx:538
msgid "obscured (media only)"
msgstr ""

#: src/pages/filters.jsx:552
msgid "minimized"
msgstr ""

#: src/pages/filters.jsx:562
msgid "hidden"
msgstr ""

#: src/pages/filters.jsx:579
msgid "Delete this filter?"
msgstr ""

#: src/pages/filters.jsx:592
msgid "Unable to delete filter."
msgstr ""

#: src/pages/filters.jsx:625
msgid "Expired"
msgstr ""

#: src/pages/filters.jsx:627
msgid "Expiring <0/>"
msgstr ""

#: src/pages/filters.jsx:631
msgid "Never expires"
msgstr ""

#. placeholder {0}: followedHashtags.length
#: src/pages/followed-hashtags.jsx:71
msgid "{0, plural, one {# hashtag} other {# hashtags}}"
msgstr ""

#: src/pages/followed-hashtags.jsx:86
msgid "Unable to load followed hashtags."
msgstr ""

#: src/pages/followed-hashtags.jsx:90
msgid "No hashtags followed yet."
msgstr ""

#: src/pages/following.jsx:147
msgid "Nothing to see here."
msgstr ""

#: src/pages/following.jsx:148
#: src/pages/list.jsx:109
msgid "Unable to load posts."
msgstr ""

#: src/pages/hashtag.jsx:56
msgid "{hashtagTitle} (Media only) on {instance}"
msgstr ""

#: src/pages/hashtag.jsx:57
msgid "{hashtagTitle} on {instance}"
msgstr ""

#: src/pages/hashtag.jsx:59
msgid "{hashtagTitle} (Media only)"
msgstr ""

#: src/pages/hashtag.jsx:60
msgid "{hashtagTitle}"
msgstr ""

#: src/pages/hashtag.jsx:185
msgid "No one has posted anything with this tag yet."
msgstr ""

#: src/pages/hashtag.jsx:186
msgid "Unable to load posts with this tag"
msgstr ""

#: src/pages/hashtag.jsx:212
msgid "Unfollow #{hashtag}?"
msgstr ""

#: src/pages/hashtag.jsx:227
msgid "Unfollowed #{hashtag}"
msgstr ""

#: src/pages/hashtag.jsx:242
msgid "Followed #{hashtag}"
msgstr ""

#: src/pages/hashtag.jsx:258
msgid "Following…"
msgstr ""

#: src/pages/hashtag.jsx:287
msgid "Unfeatured on profile"
msgstr ""

#: src/pages/hashtag.jsx:301
msgid "Unable to unfeature on profile"
msgstr ""

#: src/pages/hashtag.jsx:310
#: src/pages/hashtag.jsx:326
msgid "Featured on profile"
msgstr ""

#: src/pages/hashtag.jsx:398
msgid "{TOTAL_TAGS_LIMIT, plural, other {Max # tags}}"
msgstr ""

#: src/pages/hashtag.jsx:401
msgid "Add hashtag"
msgstr ""

#: src/pages/hashtag.jsx:433
msgid "Remove hashtag"
msgstr ""

#: src/pages/hashtag.jsx:447
msgid "{SHORTCUTS_LIMIT, plural, one {Max # shortcut reached. Unable to add shortcut.} other {Max # shortcuts reached. Unable to add shortcut.}}"
msgstr ""

#: src/pages/hashtag.jsx:476
msgid "This shortcut already exists"
msgstr ""

#: src/pages/hashtag.jsx:479
msgid "Hashtag shortcut added"
msgstr ""

#: src/pages/hashtag.jsx:485
msgid "Add to Shortcuts"
msgstr ""

#: src/pages/hashtag.jsx:491
#: src/pages/public.jsx:141
#: src/pages/trending.jsx:481
msgid "Enter a new instance e.g. \"mastodon.social\""
msgstr ""

#: src/pages/hashtag.jsx:494
#: src/pages/public.jsx:144
#: src/pages/trending.jsx:484
msgid "Invalid instance"
msgstr ""

#: src/pages/hashtag.jsx:508
#: src/pages/public.jsx:158
#: src/pages/trending.jsx:496
msgid "Go to another instance…"
msgstr ""

#: src/pages/hashtag.jsx:521
#: src/pages/public.jsx:171
#: src/pages/trending.jsx:507
msgid "Go to my instance (<0>{currentInstance}</0>)"
msgstr ""

#: src/pages/home.jsx:224
msgid "Unable to fetch notifications."
msgstr ""

#: src/pages/home.jsx:245
msgid "<0>New</0> <1>Follow Requests</1>"
msgstr ""

#: src/pages/home.jsx:251
msgid "See all"
msgstr ""

#: src/pages/http-route.jsx:68
msgid "Resolving…"
msgstr ""

#: src/pages/http-route.jsx:79
msgid "Unable to resolve URL"
msgstr ""

#: src/pages/list.jsx:108
msgid "Nothing yet."
msgstr ""

#: src/pages/list.jsx:201
#: src/pages/list.jsx:307
msgid "Manage members"
msgstr ""

#. placeholder {0}: account.username
#: src/pages/list.jsx:342
msgid "Remove <0>@{0}</0> from list?"
msgstr ""

#: src/pages/list.jsx:388
msgid "Remove…"
msgstr ""

#. placeholder {0}: lists.length
#: src/pages/lists.jsx:115
msgid "{0, plural, one {# list} other {# lists}}"
msgstr ""

#: src/pages/lists.jsx:135
msgid "No lists yet."
msgstr ""

#: src/pages/login.jsx:122
#: src/pages/login.jsx:132
msgid "Failed to register application"
msgstr ""

#: src/pages/login.jsx:218
msgid "instance domain"
msgstr ""

#: src/pages/login.jsx:242
msgid "e.g. “mastodon.social”"
msgstr "例: “mastodon.social”"

#: src/pages/login.jsx:253
msgid "Failed to log in. Please try again or try another instance."
msgstr ""

#: src/pages/login.jsx:265
msgid "Continue with {selectedInstanceText}"
msgstr "{selectedInstanceText} にログイン"

#: src/pages/login.jsx:266
msgid "Continue"
msgstr "ログイン"

#: src/pages/login.jsx:274
msgid "Don't have an account? Create one!"
msgstr "アカウントを持っていませんか？アカウントを作成しましょう！"

#: src/pages/mentions.jsx:21
msgid "Private mentions"
msgstr "非公開の返信"

#: src/pages/mentions.jsx:166
msgid "Private"
msgstr "非公開"

#: src/pages/mentions.jsx:176
msgid "No one mentioned you :("
msgstr ""

#: src/pages/mentions.jsx:177
msgid "Unable to load mentions."
msgstr ""

#: src/pages/notifications.jsx:108
msgid "You don't follow"
msgstr ""

#: src/pages/notifications.jsx:109
msgid "Who don't follow you"
msgstr ""

#: src/pages/notifications.jsx:110
msgid "With a new account"
msgstr ""

#: src/pages/notifications.jsx:111
msgid "Who unsolicitedly private mention you"
msgstr ""

#: src/pages/notifications.jsx:112
msgid "Who are limited by server moderators"
msgstr ""

#: src/pages/notifications.jsx:605
#: src/pages/notifications.jsx:946
msgid "Notifications settings"
msgstr "通知設定"

#: src/pages/notifications.jsx:623
msgid "New notifications"
msgstr ""

#. placeholder {0}: announcements.length
#: src/pages/notifications.jsx:634
msgid "{0, plural, one {Announcement} other {Announcements}}"
msgstr ""

#: src/pages/notifications.jsx:681
#: src/pages/settings.jsx:1206
msgid "Follow requests"
msgstr "フォローリクエスト"

#. placeholder {0}: followRequests.length
#: src/pages/notifications.jsx:686
msgid "{0, plural, one {# follow request} other {# follow requests}}"
msgstr ""

#. placeholder {0}: notificationsPolicy.summary.pendingRequestsCount
#: src/pages/notifications.jsx:741
msgid "{0, plural, one {Filtered notifications from # person} other {Filtered notifications from # people}}"
msgstr ""

#: src/pages/notifications.jsx:814
msgid "Only mentions"
msgstr ""

#: src/pages/notifications.jsx:818
msgid "Today"
msgstr ""

#: src/pages/notifications.jsx:823
msgid "You're all caught up."
msgstr ""

#: src/pages/notifications.jsx:846
msgid "Yesterday"
msgstr ""

#: src/pages/notifications.jsx:894
msgid "Unable to load notifications"
msgstr ""

#: src/pages/notifications.jsx:973
msgid "Notifications settings updated"
msgstr "通知設定が更新されました"

#: src/pages/notifications.jsx:981
msgid "Filter out notifications from people:"
msgstr ""

#: src/pages/notifications.jsx:995
msgid "Filter"
msgstr ""

#: src/pages/notifications.jsx:998
msgid "Ignore"
msgstr ""

#. placeholder {0}: niceDateTime(updatedAtDate)
#: src/pages/notifications.jsx:1071
msgid "Updated <0>{0}</0>"
msgstr ""

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1139
msgid "View notifications from <0>@{0}</0>"
msgstr ""

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1160
msgid "Notifications from <0>@{0}</0>"
msgstr ""

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1228
msgid "Notifications from @{0} will not be filtered from now on."
msgstr ""

#: src/pages/notifications.jsx:1233
msgid "Unable to accept notification request"
msgstr ""

#: src/pages/notifications.jsx:1238
msgid "Allow"
msgstr ""

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1258
msgid "Notifications from @{0} will not show up in Filtered notifications from now on."
msgstr ""

#: src/pages/notifications.jsx:1263
msgid "Unable to dismiss notification request"
msgstr ""

#: src/pages/notifications.jsx:1268
msgid "Dismiss"
msgstr ""

#: src/pages/notifications.jsx:1283
msgid "Dismissed"
msgstr ""

#: src/pages/public.jsx:28
msgid "Local timeline ({instance})"
msgstr ""

#: src/pages/public.jsx:29
msgid "Federated timeline ({instance})"
msgstr ""

#: src/pages/public.jsx:92
msgid "Local timeline"
msgstr ""

#: src/pages/public.jsx:92
msgid "Federated timeline"
msgstr ""

#: src/pages/public.jsx:98
msgid "No one has posted anything yet."
msgstr ""

#: src/pages/public.jsx:125
msgid "Switch to Federated"
msgstr ""

#: src/pages/public.jsx:132
msgid "Switch to Local"
msgstr ""

#: src/pages/scheduled-posts.jsx:110
msgid "No scheduled posts."
msgstr ""

#. Scheduled [in 1 day] ([Thu, Feb 27, 6:30:00 PM])
#. placeholder {0}: niceDateTime(scheduledAt, { formatOpts: { weekday: 'short', second: 'numeric', }, })
#: src/pages/scheduled-posts.jsx:207
msgid "Scheduled <0><1/></0> <2>({0})</2>"
msgstr ""

#. Scheduled [in 1 day]
#: src/pages/scheduled-posts.jsx:263
msgid "Scheduled <0><1/></0>"
msgstr ""

#: src/pages/scheduled-posts.jsx:308
msgid "Scheduled post rescheduled"
msgstr ""

#: src/pages/scheduled-posts.jsx:315
msgid "Failed to reschedule post"
msgstr ""

#: src/pages/scheduled-posts.jsx:338
msgid "Reschedule"
msgstr ""

#: src/pages/scheduled-posts.jsx:344
msgid "Delete scheduled post?"
msgstr ""

#: src/pages/scheduled-posts.jsx:352
msgid "Scheduled post deleted"
msgstr ""

#: src/pages/scheduled-posts.jsx:359
msgid "Failed to delete scheduled post"
msgstr ""

#: src/pages/search.jsx:50
msgid "Search: {q} (Posts)"
msgstr ""

#: src/pages/search.jsx:53
msgid "Search: {q} (Accounts)"
msgstr ""

#: src/pages/search.jsx:56
msgid "Search: {q} (Hashtags)"
msgstr ""

#: src/pages/search.jsx:59
msgid "Search: {q}"
msgstr ""

#: src/pages/search.jsx:339
#: src/pages/search.jsx:421
msgid "Hashtags"
msgstr ""

#: src/pages/search.jsx:371
#: src/pages/search.jsx:425
#: src/pages/search.jsx:495
msgid "See more"
msgstr ""

#: src/pages/search.jsx:397
msgid "See more accounts"
msgstr ""

#: src/pages/search.jsx:411
msgid "No accounts found."
msgstr ""

#: src/pages/search.jsx:467
msgid "See more hashtags"
msgstr ""

#: src/pages/search.jsx:481
msgid "No hashtags found."
msgstr ""

#: src/pages/search.jsx:525
msgid "See more posts"
msgstr ""

#: src/pages/search.jsx:539
msgid "No posts found."
msgstr ""

#: src/pages/search.jsx:583
msgid "Enter your search term or paste a URL above to get started."
msgstr ""

#: src/pages/settings.jsx:95
msgid "Settings"
msgstr "設定"

#: src/pages/settings.jsx:104
msgid "Appearance"
msgstr "外観"

#: src/pages/settings.jsx:180
msgid "Light"
msgstr "ライト"

#: src/pages/settings.jsx:191
msgid "Dark"
msgstr "ダーク"

#: src/pages/settings.jsx:204
msgid "Auto"
msgstr "自動"

#: src/pages/settings.jsx:214
msgid "Text size"
msgstr "テキストサイズ"

#. Preview of one character, in smallest size
#. Preview of one character, in largest size
#: src/pages/settings.jsx:219
#: src/pages/settings.jsx:244
msgid "A"
msgstr ""

#: src/pages/settings.jsx:258
msgid "Display language"
msgstr "表示言語"

#: src/pages/settings.jsx:267
msgid "Volunteer translations"
msgstr "翻訳のボランティア"

#: src/pages/settings.jsx:278
msgid "Posting"
msgstr "投稿"

#: src/pages/settings.jsx:285
msgid "Default visibility"
msgstr "デフォルトの公開範囲"

#: src/pages/settings.jsx:286
#: src/pages/settings.jsx:332
msgid "Synced"
msgstr "同期済み"

#: src/pages/settings.jsx:311
msgid "Failed to update posting privacy"
msgstr "投稿のプライバシーを更新できませんでした"

#: src/pages/settings.jsx:334
msgid "Synced to your instance server's settings. <0>Go to your instance ({instance}) for more settings.</0>"
msgstr ""

#: src/pages/settings.jsx:349
msgid "Experiments"
msgstr "実験的機能"

#: src/pages/settings.jsx:362
msgid "Auto refresh timeline posts"
msgstr "タイムライン投稿を自動更新する"

#: src/pages/settings.jsx:374
msgid "Boosts carousel"
msgstr "ブーストのカルーセル表示"

#: src/pages/settings.jsx:391
msgid "Post translation"
msgstr "翻訳を投稿"

#: src/pages/settings.jsx:402
msgid "Translate to "
msgstr ""

#: src/pages/settings.jsx:413
msgid "System language ({systemTargetLanguageText})"
msgstr "システム言語 ({systemTargetLanguageText})"

#. placeholder {0}: snapStates.settings.contentTranslationHideLanguages .length
#: src/pages/settings.jsx:439
msgid "{0, plural, =0 {Hide \"Translate\" button for:} other {Hide \"Translate\" button for (#):}}"
msgstr ""

#: src/pages/settings.jsx:494
msgid "Note: This feature uses external translation services, powered by <0>{TRANSLATION_API_NAME}</0>."
msgstr ""

#: src/pages/settings.jsx:522
msgid "Auto inline translation"
msgstr "自動インライン翻訳"

#: src/pages/settings.jsx:526
msgid "Automatically show translation for posts in timeline. Only works for <0>short</0> posts without content warning, media and poll."
msgstr ""

#: src/pages/settings.jsx:547
msgid "GIF Picker for composer"
msgstr "投稿用の GIF ピッカー"

#: src/pages/settings.jsx:551
msgid "Note: This feature uses external GIF search service, powered by <0>GIPHY</0>. G-rated (suitable for viewing by all ages), tracking parameters are stripped, referrer information is omitted from requests, but search queries and IP address information will still reach their servers."
msgstr ""

#: src/pages/settings.jsx:580
msgid "Image description generator"
msgstr "画像説明の生成"

#: src/pages/settings.jsx:585
msgid "Only for new images while composing new posts."
msgstr ""

#: src/pages/settings.jsx:592
msgid "Note: This feature uses external AI service, powered by <0>img-alt-api</0>. May not work well. Only for images and in English."
msgstr ""

#: src/pages/settings.jsx:618
msgid "Server-side grouped notifications"
msgstr ""

#: src/pages/settings.jsx:622
msgid "Alpha-stage feature. Potentially improved grouping window but basic grouping logic."
msgstr ""

#: src/pages/settings.jsx:643
msgid "\"Cloud\" import/export for shortcuts settings"
msgstr ""

#: src/pages/settings.jsx:648
msgid "⚠️⚠️⚠️ Very experimental.<0/>Stored in your own profile’s notes. Profile (private) notes are mainly used for other profiles, and hidden for own profile."
msgstr ""

#: src/pages/settings.jsx:659
msgid "Note: This feature uses currently-logged-in instance server API."
msgstr ""

#: src/pages/settings.jsx:676
msgid "Cloak mode <0>(<1>Text</1> → <2>████</2>)</0>"
msgstr ""

#: src/pages/settings.jsx:685
msgid "Replace text as blocks, useful when taking screenshots, for privacy reasons."
msgstr ""

#: src/pages/settings.jsx:710
msgid "About"
msgstr ""

#: src/pages/settings.jsx:749
msgid "<0>Built</0> by <1>@cheeaun</1>"
msgstr ""

#: src/pages/settings.jsx:778
msgid "Sponsor"
msgstr ""

#: src/pages/settings.jsx:786
msgid "Donate"
msgstr ""

#: src/pages/settings.jsx:802
msgid "What's new"
msgstr ""

#: src/pages/settings.jsx:806
msgid "Privacy Policy"
msgstr "プライバシーポリシー"

#. placeholder {0}: WEBSITE.replace(/https?:\/\//g, '').replace(/\/$/, '')
#: src/pages/settings.jsx:813
msgid "<0>Site:</0> {0}"
msgstr ""

#. placeholder {0}: !__FAKE_COMMIT_HASH__ && ( <span class="ib insignificant"> ( <a href={`https://github.com/cheeaun/phanpy/commit/${__COMMIT_HASH__}`} target="_blank" rel="noopener" > <RelativeTime datetime={new Date(__BUILD_TIME__)} /> </a> ) </span> )
#: src/pages/settings.jsx:820
msgid "<0>Version:</0> <1/> {0}"
msgstr ""

#: src/pages/settings.jsx:835
msgid "Version string copied"
msgstr ""

#: src/pages/settings.jsx:838
msgid "Unable to copy version string"
msgstr ""

#: src/pages/settings.jsx:1103
#: src/pages/settings.jsx:1108
msgid "Failed to update subscription. Please try again."
msgstr ""

#: src/pages/settings.jsx:1114
msgid "Failed to remove subscription. Please try again."
msgstr ""

#: src/pages/settings.jsx:1121
msgid "Push Notifications (beta)"
msgstr ""

#: src/pages/settings.jsx:1143
msgid "Push notifications are blocked. Please enable them in your browser settings."
msgstr ""

#. placeholder {0}: [ { value: 'all', label: t`anyone`, }, { value: 'followed', label: t`people I follow`, }, { value: 'follower', label: t`followers`, }, ].map((type) => ( <option value={type.value}>{type.label}</option> ))
#: src/pages/settings.jsx:1152
msgid "Allow from <0>{0}</0>"
msgstr ""

#: src/pages/settings.jsx:1161
msgid "anyone"
msgstr "誰でも"

#: src/pages/settings.jsx:1165
msgid "people I follow"
msgstr "フォローしている人"

#: src/pages/settings.jsx:1169
msgid "followers"
msgstr "フォロワー"

#: src/pages/settings.jsx:1202
msgid "Follows"
msgstr "フォロー"

#: src/pages/settings.jsx:1210
msgid "Polls"
msgstr "投票"

#: src/pages/settings.jsx:1214
msgid "Post edits"
msgstr "投稿の編集"

#: src/pages/settings.jsx:1235
msgid "Push permission was not granted since your last login. You'll need to <0><1>log in</1> again to grant push permission</0>."
msgstr ""

#: src/pages/settings.jsx:1251
msgid "NOTE: Push notifications only work for <0>one account</0>."
msgstr ""

#. js-lingui-explicit-id
#: src/pages/status.jsx:657
#: src/pages/status.jsx:1220
msgid "post.title"
msgstr ""

#: src/pages/status.jsx:911
msgid "You're not logged in. Interactions (reply, boost, etc) are not possible."
msgstr ""

#: src/pages/status.jsx:931
msgid "This post is from another instance (<0>{instance}</0>). Interactions (reply, boost, etc) are not possible."
msgstr ""

#: src/pages/status.jsx:959
msgid "Error: {e}"
msgstr ""

#: src/pages/status.jsx:966
msgid "Switch to my instance to enable interactions"
msgstr ""

#: src/pages/status.jsx:1068
msgid "Unable to load replies."
msgstr ""

#: src/pages/status.jsx:1180
msgid "Back"
msgstr ""

#: src/pages/status.jsx:1211
msgid "Go to main post"
msgstr ""

#. placeholder {0}: ancestors.length
#: src/pages/status.jsx:1234
msgid "{0} posts above ‒ Go to top"
msgstr ""

#: src/pages/status.jsx:1281
#: src/pages/status.jsx:1344
msgid "Switch to Side Peek view"
msgstr ""

#: src/pages/status.jsx:1345
msgid "Switch to Full view"
msgstr ""

#: src/pages/status.jsx:1363
msgid "Show all sensitive content"
msgstr ""

#: src/pages/status.jsx:1368
msgid "Experimental"
msgstr ""

#: src/pages/status.jsx:1377
msgid "Unable to switch"
msgstr ""

#. placeholder {0}: punycode.toUnicode( postInstance, )
#: src/pages/status.jsx:1384
msgid "Switch to post's instance ({0})"
msgstr ""

#: src/pages/status.jsx:1387
msgid "Switch to post's instance"
msgstr ""

#: src/pages/status.jsx:1445
msgid "Unable to load post"
msgstr ""

#. placeholder {0}: replies.length
#. placeholder {1}: shortenNumber(replies.length)
#: src/pages/status.jsx:1581
msgid "{0, plural, one {# reply} other {<0>{1}</0> replies}}"
msgstr ""

#. placeholder {0}: shortenNumber(totalComments)
#: src/pages/status.jsx:1599
msgid "{totalComments, plural, one {# comment} other {<0>{0}</0> comments}}"
msgstr ""

#: src/pages/status.jsx:1621
msgid "View post with its replies"
msgstr ""

#: src/pages/trending.jsx:77
msgid "Trending ({instance})"
msgstr ""

#: src/pages/trending.jsx:235
msgid "Trending News"
msgstr ""

#. By [Author]
#. placeholder {0}: author ? ( <NameText account={author} showAvatar /> ) : authorUrl ? ( <a href={authorUrl} target="_blank" rel="noopener" > {authorName} </a> ) : ( authorName )
#: src/pages/trending.jsx:350
msgid "By {0}"
msgstr ""

#: src/pages/trending.jsx:411
msgid "Back to showing trending posts"
msgstr ""

#. placeholder {0}: currentLink .replace(/^https?:\/\/(www\.)?/i, '') .replace(/\/$/, '')
#: src/pages/trending.jsx:416
msgid "Showing posts mentioning <0>{0}</0>"
msgstr ""

#: src/pages/trending.jsx:428
msgid "Trending posts"
msgstr ""

#: src/pages/trending.jsx:451
msgid "No trending posts."
msgstr ""

#: src/pages/welcome.jsx:54
msgid "A minimalistic opinionated Mastodon web client."
msgstr "こだわりのあるミニマルな Mastodon Web クライアント"

#: src/pages/welcome.jsx:65
msgid "Log in with Mastodon"
msgstr "Mastodon にログイン"

#: src/pages/welcome.jsx:71
msgid "Sign up"
msgstr "登録"

#: src/pages/welcome.jsx:78
msgid "Connect your existing Mastodon/Fediverse account.<0/>Your credentials are not stored on this server."
msgstr "あなたの Mastodon/Fediverse アカウントに接続します。<0/>認証情報はこのサーバーに保存されません。"

#: src/pages/welcome.jsx:95
msgid "<0>Built</0> by <1>@cheeaun</1>. <2>Privacy Policy</2>."
msgstr "<0>開発者</0> <1>@cheeaun</1> <2>プライバシーポリシー</2>"

#: src/pages/welcome.jsx:126
msgid "Screenshot of Boosts Carousel"
msgstr "ブーストのスライド表示のスクリーンショット"

#: src/pages/welcome.jsx:130
msgid "Boosts Carousel"
msgstr "ブーストのスライド表示"

#: src/pages/welcome.jsx:133
msgid "Visually separate original posts and re-shared posts (boosted posts)."
msgstr "オリジナルのポストとブーストされたポストを見分けやすく表示します。"

#: src/pages/welcome.jsx:142
msgid "Screenshot of nested comments thread"
msgstr "コメントスレッドのスクリーンショット"

#: src/pages/welcome.jsx:146
msgid "Nested comments thread"
msgstr "コメントスレッド"

#: src/pages/welcome.jsx:149
msgid "Effortlessly follow conversations. Semi-collapsible replies."
msgstr "会話をわかりやすく追えます。また、返信を折りたたみ可能。"

#: src/pages/welcome.jsx:157
msgid "Screenshot of grouped notifications"
msgstr "通知のグルーピングのスクリーンショット"

#: src/pages/welcome.jsx:161
msgid "Grouped notifications"
msgstr "通知のグルーピング"

#: src/pages/welcome.jsx:164
msgid "Similar notifications are grouped and collapsed to reduce clutter."
msgstr "似たような通知はグループかされ、煩雑さが軽減します。"

#: src/pages/welcome.jsx:173
msgid "Screenshot of multi-column UI"
msgstr "マルチカラムUIのスクリーンショット"

#: src/pages/welcome.jsx:177
msgid "Single or multi-column"
msgstr "シングル or マルチカラム"

#: src/pages/welcome.jsx:180
msgid "By default, single column for zen-mode seekers. Configurable multi-column for power users."
msgstr "デフォルトではシングルモードですが、パワーユーザー向けにマルチカラムモードも設定できます。"

#: src/pages/welcome.jsx:189
msgid "Screenshot of multi-hashtag timeline with a form to add more hashtags"
msgstr "マルチタグ・タイムラインにハッシュタグを追加するスクリーンショット"

#: src/pages/welcome.jsx:193
msgid "Multi-hashtag timeline"
msgstr "マルチタグ・タイムライン"

#: src/pages/welcome.jsx:196
msgid "Up to 5 hashtags combined into a single timeline."
msgstr "最大5つのハッシュタグを1つのタイムラインでまとめて表示できます。"

#: src/utils/open-compose.js:24
msgid "Looks like your browser is blocking popups."
msgstr ""

#: src/utils/show-compose.js:16
msgid "A draft post is currently minimized. Post or discard it before creating a new one."
msgstr ""

#: src/utils/show-compose.js:21
msgid "A post is currently open. Post or discard it before creating a new one."
msgstr ""

