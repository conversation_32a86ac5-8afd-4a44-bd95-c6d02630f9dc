msgid ""
msgstr ""
"POT-Creation-Date: 2025-05-03 21:08+0800\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: fi\n"
"Project-Id-Version: phanpy\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-07-15 09:08\n"
"Last-Translator: \n"
"Language-Team: Finnish\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: phanpy\n"
"X-Crowdin-Project-ID: 703337\n"
"X-Crowdin-Language: fi\n"
"X-Crowdin-File: /main/src/locales/en.po\n"
"X-Crowdin-File-ID: 18\n"

#: src/components/account-block.jsx:137
msgid "Locked"
msgstr "Lukittu"

#. placeholder {0}: shortenNumber(statusesCount)
#: src/components/account-block.jsx:143
msgid "Posts: {0}"
msgstr "Julkaisut: {0}"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-block.jsx:148
msgid "Last posted: {0}"
msgstr "Julkaissut viimeksi: {0}"

#: src/components/account-block.jsx:163
#: src/components/account-info.jsx:710
msgid "Automated"
msgstr "Botti"

#: src/components/account-block.jsx:170
#: src/components/account-info.jsx:715
#: src/components/status.jsx:665
msgid "Group"
msgstr "Ryhmä"

#: src/components/account-block.jsx:180
msgid "Mutual"
msgstr "Seuraatte toisianne"

#: src/components/account-block.jsx:184
#: src/components/account-info.jsx:1875
msgid "Requested"
msgstr "Pyydetty"

#: src/components/account-block.jsx:188
#: src/components/account-info.jsx:1866
msgid "Following"
msgstr "Seurataan"

#: src/components/account-block.jsx:192
#: src/components/account-info.jsx:1180
msgid "Follows you"
msgstr "Seuraa sinua"

#: src/components/account-block.jsx:200
msgid "{followersCount, plural, one {# follower} other {# followers}}"
msgstr "{followersCount, plural, one {# seuraaja} other {# seuraajaa}}"

#: src/components/account-block.jsx:209
#: src/components/account-info.jsx:758
msgid "Verified"
msgstr "Vahvistettu"

#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#: src/components/account-block.jsx:224
#: src/components/account-info.jsx:860
msgid "Joined <0>{0}</0>"
msgstr "Liittynyt <0>{0}</0>"

#: src/components/account-info.jsx:63
msgid "Forever"
msgstr "Ikuisesti"

#: src/components/account-info.jsx:403
msgid "Unable to load account."
msgstr "Tiliä ei voitu ladata."

#: src/components/account-info.jsx:418
msgid "Go to account page"
msgstr "Siirry tilisivulle"

#: src/components/account-info.jsx:447
#: src/components/account-info.jsx:780
#: src/components/account-info.jsx:810
msgid "Followers"
msgstr "Seuraajat"

#. js-lingui-explicit-id
#: src/components/account-info.jsx:451
#: src/components/account-info.jsx:820
#: src/components/account-info.jsx:839
msgid "following.stats"
msgstr "Seurattavat"

#: src/components/account-info.jsx:454
#: src/components/account-info.jsx:856
#: src/pages/account-statuses.jsx:487
#: src/pages/search.jsx:344
#: src/pages/search.jsx:491
msgid "Posts"
msgstr "Julkaisut"

#: src/components/account-info.jsx:462
#: src/components/account-info.jsx:1236
#: src/components/compose.jsx:2792
#: src/components/media-alt-modal.jsx:55
#: src/components/media-modal.jsx:363
#: src/components/status.jsx:1915
#: src/components/status.jsx:1932
#: src/components/status.jsx:2057
#: src/components/status.jsx:2686
#: src/components/status.jsx:2689
#: src/pages/account-statuses.jsx:531
#: src/pages/accounts.jsx:118
#: src/pages/hashtag.jsx:203
#: src/pages/list.jsx:171
#: src/pages/public.jsx:116
#: src/pages/scheduled-posts.jsx:89
#: src/pages/status.jsx:1305
#: src/pages/trending.jsx:474
msgid "More"
msgstr "Lisää"

#: src/components/account-info.jsx:474
msgid "<0>{displayName}</0> has indicated that their new account is now:"
msgstr "<0>{displayName}</0> on ilmoittanut, että hänen uusi tilinsä on nyt:"

#: src/components/account-info.jsx:619
#: src/components/account-info.jsx:1464
msgid "Handle copied"
msgstr "Käyttäjätunnus kopioitu"

#: src/components/account-info.jsx:622
#: src/components/account-info.jsx:1467
msgid "Unable to copy handle"
msgstr "Käyttäjätunnusta ei voitu kopioida"

#: src/components/account-info.jsx:628
#: src/components/account-info.jsx:1473
msgid "Copy handle"
msgstr "Kopioi käyttäjätunnus"

#: src/components/account-info.jsx:634
msgid "Go to original profile page"
msgstr "Siirry alkuperäiselle profiilisivulle"

#: src/components/account-info.jsx:652
msgid "View profile image"
msgstr "Näytä profiilikuva"

#: src/components/account-info.jsx:670
msgid "View profile header"
msgstr "Näytä profiilin otsake"

#: src/components/account-info.jsx:686
#: src/components/account-info.jsx:1774
#: src/components/account-info.jsx:2300
msgid "Edit profile"
msgstr "Muokkaa profiilia"

#: src/components/account-info.jsx:705
msgid "In Memoriam"
msgstr "Muistoissamme"

#: src/components/account-info.jsx:787
#: src/components/account-info.jsx:830
msgid "This user has chosen to not make this information available."
msgstr "Käyttäjä on päättänyt pitää nämä tiedot yksityisinä."

#. placeholder {0}: ( postingStats.originals / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {1}: ( postingStats.replies / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {2}: ( postingStats.boosts / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#: src/components/account-info.jsx:885
msgid "{0} original posts, {1} replies, {2} boosts"
msgstr "{0} alkuperäistä julkaisua, {1} vastausta, {2} tehostusta"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.daysSinceLastPost
#. placeholder {2}: postingStats.daysSinceLastPost
#. placeholder {3}: postingStats.daysSinceLastPost
#. placeholder {4}: postingStats.total
#. placeholder {5}: postingStats.total
#. placeholder {6}: postingStats.daysSinceLastPost
#: src/components/account-info.jsx:901
msgid "{0, plural, one {{1, plural, one {Last 1 post in the past 1 day} other {Last 1 post in the past {2} days}}} other {{3, plural, one {Last {4} posts in the past 1 day} other {Last {5} posts in the past {6} days}}}}"
msgstr "{0, plural, one {{1, plural, one {Viimeisin julkaisu viime päivänä} other {Viimeisin julkaisu viimeisenä {2} päivänä}}} other {{3, plural, one {Viimeiset {4} julkaisua viime päivänä} other {Viimeiset {5} julkaisua viimeisenä {6} päivänä}}}}"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.total
#: src/components/account-info.jsx:917
msgid "{0, plural, one {Last 1 post in the past year(s)} other {Last {1} posts in the past year(s)}}"
msgstr "{0, plural, one {Viimeisen julkaisu viime vuosina} other {Viimeiset {1} julkaisua viime vuosina}}"

#: src/components/account-info.jsx:942
#: src/pages/catchup.jsx:70
msgid "Original"
msgstr "Alkuperäiset"

#: src/components/account-info.jsx:946
#: src/components/status.jsx:2470
#: src/pages/catchup.jsx:71
#: src/pages/catchup.jsx:1448
#: src/pages/catchup.jsx:2061
#: src/pages/status.jsx:1024
#: src/pages/status.jsx:1651
msgid "Replies"
msgstr "Vastaukset"

#: src/components/account-info.jsx:950
#: src/pages/catchup.jsx:72
#: src/pages/catchup.jsx:1450
#: src/pages/catchup.jsx:2073
#: src/pages/settings.jsx:1198
msgid "Boosts"
msgstr "Tehostukset"

#: src/components/account-info.jsx:956
msgid "Post stats unavailable."
msgstr "Julkaisutilastoja ei saatavilla."

#: src/components/account-info.jsx:987
msgid "View post stats"
msgstr "Näytä julkaisutilastot"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-info.jsx:1184
msgid "Last post: <0>{0}</0>"
msgstr "Edellinen julkaisu: <0>{0}</0>"

#: src/components/account-info.jsx:1198
msgid "Muted"
msgstr "Mykistetty"

#: src/components/account-info.jsx:1203
msgid "Blocked"
msgstr "Estetty"

#: src/components/account-info.jsx:1212
msgid "Private note"
msgstr "Yksityinen merkintä"

#: src/components/account-info.jsx:1269
msgid "Mention <0>@{username}</0>"
msgstr "Mainitse <0>@{username}</0>"

#: src/components/account-info.jsx:1281
msgid "Translate bio"
msgstr "Käännä elämäkerta"

#: src/components/account-info.jsx:1292
msgid "Edit private note"
msgstr "Muokkaa yksityistä merkintää"

#: src/components/account-info.jsx:1292
msgid "Add private note"
msgstr "Lisää yksityinen merkintä"

#: src/components/account-info.jsx:1312
msgid "Notifications enabled for @{username}'s posts."
msgstr "Ilmoitukset käyttäjän @{username} julkaisuista otettu käyttöön."

#: src/components/account-info.jsx:1313
msgid " Notifications disabled for @{username}'s posts."
msgstr " Ilmoitukset käyttäjän @{username} julkaisuista poistettu käytöstä."

#: src/components/account-info.jsx:1325
msgid "Disable notifications"
msgstr "Poista ilmoitukset käytöstä"

#: src/components/account-info.jsx:1326
msgid "Enable notifications"
msgstr "Ota ilmoitukset käyttöön"

#: src/components/account-info.jsx:1343
msgid "Boosts from @{username} enabled."
msgstr "Käyttäjän @{username} tehostukset otettu käyttöön."

#: src/components/account-info.jsx:1344
msgid "Boosts from @{username} disabled."
msgstr "Käyttäjän @{username} tehostukset poistettu käytöstä."

#: src/components/account-info.jsx:1355
msgid "Disable boosts"
msgstr "Poista tehostukset käytöstä"

#: src/components/account-info.jsx:1355
msgid "Enable boosts"
msgstr "Ota tehostukset käyttöön"

#: src/components/account-info.jsx:1374
msgid "@{username} is no longer featured on your profile."
msgstr "@{username} ei ole enää esille nostettuna profiilissasi."

#: src/components/account-info.jsx:1384
msgid "@{username} is now featured on your profile."
msgstr "@{username} on nyt nostettu esille profiilissasi."

#: src/components/account-info.jsx:1392
msgid "Unable to unfeature @{username} on your profile."
msgstr "Käyttäjää @{username} ei voitu poistaa esiltä profiilistasi."

#: src/components/account-info.jsx:1396
msgid "Unable to feature @{username} on your profile."
msgstr "Käyttäjää @{username} ei voitu nostaa esille profiilissasi."

#: src/components/account-info.jsx:1405
msgid "Don't feature on profile"
msgstr "Poista esiltä profiilista"

#: src/components/account-info.jsx:1406
#: src/pages/hashtag.jsx:333
msgid "Feature on profile"
msgstr "Nosta esille profiiliin"

#: src/components/account-info.jsx:1415
msgid "Show featured profiles"
msgstr "Näytä esille nostetut profiilit"

#: src/components/account-info.jsx:1430
#: src/components/account-info.jsx:1440
#: src/components/account-info.jsx:2046
msgid "Add/Remove from Lists"
msgstr "Lisää/poista listoista"

#: src/components/account-info.jsx:1490
#: src/components/status.jsx:1337
msgid "Link copied"
msgstr "Linkki kopioitu"

#: src/components/account-info.jsx:1493
#: src/components/status.jsx:1340
msgid "Unable to copy link"
msgstr "Linkkiä ei voitu kopioida"

#: src/components/account-info.jsx:1499
#: src/components/shortcuts-settings.jsx:1059
#: src/components/status.jsx:1346
#: src/components/status.jsx:3464
msgid "Copy"
msgstr "Kopioi"

#: src/components/account-info.jsx:1514
#: src/components/shortcuts-settings.jsx:1077
#: src/components/status.jsx:1362
msgid "Sharing doesn't seem to work."
msgstr "Jako ei näytä toimivan."

#: src/components/account-info.jsx:1520
#: src/components/status.jsx:1368
msgid "Share…"
msgstr "Jaa…"

#: src/components/account-info.jsx:1540
msgid "Unmuted @{username}"
msgstr "Kumottu käyttäjän @{username} mykistys"

#: src/components/account-info.jsx:1552
msgid "Unmute <0>@{username}</0>"
msgstr "Kumoa käyttäjän <0>@{username}</0> mykistys"

#: src/components/account-info.jsx:1568
msgid "Mute <0>@{username}</0>…"
msgstr "Mykistä <0>@{username}</0>…"

#. placeholder {0}: typeof MUTE_DURATIONS_LABELS[duration] === 'function' ? MUTE_DURATIONS_LABELS[duration]() : _(MUTE_DURATIONS_LABELS[duration])
#: src/components/account-info.jsx:1600
msgid "Muted @{username} for {0}"
msgstr "Mykistetty @{username}, kestona {0}"

#: src/components/account-info.jsx:1612
msgid "Unable to mute @{username}"
msgstr "Käyttäjää @{username} ei voitu mykistää"

#: src/components/account-info.jsx:1633
msgid "Remove <0>@{username}</0> from followers?"
msgstr "Poistetaanko <0>@{username}</0> seuraajista?"

#: src/components/account-info.jsx:1653
msgid "@{username} removed from followers"
msgstr "@{username} poistettu seuraajista"

#: src/components/account-info.jsx:1665
msgid "Remove follower…"
msgstr "Poista seuraaja…"

#: src/components/account-info.jsx:1676
msgid "Block <0>@{username}</0>?"
msgstr "Estetäänkö <0>@{username}</0>?"

#: src/components/account-info.jsx:1700
msgid "Unblocked @{username}"
msgstr "Kumottu käyttäjän @{username} esto"

#: src/components/account-info.jsx:1708
msgid "Blocked @{username}"
msgstr "Estetty @{username}"

#: src/components/account-info.jsx:1716
msgid "Unable to unblock @{username}"
msgstr "Käyttäjän @{username} mykistystä ei voitu kumota"

#: src/components/account-info.jsx:1718
msgid "Unable to block @{username}"
msgstr "Käyttäjää @{username} ei voitu estää"

#: src/components/account-info.jsx:1728
msgid "Unblock <0>@{username}</0>"
msgstr "Kumoa käyttäjän <0>@{username}</0> esto"

#: src/components/account-info.jsx:1737
msgid "Block <0>@{username}</0>…"
msgstr "Estä <0>@{username}</0>…"

#: src/components/account-info.jsx:1754
msgid "Report <0>@{username}</0>…"
msgstr "Raportoi <0>@{username}</0>…"

#: src/components/account-info.jsx:1810
msgid "Withdraw follow request?"
msgstr "Perutaanko seurauspyyntö?"

#. placeholder {0}: info.acct || info.username
#: src/components/account-info.jsx:1811
msgid "Unfollow @{0}?"
msgstr "Lopetetaanko käyttäjän @{0} seuraaminen?"

#: src/components/account-info.jsx:1869
msgid "Unfollow…"
msgstr "Älä seuraa…"

#: src/components/account-info.jsx:1878
msgid "Withdraw…"
msgstr "Peru…"

#: src/components/account-info.jsx:1885
#: src/components/account-info.jsx:1889
#: src/pages/hashtag.jsx:265
msgid "Follow"
msgstr "Seuraa"

#: src/components/account-info.jsx:1986
#: src/components/account-info.jsx:2041
#: src/components/account-info.jsx:2175
#: src/components/account-info.jsx:2295
#: src/components/account-sheet.jsx:38
#: src/components/compose.jsx:891
#: src/components/compose.jsx:2748
#: src/components/compose.jsx:3228
#: src/components/compose.jsx:3437
#: src/components/compose.jsx:3667
#: src/components/drafts.jsx:59
#: src/components/embed-modal.jsx:13
#: src/components/generic-accounts.jsx:151
#: src/components/keyboard-shortcuts-help.jsx:43
#: src/components/list-add-edit.jsx:37
#: src/components/media-alt-modal.jsx:43
#: src/components/media-modal.jsx:327
#: src/components/notification-service.jsx:157
#: src/components/report-modal.jsx:118
#: src/components/shortcuts-settings.jsx:230
#: src/components/shortcuts-settings.jsx:583
#: src/components/shortcuts-settings.jsx:783
#: src/components/status.jsx:3188
#: src/components/status.jsx:3428
#: src/components/status.jsx:3937
#: src/pages/accounts.jsx:45
#: src/pages/catchup.jsx:1584
#: src/pages/filters.jsx:225
#: src/pages/list.jsx:302
#: src/pages/notifications.jsx:942
#: src/pages/scheduled-posts.jsx:259
#: src/pages/settings.jsx:90
#: src/pages/status.jsx:1392
msgid "Close"
msgstr "Sulje"

#: src/components/account-info.jsx:1991
msgid "Translated Bio"
msgstr "Käännetty elämäkerta"

#: src/components/account-info.jsx:2086
msgid "Unable to remove from list."
msgstr "Listasta ei voitu poistaa."

#: src/components/account-info.jsx:2087
msgid "Unable to add to list."
msgstr "Listaan ei voitu lisätä."

#: src/components/account-info.jsx:2106
#: src/pages/lists.jsx:131
msgid "Unable to load lists."
msgstr "Listoja ei voitu ladata."

#: src/components/account-info.jsx:2110
msgid "No lists."
msgstr "Ei listoja."

#: src/components/account-info.jsx:2121
#: src/components/list-add-edit.jsx:41
#: src/pages/lists.jsx:62
msgid "New list"
msgstr "Uusi lista"

#. placeholder {0}: account?.username || account?.acct
#: src/components/account-info.jsx:2180
msgid "Private note about <0>@{0}</0>"
msgstr "Yksityinen merkintä käyttäjästä <0>@{0}</0>"

#: src/components/account-info.jsx:2210
msgid "Unable to update private note."
msgstr "Yksityistä merkintää ei voitu päivittää."

#: src/components/account-info.jsx:2233
#: src/components/account-info.jsx:2531
msgid "Cancel"
msgstr "Peruuta"

#: src/components/account-info.jsx:2238
msgid "Save & close"
msgstr "Tallenna ja sulje"

#: src/components/account-info.jsx:2355
msgid "Unable to update profile."
msgstr "Profiilia ei voitu päivittää."

#: src/components/account-info.jsx:2362
msgid "Header picture"
msgstr "Otsakekuva"

#: src/components/account-info.jsx:2414
msgid "Profile picture"
msgstr "Profiilikuva"

#: src/components/account-info.jsx:2466
#: src/components/list-add-edit.jsx:106
msgid "Name"
msgstr "Nimi"

#: src/components/account-info.jsx:2479
msgid "Bio"
msgstr "Elämäkerta"

#: src/components/account-info.jsx:2492
msgid "Extra fields"
msgstr "Lisäkentät"

#: src/components/account-info.jsx:2498
msgid "Label"
msgstr "Nimike"

#: src/components/account-info.jsx:2501
msgid "Content"
msgstr "Sisältö"

#: src/components/account-info.jsx:2534
#: src/components/list-add-edit.jsx:152
#: src/components/shortcuts-settings.jsx:715
#: src/pages/filters.jsx:570
#: src/pages/notifications.jsx:1008
msgid "Save"
msgstr "Tallenna"

#: src/components/account-info.jsx:2588
msgid "username"
msgstr "käyttäjänimi"

#: src/components/account-info.jsx:2592
msgid "server domain name"
msgstr "palvelimen verkkotunnus"

#. placeholder {0}: info.username
#: src/components/account-info.jsx:2658
msgid "Profiles featured by @{0}"
msgstr "Profiilit, jotka on nostanut esille @{0}"

#: src/components/account-info.jsx:2684
msgid "No featured profiles."
msgstr "Ei esille nostettuja profiileja."

#: src/components/background-service.jsx:158
msgid "Cloak mode disabled"
msgstr "Peittotila poistettu käytöstä"

#: src/components/background-service.jsx:158
msgid "Cloak mode enabled"
msgstr "Peittotila otettu käyttöön"

#: src/components/columns.jsx:27
#: src/components/nav-menu.jsx:181
#: src/components/shortcuts-settings.jsx:139
#: src/components/timeline.jsx:469
#: src/pages/catchup.jsx:883
#: src/pages/filters.jsx:90
#: src/pages/followed-hashtags.jsx:41
#: src/pages/home.jsx:54
#: src/pages/notifications.jsx:587
#: src/pages/scheduled-posts.jsx:74
msgid "Home"
msgstr "Koti"

#: src/components/compose-button.jsx:150
#: src/compose.jsx:38
msgid "Compose"
msgstr "Luo"

#: src/components/compose-button.jsx:177
#: src/components/nav-menu.jsx:265
#: src/pages/scheduled-posts.jsx:31
#: src/pages/scheduled-posts.jsx:78
msgid "Scheduled Posts"
msgstr "Ajastetut julkaisut"

#: src/components/compose-button.jsx:190
msgid "Add to thread"
msgstr "Lisää ketjuun"

#: src/components/compose.jsx:211
msgid "Take photo or video"
msgstr "Ota kuva tai video"

#: src/components/compose.jsx:212
msgid "Add media"
msgstr "Lisää mediaa"

#: src/components/compose.jsx:213
msgid "Add custom emoji"
msgstr "Lisää mukautettu emoji"

#: src/components/compose.jsx:214
msgid "Add GIF"
msgstr "Lisää GIF"

#: src/components/compose.jsx:215
msgid "Add poll"
msgstr "Lisää äänestys"

#: src/components/compose.jsx:216
msgid "Schedule post"
msgstr "Ajasta julkaisu"

#: src/components/compose.jsx:416
msgid "You have unsaved changes. Discard this post?"
msgstr "Sinulla on tallentamattomia muutoksia. Hylätäänkö julkaisu?"

#. placeholder {0}: unsupportedFiles.length
#. placeholder {1}: unsupportedFiles[0].name
#. placeholder {2}: lf.format( unsupportedFiles.map((f) => f.name), )
#: src/components/compose.jsx:654
msgid "{0, plural, one {File {1} is not supported.} other {Files {2} are not supported.}}"
msgstr "{0, plural, one {Tiedostoa {1} ei tueta.} other {Tiedostoja {2} ei tueta.}}"

#: src/components/compose.jsx:664
#: src/components/compose.jsx:682
#: src/components/compose.jsx:1794
#: src/components/compose.jsx:1919
msgid "{maxMediaAttachments, plural, one {You can only attach up to 1 file.} other {You can only attach up to # files.}}"
msgstr "{maxMediaAttachments, plural, one {Voit liittää enintään 1 tiedoston.} other {Voit liittää enintään # tiedostoa.}}"

#: src/components/compose.jsx:872
msgid "Pop out"
msgstr "Ponnauta ulos"

#: src/components/compose.jsx:879
msgid "Minimize"
msgstr "Pienennä"

#: src/components/compose.jsx:915
msgid "Looks like you closed the parent window."
msgstr "Näytät sulkeneesi pääikkunan."

#: src/components/compose.jsx:922
msgid "Looks like you already have a compose field open in the parent window and currently publishing. Please wait for it to be done and try again later."
msgstr "Luontikenttä näyttää olevan sinulla avoinna pääikkunassa ja julkaiseminen meneillään. Odota, että se on valmis, ja yritä myöhemmin uudelleen."

#: src/components/compose.jsx:927
msgid "Looks like you already have a compose field open in the parent window. Popping in this window will discard the changes you made in the parent window. Continue?"
msgstr "Luontikenttä näyttää olevan sinulla avoinna pääikkunassa. Tämän ikkunan sisään ponnauttaminen hävittää pääikkunassa tekemäsi muutokset. Jatketaanko?"

#: src/components/compose.jsx:970
msgid "Pop in"
msgstr "Ponnauta sisään"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#. placeholder {1}: rtf.format(-replyToStatusMonthsAgo, 'month')
#: src/components/compose.jsx:980
msgid "Replying to @{0}’s post (<0>{1}</0>)"
msgstr "Vastataan käyttäjän @{0} julkaisuun (<0>{1}</0>)"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#: src/components/compose.jsx:990
msgid "Replying to @{0}’s post"
msgstr "Vastataan käyttäjän @{0} julkaisuun"

#: src/components/compose.jsx:1003
msgid "Editing source post"
msgstr "Muokataan lähdejulkaisua"

#: src/components/compose.jsx:1056
msgid "Poll must have at least 2 options"
msgstr "Äänestyksessä pitää olla vähintään 2 vastausvaihtoehtoa"

#: src/components/compose.jsx:1060
msgid "Some poll choices are empty"
msgstr "Osa vaihtoehdoista on tyhjiä"

#: src/components/compose.jsx:1073
msgid "Some media have no descriptions. Continue?"
msgstr "Osalta mediasta puuttuu kuvaus. Jatketaanko?"

#: src/components/compose.jsx:1125
msgid "Attachment #{i} failed"
msgstr "Liite #{i} epäonnistui"

#: src/components/compose.jsx:1221
#: src/components/status.jsx:2245
#: src/components/timeline.jsx:1023
msgid "Content warning"
msgstr "Sisältövaroitus"

#: src/components/compose.jsx:1237
msgid "Content warning or sensitive media"
msgstr "Sisältövaroitus tai arkaluonteinen media"

#: src/components/compose.jsx:1273
#: src/components/status.jsx:99
#: src/pages/settings.jsx:318
msgid "Public"
msgstr "Julkinen"

#: src/components/compose.jsx:1278
#: src/components/nav-menu.jsx:349
#: src/components/shortcuts-settings.jsx:165
#: src/components/status.jsx:100
msgid "Local"
msgstr "Paikallinen"

#: src/components/compose.jsx:1282
#: src/components/status.jsx:101
#: src/pages/settings.jsx:321
msgid "Unlisted"
msgstr "Listaamaton"

#: src/components/compose.jsx:1285
#: src/components/status.jsx:102
#: src/pages/settings.jsx:324
msgid "Followers only"
msgstr "Vain seuraajat"

#: src/components/compose.jsx:1288
#: src/components/status.jsx:103
#: src/components/status.jsx:2121
msgid "Private mention"
msgstr "Yksityismaininta"

#: src/components/compose.jsx:1297
msgid "Post your reply"
msgstr "Julkaise vastauksesi"

#: src/components/compose.jsx:1299
msgid "Edit your post"
msgstr "Muokkaa julkaisuasi"

#: src/components/compose.jsx:1300
msgid "What are you doing?"
msgstr "Mitä teet?"

#: src/components/compose.jsx:1379
msgid "Mark media as sensitive"
msgstr "Merkitse media arkaluonteiseksi"

#: src/components/compose.jsx:1416
msgid "Posting on <0/>"
msgstr "Julkaistaan <0/>"

#: src/components/compose.jsx:1447
#: src/components/compose.jsx:3286
#: src/components/shortcuts-settings.jsx:715
#: src/pages/list.jsx:388
msgid "Add"
msgstr "Lisää"

#: src/components/compose.jsx:1675
msgid "Schedule"
msgstr "Ajasta"

#: src/components/compose.jsx:1677
#: src/components/keyboard-shortcuts-help.jsx:155
#: src/components/status.jsx:1109
#: src/components/status.jsx:1895
#: src/components/status.jsx:1896
#: src/components/status.jsx:2590
msgid "Reply"
msgstr "Vastaa"

#: src/components/compose.jsx:1679
msgid "Update"
msgstr "Päivitä"

#: src/components/compose.jsx:1680
msgctxt "Submit button in composer"
msgid "Post"
msgstr "Julkaise"

#: src/components/compose.jsx:1806
msgid "Downloading GIF…"
msgstr "Ladataan GIF-kuvaa…"

#: src/components/compose.jsx:1834
msgid "Failed to download GIF"
msgstr "GIF-kuvan lataus epäonnistui"

#: src/components/compose.jsx:2049
#: src/components/compose.jsx:2126
#: src/components/nav-menu.jsx:244
msgid "More…"
msgstr "Lisää…"

#: src/components/compose.jsx:2562
msgid "Uploaded"
msgstr "Ladattu"

#: src/components/compose.jsx:2575
msgid "Image description"
msgstr "Kuvan kuvaus"

#: src/components/compose.jsx:2576
msgid "Video description"
msgstr "Videon kuvaus"

#: src/components/compose.jsx:2577
msgid "Audio description"
msgstr "Äänen kuvaus"

#. placeholder {0}: prettyBytes( imageSize, )
#. placeholder {0}: prettyBytes( videoSize, )
#. placeholder {1}: prettyBytes(imageSizeLimit)
#. placeholder {1}: prettyBytes(videoSizeLimit)
#: src/components/compose.jsx:2612
#: src/components/compose.jsx:2632
msgid "File size too large. Uploading might encounter issues. Try reduce the file size from {0} to {1} or lower."
msgstr "Liian suuri tiedostokoko. Palveluun lataaminen saattaa aiheuttaa ongelmia. Kokeile pienentää tiedostoa koosta {0} kokoon {1} tai pienemmäksi."

#. placeholder {0}: i18n.number( width, )
#. placeholder {1}: i18n.number(height)
#. placeholder {2}: i18n.number(newWidth)
#. placeholder {3}: i18n.number( newHeight, )
#: src/components/compose.jsx:2624
#: src/components/compose.jsx:2644
msgid "Dimension too large. Uploading might encounter issues. Try reduce dimension from {0}×{1}px to {2}×{3}px."
msgstr "Liian suuret mitat. Palveluun lataaminen saattaa aiheuttaa ongelmia. Kokeile pienentää kuvaa mitoista {0}×{1} px mittoihin {2}×{3} px."

#: src/components/compose.jsx:2652
msgid "Frame rate too high. Uploading might encounter issues."
msgstr "Liian suuri kuvataajuus. Palveluun lataaminen saattaa aiheuttaa ongelmia."

#: src/components/compose.jsx:2712
#: src/components/compose.jsx:2962
#: src/components/shortcuts-settings.jsx:726
#: src/pages/catchup.jsx:1081
#: src/pages/filters.jsx:413
msgid "Remove"
msgstr "Poista"

#: src/components/compose.jsx:2729
#: src/compose.jsx:84
msgid "Error"
msgstr "Virhe"

#: src/components/compose.jsx:2754
msgid "Edit image description"
msgstr "Muokkaa kuvan kuvausta"

#: src/components/compose.jsx:2755
msgid "Edit video description"
msgstr "Muokkaa videon kuvausta"

#: src/components/compose.jsx:2756
msgid "Edit audio description"
msgstr "Muokkaa äänen kuvausta"

#: src/components/compose.jsx:2801
#: src/components/compose.jsx:2850
msgid "Generating description. Please wait…"
msgstr "Luodaan kuvausta. Odota hetki…"

#. placeholder {0}: e.message
#: src/components/compose.jsx:2821
msgid "Failed to generate description: {0}"
msgstr "Kuvauksen luonti epäonnistui: {0}"

#: src/components/compose.jsx:2822
msgid "Failed to generate description"
msgstr "Kuvauksen luonti epäonnistui"

#: src/components/compose.jsx:2834
#: src/components/compose.jsx:2840
#: src/components/compose.jsx:2886
msgid "Generate description…"
msgstr "Luo kuvaus…"

#. placeholder {0}: e?.message ? `: ${e.message}` : ''
#: src/components/compose.jsx:2873
msgid "Failed to generate description{0}"
msgstr "Kuvauksen luonti epäonnistui{0}"

#. placeholder {0}: localeCode2Text(lang)
#: src/components/compose.jsx:2888
msgid "({0}) <0>— experimental</0>"
msgstr "({0}) <0>— kokeellinen</0>"

#: src/components/compose.jsx:2907
msgid "Done"
msgstr "Valmis"

#. placeholder {0}: i + 1
#: src/components/compose.jsx:2943
msgid "Choice {0}"
msgstr "Vaihtoehto {0}"

#: src/components/compose.jsx:2990
msgid "Multiple choices"
msgstr "Monivalinta"

#: src/components/compose.jsx:2993
msgid "Duration"
msgstr "Kesto"

#: src/components/compose.jsx:3024
msgid "Remove poll"
msgstr "Poista äänestys"

#: src/components/compose.jsx:3245
msgid "Search accounts"
msgstr "Hae tilejä"

#: src/components/compose.jsx:3299
#: src/components/generic-accounts.jsx:236
msgid "Error loading accounts"
msgstr "Virhe ladattaessa tilejä"

#: src/components/compose.jsx:3443
msgid "Custom emojis"
msgstr "Mukautetut emojit"

#: src/components/compose.jsx:3463
msgid "Search emoji"
msgstr "Hae emojeita"

#: src/components/compose.jsx:3494
msgid "Error loading custom emojis"
msgstr "Virhe ladattaessa mukautettuja emojeita"

#: src/components/compose.jsx:3505
msgid "Recently used"
msgstr "Viimeaikaiset"

#: src/components/compose.jsx:3506
msgid "Others"
msgstr "Muut"

#. placeholder {0}: i18n.number(emojis.length - max)
#: src/components/compose.jsx:3544
msgid "{0} more…"
msgstr "{0} lisää…"

#: src/components/compose.jsx:3682
msgid "Search GIFs"
msgstr "Hae GIF-kuvia"

#: src/components/compose.jsx:3697
msgid "Powered by GIPHY"
msgstr "Palvelun tarjoaa GIPHY"

#: src/components/compose.jsx:3705
msgid "Type to search GIFs"
msgstr "Hae GIF-kuvia kirjoittamalla"

#: src/components/compose.jsx:3803
#: src/components/media-modal.jsx:469
#: src/components/timeline.jsx:927
msgid "Previous"
msgstr "Edellinen"

#: src/components/compose.jsx:3821
#: src/components/media-modal.jsx:488
#: src/components/timeline.jsx:944
msgid "Next"
msgstr "Seuraava"

#: src/components/compose.jsx:3838
msgid "Error loading GIFs"
msgstr "Virhe ladattaessa GIF-kuvia"

#: src/components/drafts.jsx:64
#: src/pages/settings.jsx:702
msgid "Unsent drafts"
msgstr "Lähettämättömät luonnokset"

#: src/components/drafts.jsx:69
msgid "Looks like you have unsent drafts. Let's continue where you left off."
msgstr "Sinulla näyttää olevan lähettämättömiä luonnoksia. Jatketaan siitä, mihin jäit."

#: src/components/drafts.jsx:103
msgid "Delete this draft?"
msgstr "Poistetaanko tämä luonnos?"

#: src/components/drafts.jsx:118
msgid "Error deleting draft! Please try again."
msgstr "Virhe poistettaessa luonnosta! Yritä uudelleen."

#: src/components/drafts.jsx:128
#: src/components/list-add-edit.jsx:188
#: src/components/status.jsx:1512
#: src/pages/filters.jsx:603
#: src/pages/scheduled-posts.jsx:369
msgid "Delete…"
msgstr "Poista…"

#: src/components/drafts.jsx:147
msgid "Error fetching reply-to status!"
msgstr "Virhe haettaessa vastauksellisuuden tilaa!"

#: src/components/drafts.jsx:172
msgid "Delete all drafts?"
msgstr "Poistetaanko kaikki luonnokset?"

#: src/components/drafts.jsx:190
msgid "Error deleting drafts! Please try again."
msgstr "Virhe poistettaessa luonnoksia! Yritä uudelleen."

#: src/components/drafts.jsx:202
msgid "Delete all…"
msgstr "Poista kaikki…"

#: src/components/drafts.jsx:210
msgid "No drafts found."
msgstr "Luonnoksia ei löytynyt."

#: src/components/drafts.jsx:247
#: src/pages/catchup.jsx:1932
msgid "Poll"
msgstr "Äänestys"

#: src/components/drafts.jsx:250
#: src/pages/account-statuses.jsx:367
msgid "Media"
msgstr "Media"

#: src/components/embed-modal.jsx:18
msgid "Open in new window"
msgstr "Avaa uuteen ikkunaan"

#: src/components/follow-request-buttons.jsx:43
#: src/pages/notifications.jsx:992
msgid "Accept"
msgstr "Hyväksy"

#: src/components/follow-request-buttons.jsx:69
msgid "Reject"
msgstr "Hylkää"

#: src/components/follow-request-buttons.jsx:76
#: src/pages/notifications.jsx:1276
msgid "Accepted"
msgstr "Hyväksytty"

#: src/components/follow-request-buttons.jsx:80
msgid "Rejected"
msgstr "Hylätty"

#: src/components/generic-accounts.jsx:154
#: src/components/notification.jsx:455
#: src/pages/accounts.jsx:50
#: src/pages/search.jsx:334
#: src/pages/search.jsx:367
msgid "Accounts"
msgstr "Tilit"

#: src/components/generic-accounts.jsx:214
#: src/components/timeline.jsx:551
#: src/pages/list.jsx:321
#: src/pages/notifications.jsx:922
#: src/pages/search.jsx:561
#: src/pages/status.jsx:1425
msgid "Show more…"
msgstr "Näytä lisää…"

#: src/components/generic-accounts.jsx:219
#: src/components/timeline.jsx:556
#: src/pages/search.jsx:566
msgid "The end."
msgstr "Loppu."

#: src/components/generic-accounts.jsx:240
msgid "Nothing to show"
msgstr "Ei mitään näytettävää"

#: src/components/keyboard-shortcuts-help.jsx:47
#: src/components/nav-menu.jsx:368
#: src/pages/catchup.jsx:1622
msgid "Keyboard shortcuts"
msgstr "Pikanäppäimet"

#: src/components/keyboard-shortcuts-help.jsx:55
msgid "Keyboard shortcuts help"
msgstr "Pikanäppäinten ohje"

#: src/components/keyboard-shortcuts-help.jsx:59
#: src/pages/catchup.jsx:1647
msgid "Next post"
msgstr "Seuraava julkaisu"

#: src/components/keyboard-shortcuts-help.jsx:63
#: src/pages/catchup.jsx:1655
msgid "Previous post"
msgstr "Edellinen julkaisu"

#: src/components/keyboard-shortcuts-help.jsx:67
msgid "Skip carousel to next post"
msgstr "Siirrä karuselli seuraavaan julkaisuun"

#: src/components/keyboard-shortcuts-help.jsx:69
msgid "<0>Shift</0> + <1>j</1>"
msgstr "<0>Vaihto</0> + <1>j</1>"

#: src/components/keyboard-shortcuts-help.jsx:75
msgid "Skip carousel to previous post"
msgstr "Siirrä karuselli edelliseen julkaisuun"

#: src/components/keyboard-shortcuts-help.jsx:77
msgid "<0>Shift</0> + <1>k</1>"
msgstr "<0>Vaihto</0> + <1>k</1>"

#: src/components/keyboard-shortcuts-help.jsx:83
msgid "Load new posts"
msgstr "Lataa lisää julkaisuja"

#: src/components/keyboard-shortcuts-help.jsx:87
#: src/pages/catchup.jsx:1679
msgid "Open post details"
msgstr "Avaa julkaisun lisätiedot"

#: src/components/keyboard-shortcuts-help.jsx:89
msgid "<0>Enter</0> or <1>o</1>"
msgstr "<0>Enter</0> tai <1>o</1>"

#: src/components/keyboard-shortcuts-help.jsx:96
msgid "Expand content warning or<0/>toggle expanded/collapsed thread"
msgstr "Laajenna sisältövaroitus tai<0/>laajenna/supista keskusteluketju"

#: src/components/keyboard-shortcuts-help.jsx:105
msgid "Close post or dialogs"
msgstr "Sulje julkaisu tai valintaikkunat"

#: src/components/keyboard-shortcuts-help.jsx:107
msgid "<0>Esc</0> or <1>Backspace</1>"
msgstr "<0>Esc</0> tai <1>askelpalautin</1>"

#: src/components/keyboard-shortcuts-help.jsx:113
msgid "Focus column in multi-column mode"
msgstr "Kohdista sarakkeeseen usean sarakkeen tilassa"

#: src/components/keyboard-shortcuts-help.jsx:115
msgid "<0>1</0> to <1>9</1>"
msgstr "<0>1</0>–<1>9</1>"

#: src/components/keyboard-shortcuts-help.jsx:121
msgid "Focus next column in multi-column mode"
msgstr "Kohdista edelliseen sarakkeeseen usean sarakkeen tilassa"

#: src/components/keyboard-shortcuts-help.jsx:125
msgid "Focus previous column in multi-column mode"
msgstr "Kohdista seuraavaan sarakkeeseen usean sarakkeen tilassa"

#: src/components/keyboard-shortcuts-help.jsx:129
msgid "Compose new post"
msgstr "Luo uusi julkaisu"

#: src/components/keyboard-shortcuts-help.jsx:133
msgid "Compose new post (new window)"
msgstr "Luo uusi julkaisu (uusi ikkuna)"

#: src/components/keyboard-shortcuts-help.jsx:136
msgid "<0>Shift</0> + <1>c</1>"
msgstr "<0>Vaihto</0> + <1>c</1>"

#: src/components/keyboard-shortcuts-help.jsx:142
msgid "Send post"
msgstr "Lähetä julkaisu"

#: src/components/keyboard-shortcuts-help.jsx:144
msgid "<0>Ctrl</0> + <1>Enter</1> or <2>⌘</2> + <3>Enter</3>"
msgstr "<0>Ctrl</0> + <1>Enter</1> tai <2>⌘</2> + <3>Enter</3>"

#: src/components/keyboard-shortcuts-help.jsx:151
#: src/components/nav-menu.jsx:337
#: src/components/search-form.jsx:73
#: src/components/shortcuts-settings.jsx:52
#: src/components/shortcuts-settings.jsx:179
#: src/pages/search.jsx:46
#: src/pages/search.jsx:316
msgid "Search"
msgstr "Haku"

#: src/components/keyboard-shortcuts-help.jsx:159
msgid "Reply (new window)"
msgstr "Vastaa (uusi ikkuna)"

#: src/components/keyboard-shortcuts-help.jsx:162
msgid "<0>Shift</0> + <1>r</1>"
msgstr "<0>Vaihto</0> + <1>r</1>"

#: src/components/keyboard-shortcuts-help.jsx:168
msgid "Like (favourite)"
msgstr "Tykkää (lisää suosikkeihin)"

#: src/components/keyboard-shortcuts-help.jsx:170
msgid "<0>l</0> or <1>f</1>"
msgstr "<0>l</0> tai <1>f</1>"

#: src/components/keyboard-shortcuts-help.jsx:176
#: src/components/status.jsx:1117
#: src/components/status.jsx:2617
#: src/components/status.jsx:2640
#: src/components/status.jsx:2641
msgid "Boost"
msgstr "Tehosta"

#: src/components/keyboard-shortcuts-help.jsx:178
msgid "<0>Shift</0> + <1>b</1>"
msgstr "<0>Vaihto</0> + <1>b</1>"

#: src/components/keyboard-shortcuts-help.jsx:184
#: src/components/status.jsx:1180
#: src/components/status.jsx:2665
#: src/components/status.jsx:2666
msgid "Bookmark"
msgstr "Lisää kirjanmerkkeihin"

#: src/components/keyboard-shortcuts-help.jsx:188
msgid "Toggle Cloak mode"
msgstr "Ota peittotila käyttöön tai pois käytöstä"

#: src/components/keyboard-shortcuts-help.jsx:190
msgid "<0>Shift</0> + <1>Alt</1> + <2>k</2>"
msgstr "<0>Vaihto</0> + <1>Alt</1> + <2>k</2>"

#: src/components/list-add-edit.jsx:41
msgid "Edit list"
msgstr "Muokkaa listaa"

#: src/components/list-add-edit.jsx:97
msgid "Unable to edit list."
msgstr "Listaa ei voitu muokata."

#: src/components/list-add-edit.jsx:98
msgid "Unable to create list."
msgstr "Listaa ei voitu luoda."

#: src/components/list-add-edit.jsx:126
msgid "Show replies to list members"
msgstr "Näytä vastaukset listan jäsenille"

#: src/components/list-add-edit.jsx:129
msgid "Show replies to people I follow"
msgstr "Näytä vastaukset seuraamilleni käyttäjille"

#: src/components/list-add-edit.jsx:132
msgid "Don't show replies"
msgstr "Älä näytä vastauksia"

#: src/components/list-add-edit.jsx:146
msgid "Hide posts on this list from Home/Following"
msgstr "Piilota tämän listan julkaisut Koti- ja Seurattavat-näkymistä"

#: src/components/list-add-edit.jsx:152
#: src/pages/filters.jsx:570
msgid "Create"
msgstr "Luo"

#: src/components/list-add-edit.jsx:159
msgid "Delete this list?"
msgstr "Poistetaanko tämä lista?"

#: src/components/list-add-edit.jsx:178
msgid "Unable to delete list."
msgstr "Listaa ei voitu poistaa."

#: src/components/list-exclusive-badge.jsx:14
#: src/pages/list.jsx:179
#: src/pages/lists.jsx:107
msgid "Posts on this list are hidden from Home/Following"
msgstr "Tämän listan julkaisut on piilotettu Koti- ja Seurattavat-näkymistä"

#: src/components/media-alt-modal.jsx:48
#: src/components/media.jsx:51
msgid "Media description"
msgstr "Median kuvaus"

#: src/components/media-alt-modal.jsx:67
#: src/components/status.jsx:1223
#: src/components/status.jsx:1232
#: src/components/translation-block.jsx:239
msgid "Translate"
msgstr "Käännä"

#: src/components/media-alt-modal.jsx:78
#: src/components/status.jsx:1251
msgid "Speak"
msgstr "Puhu"

#: src/components/media-modal.jsx:374
msgid "Open original media in new window"
msgstr "Avaa alkuperäinen media uuteen ikkunaan"

#: src/components/media-modal.jsx:378
msgid "Open original media"
msgstr "Avaa alkuperäinen media"

#: src/components/media-modal.jsx:394
msgid "Attempting to describe image. Please wait…"
msgstr "Koetetaan kuvailla kuvaa. Odota hetki…"

#: src/components/media-modal.jsx:409
msgid "Failed to describe image"
msgstr "Kuvan kuvailu epäonnistui"

#: src/components/media-modal.jsx:419
msgid "Describe image…"
msgstr "Kuvaile kuvaa…"

#: src/components/media-modal.jsx:443
msgid "View post"
msgstr "Näytä julkaisu"

#: src/components/media-post.jsx:127
msgid "Sensitive media"
msgstr "Arkaluonteinen media"

#: src/components/media-post.jsx:132
msgid "Filtered: {filterTitleStr}"
msgstr "Suodatettu: {filterTitleStr}"

#: src/components/media-post.jsx:133
#: src/components/status.jsx:3767
#: src/components/status.jsx:3863
#: src/components/status.jsx:3941
#: src/components/timeline.jsx:1012
#: src/pages/catchup.jsx:75
#: src/pages/catchup.jsx:1880
msgid "Filtered"
msgstr "Suodatettu"

#: src/components/media.jsx:477
msgid "Open file"
msgstr "Avaa tiedosto"

#: src/components/modals.jsx:75
msgid "Post scheduled"
msgstr "Julkaisu ajastettu"

#: src/components/modals.jsx:76
msgid "Post published. Check it out."
msgstr "Julkaisu lähetetty. Tarkista se."

#: src/components/modals.jsx:78
msgid "Reply scheduled"
msgstr "Vastaus ajastettu"

#: src/components/modals.jsx:79
msgid "Reply posted. Check it out."
msgstr "Vastaus julkaistu. Tarkista se."

#: src/components/modals.jsx:80
msgid "Post updated. Check it out."
msgstr "Julkaisu päivitetty. Tarkista se."

#: src/components/nav-menu.jsx:123
msgid "Menu"
msgstr "Valikko"

#: src/components/nav-menu.jsx:159
msgid "Reload page now to update?"
msgstr "Päivitetäänkö lataamalla sivu uudelleen nyt?"

#: src/components/nav-menu.jsx:171
msgid "New update available…"
msgstr "Uusi päivitys saatavilla…"

#. js-lingui-explicit-id
#: src/components/nav-menu.jsx:190
#: src/components/shortcuts-settings.jsx:140
#: src/pages/following.jsx:23
#: src/pages/following.jsx:145
msgid "following.title"
msgstr "Seurattavat"

#: src/components/nav-menu.jsx:197
#: src/pages/catchup.jsx:878
msgid "Catch-up"
msgstr "Kiinnikuronta"

#: src/components/nav-menu.jsx:204
#: src/components/shortcuts-settings.jsx:58
#: src/components/shortcuts-settings.jsx:146
#: src/pages/home.jsx:240
#: src/pages/mentions.jsx:21
#: src/pages/mentions.jsx:174
#: src/pages/settings.jsx:1190
#: src/pages/trending.jsx:384
msgid "Mentions"
msgstr "Maininnat"

#: src/components/nav-menu.jsx:211
#: src/components/shortcuts-settings.jsx:49
#: src/components/shortcuts-settings.jsx:152
#: src/pages/filters.jsx:24
#: src/pages/home.jsx:86
#: src/pages/home.jsx:198
#: src/pages/notifications.jsx:117
#: src/pages/notifications.jsx:591
msgid "Notifications"
msgstr "Ilmoitukset"

#: src/components/nav-menu.jsx:214
msgid "New"
msgstr "Uusi"

#: src/components/nav-menu.jsx:225
msgid "Profile"
msgstr "Profiili"

#: src/components/nav-menu.jsx:233
#: src/components/shortcuts-settings.jsx:54
#: src/components/shortcuts-settings.jsx:195
#: src/pages/bookmarks.jsx:12
#: src/pages/bookmarks.jsx:26
msgid "Bookmarks"
msgstr "Kirjanmerkit"

#: src/components/nav-menu.jsx:253
#: src/components/shortcuts-settings.jsx:55
#: src/components/shortcuts-settings.jsx:201
#: src/pages/catchup.jsx:1449
#: src/pages/catchup.jsx:2067
#: src/pages/favourites.jsx:12
#: src/pages/favourites.jsx:26
#: src/pages/settings.jsx:1194
msgid "Likes"
msgstr "Tykkäykset"

#: src/components/nav-menu.jsx:259
#: src/pages/followed-hashtags.jsx:15
#: src/pages/followed-hashtags.jsx:45
msgid "Followed Hashtags"
msgstr "Seurattavat aihetunnisteet"

#: src/components/nav-menu.jsx:273
#: src/pages/account-statuses.jsx:333
#: src/pages/filters.jsx:55
#: src/pages/filters.jsx:94
#: src/pages/hashtag.jsx:344
msgid "Filters"
msgstr "Suodattimet"

#: src/components/nav-menu.jsx:281
msgid "Muted users"
msgstr "Mykistetyt käyttäjät"

#: src/components/nav-menu.jsx:289
msgid "Muted users…"
msgstr "Mykistetyt käyttäjät…"

#: src/components/nav-menu.jsx:296
msgid "Blocked users"
msgstr "Estetyt käyttäjät"

#: src/components/nav-menu.jsx:304
msgid "Blocked users…"
msgstr "Estetyt käyttäjät…"

#: src/components/nav-menu.jsx:316
msgid "Accounts…"
msgstr "Tilit…"

#: src/components/nav-menu.jsx:326
#: src/pages/login.jsx:32
#: src/pages/login.jsx:199
#: src/pages/status.jsx:924
#: src/pages/welcome.jsx:65
msgid "Log in"
msgstr "Kirjaudu sisään"

#: src/components/nav-menu.jsx:343
#: src/components/shortcuts-settings.jsx:57
#: src/components/shortcuts-settings.jsx:172
#: src/pages/trending.jsx:444
msgid "Trending"
msgstr "Suositut"

#: src/components/nav-menu.jsx:355
#: src/components/shortcuts-settings.jsx:165
msgid "Federated"
msgstr "Federoitu"

#: src/components/nav-menu.jsx:378
msgid "Shortcuts / Columns…"
msgstr "Pikavalinnat / Sarakkeet…"

#: src/components/nav-menu.jsx:388
#: src/components/nav-menu.jsx:402
msgid "Settings…"
msgstr "Asetukset…"

#: src/components/nav-menu.jsx:432
#: src/components/nav-menu.jsx:467
#: src/components/shortcuts-settings.jsx:50
#: src/components/shortcuts-settings.jsx:158
#: src/pages/list.jsx:127
#: src/pages/lists.jsx:18
#: src/pages/lists.jsx:54
msgid "Lists"
msgstr "Listat"

#: src/components/nav-menu.jsx:440
#: src/components/shortcuts.jsx:241
#: src/pages/list.jsx:139
msgid "All Lists"
msgstr "Kaikki listat"

#: src/components/notification-service.jsx:161
msgid "Notification"
msgstr "Ilmoitus"

#: src/components/notification-service.jsx:167
msgid "This notification is from your other account."
msgstr "Tämä ilmoitus on toiselta tililtäsi."

#: src/components/notification-service.jsx:196
msgid "View all notifications"
msgstr "Näytä kaikki ilmoitukset"

#: src/components/notification.jsx:71
msgid "{account} reacted to your post with {emojiObject}"
msgstr "{account} reagoi julkaisuusi emojilla {emojiObject}"

#: src/components/notification.jsx:78
msgid "{account} published a post."
msgstr "{account} julkaisi."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:86
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted your reply.} other {{account} boosted your post.}}} other {{account} boosted {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted your reply.} other {<2><3>{1}</3> people</2> boosted your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} tehosti vastaustasi.} other {{account} tehosti julkaisuasi.}}} other {{account} tehosti {postsCount} julkaisuasi.}}} other {{postType, select, reply {<0><1>{0}</1> käyttäjää</0> tehosti vastaustasi.} other {<2><3>{1}</3> käyttäjää</2> tehosti julkaisuasi.}}}}"

#. placeholder {0}: shortenNumber(count)
#: src/components/notification.jsx:129
msgid "{count, plural, =1 {{account} followed you.} other {<0><1>{0}</1> people</0> followed you.}}"
msgstr "{count, plural, one {}=1 {{account} seurasi sinua.} other {<0><1>{0}</1> käyttäjää</0> seurasi sinua.}}"

#: src/components/notification.jsx:143
msgid "{account} requested to follow you."
msgstr "{account} pyysi saada seurata sinua."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:152
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} liked your reply.} other {{account} liked your post.}}} other {{account} liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> liked your reply.} other {<2><3>{1}</3> people</2> liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} tykkäsi vastauksestasi.} other {{account} tykkäsi julkaisustasi.}}} other {{account} tykkäsi {postsCount} julkaisustasi.}}} other {{postType, select, reply {<0><1>{0}</1> käyttäjää</0> tykkäsi vastauksestasi.} other {<2><3>{1}</3> käyttäjää</2> tykkäsi julkaisustasi.}}}}"

#: src/components/notification.jsx:194
msgid "A poll you have voted in or created has ended."
msgstr "Äänestys, johon olet osallistunut tai jonka olet luonut, on päättynyt."

#: src/components/notification.jsx:195
msgid "A poll you have created has ended."
msgstr "Äänestys, jonka olet luonut, on päättynyt."

#: src/components/notification.jsx:196
msgid "A poll you have voted in has ended."
msgstr "Äänestys, johon olet osallistunut, on päättynyt."

#: src/components/notification.jsx:197
msgid "A post you interacted with has been edited."
msgstr "Julkaisua, jonka kanssa olet ollut vuorovaikutuksessa, on päivitetty."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:205
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted & liked your reply.} other {{account} boosted & liked your post.}}} other {{account} boosted & liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted & liked your reply.} other {<2><3>{1}</3> people</2> boosted & liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} tehosti ja tykkäsi vastauksestasi.} other {{account} tehosti ja tykkäsi julkaisustasi.}}} other {{account} tehosti ja tykkäsi {postsCount} julkaisustasi.}}} other {{postType, select, reply {<0><1>{0}</1> käyttäjää</0> tehosti ja tykkäsi vastauksestasi.} other {<2><3>{1}</3> käyttäjää</2> tehosti ja tykkäsi julkaisustasi.}}}}"

#: src/components/notification.jsx:247
msgid "{account} signed up."
msgstr "{account} rekisteröityi."

#: src/components/notification.jsx:249
msgid "{account} reported {targetAccount}"
msgstr "{account} raportoi käyttäjän {targetAccount}"

#: src/components/notification.jsx:254
msgid "Lost connections with <0>{name}</0>."
msgstr "Menetettiin yhteydet kohteeseen <0>{name}</0>."

#: src/components/notification.jsx:260
msgid "Moderation warning"
msgstr "Moderointivaroitus"

#: src/components/notification.jsx:265
msgid "Your {year} #Wrapstodon is here!"
msgstr "Vuotesi {year} #Wrapstodon on täällä!"

#: src/components/notification.jsx:271
msgid "An admin from <0>{from}</0> has suspended <1>{targetName}</1>, which means you can no longer receive updates from them or interact with them."
msgstr "Palvelimen <0>{from}</0> ylläpitäjä on jäädyttänyt käyttäjän <1>{targetName}</1>, minkä takia et saa enää hänen päivityksiään etkä voi olla vuorovaikutuksessa hänen kanssaan."

#: src/components/notification.jsx:277
msgid "An admin from <0>{from}</0> has blocked <1>{targetName}</1>. Affected followers: {followersCount}, followings: {followingCount}."
msgstr "Palvelimen <0>{from}</0> ylläpitäjä on estänyt palvelimen <1>{targetName}</1>. Vaikutettuja seuraajia {followersCount}, seurattavia {followingCount}."

#: src/components/notification.jsx:283
msgid "You have blocked <0>{targetName}</0>. Removed followers: {followersCount}, followings: {followingCount}."
msgstr "Olet estänyt palvelimen <0>{targetName}</0>. Poistettuja seuraajia {followersCount}, seurattavia {followingCount}."

#: src/components/notification.jsx:291
msgid "Your account has received a moderation warning."
msgstr "Tilisi on saanut moderointivaroituksen."

#: src/components/notification.jsx:292
msgid "Your account has been disabled."
msgstr "Tilisi on poistettu käytöstä."

#: src/components/notification.jsx:293
msgid "Some of your posts have been marked as sensitive."
msgstr "Julkaisujasi on merkitty arkaluonteisiksi."

#: src/components/notification.jsx:294
msgid "Some of your posts have been deleted."
msgstr "Julkaisujasi on poistettu."

#: src/components/notification.jsx:295
msgid "Your posts will be marked as sensitive from now on."
msgstr "Tästä lähtien julkaisusi merkitään arkaluonteisiksi."

#: src/components/notification.jsx:296
msgid "Your account has been limited."
msgstr "Tiliäsi on rajoitettu."

#: src/components/notification.jsx:297
msgid "Your account has been suspended."
msgstr "Tilisi on jäädytetty."

#: src/components/notification.jsx:374
msgid "[Unknown notification type: {type}]"
msgstr "[Tuntematon ilmoitustyyppi: {type}]"

#: src/components/notification.jsx:451
#: src/components/status.jsx:1194
#: src/components/status.jsx:1204
msgid "Boosted/Liked by…"
msgstr "Tehostaneet/tykänneet…"

#: src/components/notification.jsx:452
msgid "Liked by…"
msgstr "Tykänneet…"

#: src/components/notification.jsx:453
msgid "Boosted by…"
msgstr "Tehostanut…"

#: src/components/notification.jsx:454
msgid "Followed by…"
msgstr "Seurannut…"

#: src/components/notification.jsx:535
#: src/components/notification.jsx:551
msgid "Learn more <0/>"
msgstr "Lue lisää <0/>"

#: src/components/notification.jsx:560
msgid "View #Wrapstodon"
msgstr "Näytä #Wrapstodon"

#: src/components/notification.jsx:801
#: src/components/status.jsx:403
msgid "Read more →"
msgstr "Lue lisää →"

#: src/components/poll.jsx:113
msgid "Voted"
msgstr "Äänestetty"

#: src/components/poll.jsx:119
msgid "{optionVotesCount, plural, one {# vote} other {# votes}}"
msgstr "{optionVotesCount, plural, one {# ääni} other {# ääntä}}"

#: src/components/poll.jsx:139
#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Hide results"
msgstr "Piilota tulokset"

#: src/components/poll.jsx:188
msgid "Vote"
msgstr "Äänestä"

#: src/components/poll.jsx:208
#: src/components/poll.jsx:210
#: src/pages/scheduled-posts.jsx:100
#: src/pages/status.jsx:1294
#: src/pages/status.jsx:1317
msgid "Refresh"
msgstr "Päivitä"

#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Show results"
msgstr "Näytä tulokset"

#. placeholder {0}: shortenNumber(votesCount)
#. placeholder {1}: shortenNumber(votesCount)
#: src/components/poll.jsx:231
msgid "{votesCount, plural, one {<0>{0}</0> vote} other {<1>{1}</1> votes}}"
msgstr "{votesCount, plural, one {<0>{0}</0> ääni} other {<1>{1}</1> ääntä}}"

#. placeholder {0}: shortenNumber(votersCount)
#. placeholder {1}: shortenNumber(votersCount)
#: src/components/poll.jsx:248
msgid "{votersCount, plural, one {<0>{0}</0> voter} other {<1>{1}</1> voters}}"
msgstr "{votersCount, plural, one {<0>{0}</0> äänestäjä} other {<1>{1}</1> äänestäjää}}"

#: src/components/poll.jsx:268
msgid "Ended <0/>"
msgstr "Päättynyt <0/>"

#: src/components/poll.jsx:272
msgid "Ended"
msgstr "Päättynyt"

#: src/components/poll.jsx:275
msgid "Ending <0/>"
msgstr "Päättyy <0/>"

#: src/components/poll.jsx:279
msgid "Ending"
msgstr "Päättyy"

#. Relative time in seconds, as short as possible
#. placeholder {0}: seconds < 1 ? 1 : Math.floor(seconds)
#: src/components/relative-time.jsx:61
msgid "{0}s"
msgstr "{0} s"

#. Relative time in minutes, as short as possible
#. placeholder {0}: Math.floor(seconds / minute)
#: src/components/relative-time.jsx:66
msgid "{0}m"
msgstr "{0} min"

#. Relative time in hours, as short as possible
#. placeholder {0}: Math.floor(seconds / hour)
#: src/components/relative-time.jsx:71
msgid "{0}h"
msgstr "{0} t"

#: src/components/report-modal.jsx:30
msgid "Spam"
msgstr "Roskaposti"

#: src/components/report-modal.jsx:31
msgid "Malicious links, fake engagement, or repetitive replies"
msgstr "Haitalliset linkit, valeaktiivisuus tai toisteiset vastaukset"

#: src/components/report-modal.jsx:34
msgid "Illegal"
msgstr "Laittomuus"

#: src/components/report-modal.jsx:35
msgid "Violates the law of your or the server's country"
msgstr "Rikkoo maasi tai palvelimen sijaintimaan lakia"

#: src/components/report-modal.jsx:38
msgid "Server rule violation"
msgstr "Palvelimen sääntöjen rikkomus"

#: src/components/report-modal.jsx:39
msgid "Breaks specific server rules"
msgstr "Rikkoo tietyn palvelimen sääntöjä"

#: src/components/report-modal.jsx:40
msgid "Violation"
msgstr "Rikkomus"

#: src/components/report-modal.jsx:43
msgid "Other"
msgstr "Muu"

#: src/components/report-modal.jsx:44
msgid "Issue doesn't fit other categories"
msgstr "Ongelma ei sovi muihin luokkiin"

#: src/components/report-modal.jsx:111
msgid "Report Post"
msgstr "Raportoi julkaisu"

#: src/components/report-modal.jsx:111
msgid "Report @{username}"
msgstr "Raportoi @{username}"

#: src/components/report-modal.jsx:147
msgid "Pending review"
msgstr "Odottaa arviota"

#: src/components/report-modal.jsx:189
msgid "Post reported"
msgstr "Julkaisu raportoitu"

#: src/components/report-modal.jsx:189
msgid "Profile reported"
msgstr "Profiili raportoitu"

#: src/components/report-modal.jsx:197
msgid "Unable to report post"
msgstr "Julkaisua ei voitu raportoida"

#: src/components/report-modal.jsx:198
msgid "Unable to report profile"
msgstr "Profiilia ei voitu raportoida"

#: src/components/report-modal.jsx:206
msgid "What's the issue with this post?"
msgstr "Mikä ongelma on tässä julkaisussa?"

#: src/components/report-modal.jsx:207
msgid "What's the issue with this profile?"
msgstr "Mikä ongelma on tässä profiilissa?"

#: src/components/report-modal.jsx:276
msgid "Additional info"
msgstr "Lisätiedot"

#: src/components/report-modal.jsx:299
msgid "Forward to <0>{domain}</0>"
msgstr "Välitä palvelimelle <0>{domain}</0>"

#: src/components/report-modal.jsx:309
msgid "Send Report"
msgstr "Lähetä raportti"

#: src/components/report-modal.jsx:318
msgid "Muted {username}"
msgstr "Mykistetty {username}"

#: src/components/report-modal.jsx:321
msgid "Unable to mute {username}"
msgstr "Käyttäjää {username} ei voitu mykistää"

#: src/components/report-modal.jsx:326
msgid "Send Report <0>+ Mute profile</0>"
msgstr "Lähetä raportti <0>+ mykistä profiili</0>"

#: src/components/report-modal.jsx:337
msgid "Blocked {username}"
msgstr "Estetty {username}"

#: src/components/report-modal.jsx:340
msgid "Unable to block {username}"
msgstr "Käyttäjää {username} ei voitu estää"

#: src/components/report-modal.jsx:345
msgid "Send Report <0>+ Block profile</0>"
msgstr "Lähetä raportti <0>+ estä profiili</0>"

#: src/components/search-form.jsx:203
msgid "{query} <0>‒ accounts, hashtags & posts</0>"
msgstr "{query} <0>‒ tilit, aihetunnisteet ja julkaisut</0>"

#: src/components/search-form.jsx:216
msgid "Posts with <0>{query}</0>"
msgstr "Julkaisut haulla <0>{query}</0>"

#. placeholder {0}: query.replace(/^#/, '')
#: src/components/search-form.jsx:228
msgid "Posts tagged with <0>#{0}</0>"
msgstr "Julkaisut tunnisteella <0>#{0}</0>"

#: src/components/search-form.jsx:242
msgid "Look up <0>{query}</0>"
msgstr "Hae <0>{query}</0>"

#: src/components/search-form.jsx:253
msgid "Accounts with <0>{query}</0>"
msgstr "Tilit haulla <0>{query}</0>"

#: src/components/shortcuts-settings.jsx:48
msgid "Home / Following"
msgstr "Koti / Seurattavat"

#: src/components/shortcuts-settings.jsx:51
msgid "Public (Local / Federated)"
msgstr "Julkinen (paikallinen / federoitu)"

#: src/components/shortcuts-settings.jsx:53
msgid "Account"
msgstr "Tili"

#: src/components/shortcuts-settings.jsx:56
msgid "Hashtag"
msgstr "Aihetunniste"

#: src/components/shortcuts-settings.jsx:63
msgid "List ID"
msgstr "Listan tunnus"

#: src/components/shortcuts-settings.jsx:70
msgid "Local only"
msgstr "Vain paikalliset"

#: src/components/shortcuts-settings.jsx:75
#: src/components/shortcuts-settings.jsx:84
#: src/components/shortcuts-settings.jsx:122
#: src/pages/login.jsx:203
msgid "Instance"
msgstr "Instanssi"

#: src/components/shortcuts-settings.jsx:78
#: src/components/shortcuts-settings.jsx:87
#: src/components/shortcuts-settings.jsx:125
msgid "Optional, e.g. mastodon.social"
msgstr "Valinnainen, esim. mastodon.social"

#: src/components/shortcuts-settings.jsx:93
msgid "Search term"
msgstr "Hakutermi"

#: src/components/shortcuts-settings.jsx:96
msgid "Optional, unless for multi-column mode"
msgstr "Valinnainen, paitsi usean sarakkeen tilassa"

#: src/components/shortcuts-settings.jsx:113
msgid "e.g. PixelArt (Max 5, space-separated)"
msgstr "esim. PixelArt (enintään 5, välilyönnein eroteltuina)"

#: src/components/shortcuts-settings.jsx:117
#: src/pages/hashtag.jsx:360
msgid "Media only"
msgstr "Vain media"

#: src/components/shortcuts-settings.jsx:235
#: src/components/shortcuts.jsx:218
msgid "Shortcuts"
msgstr "Pikavalinnat"

#: src/components/shortcuts-settings.jsx:243
msgid "beta"
msgstr "beeta"

#: src/components/shortcuts-settings.jsx:249
msgid "Specify a list of shortcuts that'll appear as:"
msgstr "Määritä luettelo pikavalintoja, jotka näkyvät seuraavasti:"

#: src/components/shortcuts-settings.jsx:255
msgid "Floating button"
msgstr "Kelluva painike"

#: src/components/shortcuts-settings.jsx:260
msgid "Tab/Menu bar"
msgstr "Välilehti-/valikkopalkki"

#: src/components/shortcuts-settings.jsx:265
msgid "Multi-column"
msgstr "Useampi sarake"

#: src/components/shortcuts-settings.jsx:332
msgid "Not available in current view mode"
msgstr "Ei saatavilla nykyisessä näkymätilassa"

#: src/components/shortcuts-settings.jsx:351
#: src/pages/accounts.jsx:188
msgid "Move up"
msgstr "Siirrä ylöspäin"

#: src/components/shortcuts-settings.jsx:367
#: src/pages/accounts.jsx:203
msgid "Move down"
msgstr "Siirrä alaspäin"

#: src/components/shortcuts-settings.jsx:379
#: src/components/status.jsx:1474
#: src/pages/list.jsx:195
msgid "Edit"
msgstr "Muokkaa"

#: src/components/shortcuts-settings.jsx:400
msgid "Add more than one shortcut/column to make this work."
msgstr "Lisää useampi pikavalinta/sarake, jotta tämä toimisi."

#: src/components/shortcuts-settings.jsx:411
msgid "No columns yet. Tap on the Add column button."
msgstr "Ei vielä sarakkeita. Napauta Lisää sarake -painiketta."

#: src/components/shortcuts-settings.jsx:412
msgid "No shortcuts yet. Tap on the Add shortcut button."
msgstr "Ei vielä pikavalintoja. Napauta Lisää pikavalinta -painiketta."

#: src/components/shortcuts-settings.jsx:415
msgid "Not sure what to add?<0/>Try adding <1>Home / Following and Notifications</1> first."
msgstr "Etkö ole varma, mitä lisätä?<0/>Kokeile lisätä ensin <1>Koti / Seurattavat tai Ilmoitukset</1>."

#: src/components/shortcuts-settings.jsx:443
msgid "Max {SHORTCUTS_LIMIT} columns"
msgstr "Enintään {SHORTCUTS_LIMIT} saraketta"

#: src/components/shortcuts-settings.jsx:444
msgid "Max {SHORTCUTS_LIMIT} shortcuts"
msgstr "Enintään {SHORTCUTS_LIMIT} pikavalintaa"

#: src/components/shortcuts-settings.jsx:458
msgid "Import/export"
msgstr "Tuo/vie"

#: src/components/shortcuts-settings.jsx:468
msgid "Add column…"
msgstr "Lisää sarake…"

#: src/components/shortcuts-settings.jsx:469
msgid "Add shortcut…"
msgstr "Lisää pikavalinta…"

#: src/components/shortcuts-settings.jsx:516
msgid "Specific list is optional. For multi-column mode, list is required, else the column will not be shown."
msgstr "Tietty lista on valinnainen. Usean sarakkeen tilassa lista tarvitaan, tai saraketta ei näytetä."

#: src/components/shortcuts-settings.jsx:517
msgid "For multi-column mode, search term is required, else the column will not be shown."
msgstr "Usean sarakkeen tilassa tarvitaan hakutermi, tai muuten saraketta ei näytetä."

#: src/components/shortcuts-settings.jsx:518
msgid "Multiple hashtags are supported. Space-separated."
msgstr "Useaa aihetunnistetta tuetaan. Erottele välilyönnein."

#: src/components/shortcuts-settings.jsx:587
msgid "Edit shortcut"
msgstr "Muokkaa pikavalintaa"

#: src/components/shortcuts-settings.jsx:587
msgid "Add shortcut"
msgstr "Lisää pikavalinta"

#: src/components/shortcuts-settings.jsx:623
msgid "Timeline"
msgstr "Aikajana"

#: src/components/shortcuts-settings.jsx:649
msgid "List"
msgstr "Lista"

#: src/components/shortcuts-settings.jsx:788
msgid "Import/Export <0>Shortcuts</0>"
msgstr "Tuo/vie <0>pikavalinnat</0>"

#: src/components/shortcuts-settings.jsx:798
msgid "Import"
msgstr "Tuo"

#: src/components/shortcuts-settings.jsx:806
msgid "Paste shortcuts here"
msgstr "Liitä pikavalinnat tähän"

#: src/components/shortcuts-settings.jsx:822
msgid "Downloading saved shortcuts from instance server…"
msgstr "Ladataan tallennetut pikavalinnat instanssipalvelimelta…"

#: src/components/shortcuts-settings.jsx:851
msgid "Unable to download shortcuts"
msgstr "Pikavalintoja ei voitu ladata"

#: src/components/shortcuts-settings.jsx:854
msgid "Download shortcuts from instance server"
msgstr "Lataa pikavalinnat instanssipalvelimelta"

#: src/components/shortcuts-settings.jsx:912
msgid "* Exists in current shortcuts"
msgstr "* Kuuluu nykyisiin pikavalintoihin"

#: src/components/shortcuts-settings.jsx:917
msgid "List may not work if it's from a different account."
msgstr "Lista ei välttämättä toimi, jos se on eri tililtä."

#: src/components/shortcuts-settings.jsx:927
msgid "Invalid settings format"
msgstr "Virheellinen asetusformaatti"

#: src/components/shortcuts-settings.jsx:935
msgid "Append to current shortcuts?"
msgstr "Lisätäänkö nykyisiin pikavalintoihin?"

#: src/components/shortcuts-settings.jsx:938
msgid "Only shortcuts that don’t exist in current shortcuts will be appended."
msgstr "Lisätään vain pikavalinnat, joita ei ole nykyisissä pikavalinnoissa."

#: src/components/shortcuts-settings.jsx:960
msgid "No new shortcuts to import"
msgstr "Ei uusia pikavalintoja tuotavaksi"

#: src/components/shortcuts-settings.jsx:975
msgid "Shortcuts imported. Exceeded max {SHORTCUTS_LIMIT}, so the rest are not imported."
msgstr "Pikavalinnat tuotu. Enimmäismäärä {SHORTCUTS_LIMIT} ylittyi, joten loppuja ei tuotu."

#: src/components/shortcuts-settings.jsx:976
#: src/components/shortcuts-settings.jsx:1000
msgid "Shortcuts imported"
msgstr "Pikavalinnat tuotu"

#: src/components/shortcuts-settings.jsx:986
msgid "Import & append…"
msgstr "Tuo ja lisää…"

#: src/components/shortcuts-settings.jsx:994
msgid "Override current shortcuts?"
msgstr "Korvataanko nykyiset pikavalinnat?"

#: src/components/shortcuts-settings.jsx:995
msgid "Import shortcuts?"
msgstr "Tuodaanko pikavalinnat?"

#: src/components/shortcuts-settings.jsx:1009
msgid "or override…"
msgstr "tai korvaa…"

#: src/components/shortcuts-settings.jsx:1009
msgid "Import…"
msgstr "Tuo…"

#: src/components/shortcuts-settings.jsx:1018
msgid "Export"
msgstr "Vie"

#: src/components/shortcuts-settings.jsx:1033
msgid "Shortcuts copied"
msgstr "Pikavalinnat kopioitu"

#: src/components/shortcuts-settings.jsx:1036
msgid "Unable to copy shortcuts"
msgstr "Pikavalintoja ei voitu kopioida"

#: src/components/shortcuts-settings.jsx:1050
msgid "Shortcut settings copied"
msgstr "Pikavalintojen asetukset kopioitu"

#: src/components/shortcuts-settings.jsx:1053
msgid "Unable to copy shortcut settings"
msgstr "Pikavalintojen asetuksia ei voitu kopioida"

#: src/components/shortcuts-settings.jsx:1083
msgid "Share"
msgstr "Jaa"

#: src/components/shortcuts-settings.jsx:1122
msgid "Saving shortcuts to instance server…"
msgstr "Tallennetaan pikavalinnat instanssipalvelimelle…"

#: src/components/shortcuts-settings.jsx:1129
msgid "Shortcuts saved"
msgstr "Pikavalinnat tallennettu"

#: src/components/shortcuts-settings.jsx:1134
msgid "Unable to save shortcuts"
msgstr "Pikavalintoja ei voitu tallentaa"

#: src/components/shortcuts-settings.jsx:1137
msgid "Sync to instance server"
msgstr "Synkronoi instanssipalvelimelle"

#. placeholder {0}: shortcutsStr.length
#: src/components/shortcuts-settings.jsx:1145
msgid "{0, plural, one {# character} other {# characters}}"
msgstr "{0, plural, one {# merkki} other {# merkkiä}}"

#: src/components/shortcuts-settings.jsx:1157
msgid "Raw Shortcuts JSON"
msgstr "Pikavalintojen raaka-JSON"

#: src/components/shortcuts-settings.jsx:1170
msgid "Import/export settings from/to instance server (Very experimental)"
msgstr "Tuo/vie asetukset instanssipalvelimelta/-palvelimelle (erittäin kokeellinen)"

#: src/components/status.jsx:277
msgid "Unable to format math"
msgstr ""

#: src/components/status.jsx:292
msgid "Math expressions found."
msgstr ""

#: src/components/status.jsx:294
msgid "Show markup"
msgstr ""

#: src/components/status.jsx:294
msgid "Format math"
msgstr ""

#: src/components/status.jsx:689
msgid "<0/> <1>boosted</1>"
msgstr "<0/> <1>tehosti</1>"

#: src/components/status.jsx:792
msgid "Sorry, your current logged-in instance can't interact with this post from another instance."
msgstr "Valitettavasti parhaillaan kirjautuneena oleva instanssi ei voi olla vuorovaikutuksessa tämän toiselta instanssilta peräisin olevan julkaisun kanssa."

#. placeholder {0}: username || acct
#: src/components/status.jsx:946
msgid "Unliked @{0}'s post"
msgstr "Kumottu käyttäjän @{0} julkaisun tykkäys"

#. placeholder {0}: username || acct
#: src/components/status.jsx:947
msgid "Liked @{0}'s post"
msgstr "Tykätty käyttäjän @{0} julkaisusta"

#. placeholder {0}: username || acct
#: src/components/status.jsx:986
msgid "Unbookmarked @{0}'s post"
msgstr "Poistettu käyttäjän @{0} julkaisu kirjanmerkeistä"

#. placeholder {0}: username || acct
#: src/components/status.jsx:987
msgid "Bookmarked @{0}'s post"
msgstr "Lisätty käyttäjän @{0} julkaisu kirjanmerkkeihin"

#: src/components/status.jsx:1086
msgid "Some media have no descriptions."
msgstr "Osalta mediasta puuttuu kuvaus."

#. placeholder {0}: rtf.format(-statusMonthsAgo, 'month')
#: src/components/status.jsx:1093
msgid "Old post (<0>{0}</0>)"
msgstr "Vanha julkaisu (<0>{0}</0>)"

#: src/components/status.jsx:1117
#: src/components/status.jsx:1157
#: src/components/status.jsx:2617
#: src/components/status.jsx:2640
msgid "Unboost"
msgstr "Kumoa tehostus"

#: src/components/status.jsx:1133
#: src/components/status.jsx:2632
msgid "Quote"
msgstr "Lainaa"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1145
#: src/components/status.jsx:1611
msgid "Unboosted @{0}'s post"
msgstr "Kumottu käyttäjän @{0} julkaisun tehostus"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1146
#: src/components/status.jsx:1612
msgid "Boosted @{0}'s post"
msgstr "Tehostettu käyttäjän @{0} julkaisua"

#: src/components/status.jsx:1158
msgid "Boost…"
msgstr "Tehosta…"

#: src/components/status.jsx:1170
#: src/components/status.jsx:1905
#: src/components/status.jsx:2653
msgid "Unlike"
msgstr "Kumoa tykkäys"

#: src/components/status.jsx:1171
#: src/components/status.jsx:1905
#: src/components/status.jsx:1906
#: src/components/status.jsx:2653
#: src/components/status.jsx:2654
msgid "Like"
msgstr "Tykkää"

#: src/components/status.jsx:1180
#: src/components/status.jsx:2665
msgid "Unbookmark"
msgstr "Poista kirjanmerkeistä"

#: src/components/status.jsx:1263
msgid "Post text copied"
msgstr "Julkaisun teksti kopioitu"

#: src/components/status.jsx:1266
msgid "Unable to copy post text"
msgstr "Julkaisun tekstiä ei voitu kopioida"

#: src/components/status.jsx:1272
msgid "Copy post text"
msgstr "Kopioi julkaisun teksti"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1290
msgid "View post by <0>@{0}</0>"
msgstr "Näytä käyttäjän <0>@{0}</0> julkaisu"

#: src/components/status.jsx:1311
msgid "Show Edit History"
msgstr "Näytä muokkaushistoria"

#: src/components/status.jsx:1314
msgid "Edited: {editedDateText}"
msgstr "Muokattu: {editedDateText}"

#: src/components/status.jsx:1381
#: src/components/status.jsx:3433
msgid "Embed post"
msgstr "Upota julkaisu"

#: src/components/status.jsx:1395
msgid "Conversation unmuted"
msgstr "Keskustelun mykistys kumottu"

#: src/components/status.jsx:1395
msgid "Conversation muted"
msgstr "Keskustelu mykistetty"

#: src/components/status.jsx:1401
msgid "Unable to unmute conversation"
msgstr "Keskustelun mykistystä ei voitu kumota"

#: src/components/status.jsx:1402
msgid "Unable to mute conversation"
msgstr "Keskustelua ei voitu mykistää"

#: src/components/status.jsx:1411
msgid "Unmute conversation"
msgstr "Kumoa keskustelun mykistys"

#: src/components/status.jsx:1418
msgid "Mute conversation"
msgstr "Mykistä keskustelu"

#: src/components/status.jsx:1434
msgid "Post unpinned from profile"
msgstr "Julkaisu irrotettu profiilista"

#: src/components/status.jsx:1435
msgid "Post pinned to profile"
msgstr "Julkaisu kiinnitetty profiiliin"

#: src/components/status.jsx:1440
msgid "Unable to unpin post"
msgstr "Julkaisua ei voitu irrottaa"

#: src/components/status.jsx:1440
msgid "Unable to pin post"
msgstr "Julkaisua ei voitu kiinnittää"

#: src/components/status.jsx:1449
msgid "Unpin from profile"
msgstr "Irrota profiilista"

#: src/components/status.jsx:1456
msgid "Pin to profile"
msgstr "Kiinnitä profiiliin"

#: src/components/status.jsx:1485
msgid "Delete this post?"
msgstr "Poistetaanko tämä julkaisu?"

#: src/components/status.jsx:1501
msgid "Post deleted"
msgstr "Julkaisu poistettu"

#: src/components/status.jsx:1504
msgid "Unable to delete post"
msgstr "Julkaisua ei voitu poistaa"

#: src/components/status.jsx:1532
msgid "Report post…"
msgstr "Raportoi julkaisu…"

#: src/components/status.jsx:1906
#: src/components/status.jsx:1942
#: src/components/status.jsx:2654
msgid "Liked"
msgstr "Tykätty"

#: src/components/status.jsx:1939
#: src/components/status.jsx:2641
msgid "Boosted"
msgstr "Tehostettu"

#: src/components/status.jsx:1949
#: src/components/status.jsx:2666
msgid "Bookmarked"
msgstr "Lisätty kirjanmerkkeihin"

#: src/components/status.jsx:1953
msgid "Pinned"
msgstr "Kiinnitetty"

#: src/components/status.jsx:1999
#: src/components/status.jsx:2478
msgid "Deleted"
msgstr "Poistettu"

#: src/components/status.jsx:2040
msgid "{repliesCount, plural, one {# reply} other {# replies}}"
msgstr "{repliesCount, plural, one {# vastaus} other {# vastausta}}"

#. placeholder {0}: snapStates.statusThreadNumber[sKey] ? ` ${snapStates.statusThreadNumber[sKey]}/X` : ''
#: src/components/status.jsx:2130
msgid "Thread{0}"
msgstr "Ketju{0}"

#: src/components/status.jsx:2208
#: src/components/status.jsx:2270
#: src/components/status.jsx:2374
msgid "Show less"
msgstr "Näytä vähemmän"

#: src/components/status.jsx:2208
#: src/components/status.jsx:2270
msgid "Show content"
msgstr "Näytä sisältö"

#. placeholder {0}: filterInfo.titlesStr
#. placeholder {0}: filterInfo?.titlesStr
#: src/components/status.jsx:2370
#: src/pages/catchup.jsx:1879
msgid "Filtered: {0}"
msgstr "Suodatettu: {0}"

#: src/components/status.jsx:2374
msgid "Show media"
msgstr "Näytä media"

#: src/components/status.jsx:2514
msgid "Edited"
msgstr "Muokattu"

#: src/components/status.jsx:2591
msgid "Comments"
msgstr "Kommentit"

#. More from [Author]
#: src/components/status.jsx:2891
msgid "More from <0/>"
msgstr "Lisää tekijältä <0/>"

#: src/components/status.jsx:3193
msgid "Edit History"
msgstr "Muokkaushistoria"

#: src/components/status.jsx:3197
msgid "Failed to load history"
msgstr "Historian lataus epäonnistui"

#: src/components/status.jsx:3202
#: src/pages/annual-report.jsx:45
msgid "Loading…"
msgstr "Ladataan…"

#: src/components/status.jsx:3438
msgid "HTML Code"
msgstr "HTML-koodi"

#: src/components/status.jsx:3455
msgid "HTML code copied"
msgstr "HTML-koodi kopioitu"

#: src/components/status.jsx:3458
msgid "Unable to copy HTML code"
msgstr "HTML-koodia ei voitu kopioida"

#: src/components/status.jsx:3470
msgid "Media attachments:"
msgstr "Medialiitteet:"

#: src/components/status.jsx:3492
msgid "Account Emojis:"
msgstr "Tilin emojit:"

#: src/components/status.jsx:3523
#: src/components/status.jsx:3568
msgid "static URL"
msgstr "staattinen URL"

#: src/components/status.jsx:3537
msgid "Emojis:"
msgstr "Emojit:"

#: src/components/status.jsx:3582
msgid "Notes:"
msgstr "Huomiot:"

#: src/components/status.jsx:3586
msgid "This is static, unstyled and scriptless. You may need to apply your own styles and edit as needed."
msgstr "Tämä on staattinen, tyylittelemätön ja skriptitön. Saatat joutua käyttämään omia tyylejäsi ja muokkaamaan koodia tarpeen mukaan."

#: src/components/status.jsx:3592
msgid "Polls are not interactive, becomes a list with vote counts."
msgstr "Äänestykset eivät ole vuorovaikutteisia, vaan niistä tulee luettelo äänimääristä."

#: src/components/status.jsx:3597
msgid "Media attachments can be images, videos, audios or any file types."
msgstr "Medialiitteet voivat olla kuvia, videoita, ääniä tai mitä tahansa muita tiedostotyyppejä."

#: src/components/status.jsx:3603
msgid "Post could be edited or deleted later."
msgstr "Julkaisua voi muokata tai sen voi poistaa myöhemmin."

#: src/components/status.jsx:3609
msgid "Preview"
msgstr "Esikatselu"

#: src/components/status.jsx:3618
msgid "Note: This preview is lightly styled."
msgstr "Huomaa: Tämä esikatselu on kevyesti tyylitelty."

#. [Name] [Visibility icon] boosted
#: src/components/status.jsx:3871
msgid "<0/> <1/> boosted"
msgstr "<0/> <1/> tehosti"

#: src/components/status.jsx:3973
msgid "Post hidden by your filters"
msgstr "Julkaisu piilotettu suodattimiesi perusteella"

#: src/components/status.jsx:3974
msgid "Post removed by author."
msgstr "Tekijä on poistanut julkaisun."

#: src/components/status.jsx:3975
msgid "You’re not authorized to view this post."
msgstr "Sinulla ei ole valtuuksia tarkastella tätä julkaisua."

#: src/components/status.jsx:3976
msgid "Post pending author approval."
msgstr "Julkaisu odottaa tekijän hyväksyntää."

#: src/components/status.jsx:3977
#: src/components/status.jsx:3978
msgid "Quoting not allowed by the author."
msgstr "Julkaisun tekijä ei salli lainaamista."

#: src/components/timeline.jsx:485
#: src/pages/settings.jsx:1218
msgid "New posts"
msgstr "Uudet julkaisut"

#: src/components/timeline.jsx:586
#: src/pages/home.jsx:228
#: src/pages/notifications.jsx:898
#: src/pages/status.jsx:1077
#: src/pages/status.jsx:1454
msgid "Try again"
msgstr "Yritä uudelleen"

#. placeholder {0}: fItems.length
#: src/components/timeline.jsx:621
msgid "{0, plural, one {# Boost} other {# Boosts}}"
msgstr "{0, plural, one {# tehostus} other {# tehostusta}}"

#: src/components/timeline.jsx:626
msgid "Pinned posts"
msgstr "Kiinnitetyt julkaisut"

#: src/components/timeline.jsx:985
#: src/components/timeline.jsx:992
#: src/pages/catchup.jsx:1897
msgid "Thread"
msgstr "Ketju"

#. placeholder {0}: filterInfo.titlesStr
#: src/components/timeline.jsx:1007
msgid "<0>Filtered</0>: <1>{0}</1>"
msgstr "<0>Suodatettu</0>: <1>{0}</1>"

#: src/components/translation-block.jsx:196
msgid "Auto-translated from {sourceLangText}"
msgstr "Käännetty automaattisesti kielestä {sourceLangText}"

#: src/components/translation-block.jsx:234
msgid "Translating…"
msgstr "Käännetään…"

#: src/components/translation-block.jsx:237
msgid "Translate from {sourceLangText} (auto-detected)"
msgstr "Käännä kielestä {sourceLangText} (tunnistettu automaattisesti)"

#: src/components/translation-block.jsx:238
msgid "Translate from {sourceLangText}"
msgstr "Käännä kielestä {sourceLangText}"

#. placeholder {0}: detectedLang ?? '…'
#: src/components/translation-block.jsx:266
msgid "Auto ({0})"
msgstr "Automaattinen ({0})"

#: src/components/translation-block.jsx:279
msgid "Failed to translate"
msgstr "Kääntäminen epäonnistui"

#: src/compose.jsx:33
msgid "Editing source status"
msgstr "Muokataan lähdepäivitystä"

#. placeholder {0}: replyToStatus.account?.acct || replyToStatus.account?.username
#: src/compose.jsx:35
msgid "Replying to @{0}"
msgstr "Vastataan käyttäjälle @{0}"

#: src/compose.jsx:63
msgid "You may close this page now."
msgstr "Voit nyt sulkea tämän sivun."

#: src/compose.jsx:71
msgid "Close window"
msgstr "Sulje ikkuna"

#: src/compose.jsx:87
msgid "Login required."
msgstr "Kirjautuminen vaaditaan."

#: src/compose.jsx:91
#: src/pages/annual-report.jsx:164
#: src/pages/http-route.jsx:91
#: src/pages/login.jsx:280
msgid "Go home"
msgstr "Siirry etusivulle"

#: src/pages/account-statuses.jsx:238
msgid "Account posts"
msgstr "Tilin julkaisut"

#: src/pages/account-statuses.jsx:245
msgid "{accountDisplay} (+ Replies)"
msgstr "{accountDisplay} (+ Vastaukset)"

#: src/pages/account-statuses.jsx:247
msgid "{accountDisplay} (- Boosts)"
msgstr "{accountDisplay} (- Tehostukset)"

#: src/pages/account-statuses.jsx:249
msgid "{accountDisplay} (#{tagged})"
msgstr "{accountDisplay} (#{tagged})"

#: src/pages/account-statuses.jsx:251
msgid "{accountDisplay} (Media)"
msgstr "{accountDisplay} (Media)"

#: src/pages/account-statuses.jsx:257
msgid "{accountDisplay} ({monthYear})"
msgstr "{accountDisplay} ({monthYear})"

#: src/pages/account-statuses.jsx:323
msgid "Clear filters"
msgstr "Tyhjennä suodattimet"

#: src/pages/account-statuses.jsx:326
msgid "Clear"
msgstr "Tyhjennä"

#: src/pages/account-statuses.jsx:340
msgid "Showing post with replies"
msgstr "Näytetään julkaisut vastaukset mukaan lukien"

#: src/pages/account-statuses.jsx:345
msgid "+ Replies"
msgstr "+ Vastaukset"

#: src/pages/account-statuses.jsx:351
msgid "Showing posts without boosts"
msgstr "Näytetään julkaisut tehostukset pois lukien"

#: src/pages/account-statuses.jsx:356
msgid "- Boosts"
msgstr "- Tehostukset"

#: src/pages/account-statuses.jsx:362
msgid "Showing posts with media"
msgstr "Näytetään julkaisut, joissa on mediaa"

#. placeholder {0}: tag.name
#: src/pages/account-statuses.jsx:379
msgid "Showing posts tagged with #{0}"
msgstr "Näytetään julkaisut tunnisteella #{0}"

#. placeholder {0}: date.toLocaleString(i18n.locale, { month: 'long', year: 'numeric', })
#: src/pages/account-statuses.jsx:418
msgid "Showing posts in {0}"
msgstr "Näytetään julkaisut ajalta {0}"

#: src/pages/account-statuses.jsx:508
msgid "Nothing to see here yet."
msgstr "Täällä ei ole vielä mitään nähtävää."

#: src/pages/account-statuses.jsx:509
#: src/pages/public.jsx:99
#: src/pages/trending.jsx:452
msgid "Unable to load posts"
msgstr "Julkaisua ei voitu ladata"

#: src/pages/account-statuses.jsx:550
#: src/pages/account-statuses.jsx:580
msgid "Unable to fetch account info"
msgstr "Tilitietoja ei voitu hakea"

#. placeholder {0}: accountInstance ? ( <> {' '} (<b>{punycode.toUnicode(accountInstance)}</b>) </> ) : null
#: src/pages/account-statuses.jsx:557
msgid "Switch to account's instance {0}"
msgstr "Vaihda tilin instanssiin {0}"

#: src/pages/account-statuses.jsx:587
msgid "Switch to my instance (<0>{currentInstance}</0>)"
msgstr "Vaihda omaan instanssiin (<0>{currentInstance}</0>)"

#: src/pages/account-statuses.jsx:660
msgid "Month"
msgstr "Kuukausi"

#: src/pages/accounts.jsx:64
msgid "Current"
msgstr "Nykyinen"

#: src/pages/accounts.jsx:110
msgid "Default"
msgstr "Oletus"

#: src/pages/accounts.jsx:132
msgid "Switch to this account"
msgstr "Vaihda tähän tiliin"

#: src/pages/accounts.jsx:141
msgid "Switch in new tab/window"
msgstr "Vaihda uuteen välilehteen/ikkunaan"

#: src/pages/accounts.jsx:155
msgid "View profile…"
msgstr "Näytä profiili…"

#: src/pages/accounts.jsx:173
msgid "Set as default"
msgstr "Aseta oletukseksi"

#. placeholder {0}: account.info.acct
#: src/pages/accounts.jsx:215
msgid "Log out <0>@{0}</0>?"
msgstr "Kirjataanko <0>@{0}</0> ulos?"

#: src/pages/accounts.jsx:244
msgid "Log out…"
msgstr "Kirjaa ulos…"

#. placeholder {0}: niceDateTime(account.createdAt)
#: src/pages/accounts.jsx:251
msgid "Connected on {0} (<0/>)"
msgstr "Yhdistetty {0} (<0/>)"

#: src/pages/accounts.jsx:268
msgid "Add an existing account"
msgstr "Lisää olemassa oleva tili"

#: src/pages/accounts.jsx:275
msgid "Note: <0>Default</0> account will always be used for first load. Switched accounts will persist during the session."
msgstr "Huomaa: Ensimmäiseen lataukseen käytetään aina <0>oletustiliä</0>. Vaihtoehtoiset tilit säilyvät istunnon ajan."

#: src/pages/bookmarks.jsx:28
msgid "No bookmarks yet. Go bookmark something!"
msgstr "Ei vielä kirjanmerkkejä. Mene ja lisää jokin kirjanmerkkeihin!"

#: src/pages/bookmarks.jsx:29
msgid "Unable to load bookmarks."
msgstr "Kirjanmerkkejä ei voitu ladata."

#: src/pages/catchup.jsx:54
msgid "last 1 hour"
msgstr "viimeiseltä tunnilta"

#: src/pages/catchup.jsx:55
msgid "last 2 hours"
msgstr "viimeiseltä 2 tunnilta"

#: src/pages/catchup.jsx:56
msgid "last 3 hours"
msgstr "viimeiseltä 3 tunnilta"

#: src/pages/catchup.jsx:57
msgid "last 4 hours"
msgstr "viimeiseltä 4 tunnilta"

#: src/pages/catchup.jsx:58
msgid "last 5 hours"
msgstr "viimeiseltä 5 tunnilta"

#: src/pages/catchup.jsx:59
msgid "last 6 hours"
msgstr "viimeiseltä 6 tunnilta"

#: src/pages/catchup.jsx:60
msgid "last 7 hours"
msgstr "viimeiseltä 7 tunnilta"

#: src/pages/catchup.jsx:61
msgid "last 8 hours"
msgstr "viimeiseltä 8 tunnilta"

#: src/pages/catchup.jsx:62
msgid "last 9 hours"
msgstr "viimeiseltä 9 tunnilta"

#: src/pages/catchup.jsx:63
msgid "last 10 hours"
msgstr "viimeiseltä 10 tunnilta"

#: src/pages/catchup.jsx:64
msgid "last 11 hours"
msgstr "viimeiseltä 11 tunnilta"

#: src/pages/catchup.jsx:65
msgid "last 12 hours"
msgstr "viimeiseltä 12 tunnilta"

#: src/pages/catchup.jsx:66
msgid "beyond 12 hours"
msgstr "ennen viimeistä 12 tuntia"

#: src/pages/catchup.jsx:73
msgid "Followed tags"
msgstr "Seurattavat tunnisteet"

#: src/pages/catchup.jsx:74
msgid "Groups"
msgstr "Ryhmät"

#: src/pages/catchup.jsx:597
msgid "Showing {selectedFilterCategory, select, all {all posts} original {original posts} replies {replies} boosts {boosts} followedTags {followed tags} groups {groups} filtered {filtered posts}}, {sortBy, select, createdAt {{sortOrder, select, asc {oldest} desc {latest}}} reblogsCount {{sortOrder, select, asc {fewest boosts} desc {most boosts}}} favouritesCount {{sortOrder, select, asc {fewest likes} desc {most likes}}} repliesCount {{sortOrder, select, asc {fewest replies} desc {most replies}}} density {{sortOrder, select, asc {least dense} desc {most dense}}}} first{groupBy, select, account {, grouped by authors} other {}}"
msgstr "Näytetään {selectedFilterCategory, select, all {kaikki julkaisut} original {alkuperäiset julkaisut} replies {vastaukset} boosts {tehostukset} followedTags {seurattavat tunnisteet} groups {ryhmät} filtered {suodatetut julkaisut}}, {sortBy, select, createdAt {{sortOrder, select, asc {vanhimmat} desc {uusimmat}}} reblogsCount {{sortOrder, select, asc {vähiten tehostetut} desc {eniten tehostetut}}} favouritesCount {{sortOrder, select, asc {vähiten tykätyt} desc {eniten tykätyt}}} repliesCount {{sortOrder, select, asc {vähiten vastauksia saaneet} desc {eniten vastauksia saanteet}}} density {{sortOrder, select, asc {vähiten tiheät} desc {tiheimmät}}}} ensin{groupBy, select, account {, tekijöittäin ryhmiteltynä} other {}}"

#: src/pages/catchup.jsx:889
#: src/pages/catchup.jsx:913
msgid "Catch-up <0>beta</0>"
msgstr "Kiinnikuronta <0>beeta</0>"

#: src/pages/catchup.jsx:903
#: src/pages/catchup.jsx:1588
msgid "Help"
msgstr "Ohje"

#: src/pages/catchup.jsx:919
msgid "What is this?"
msgstr "Mikä tämä on?"

#: src/pages/catchup.jsx:922
msgid "Catch-up is a separate timeline for your followings, offering a high-level view at a glance, with a simple, email-inspired interface to effortlessly sort and filter through posts."
msgstr "Kiinnikuronta on erillinen aikajana, joka tarjoaa ylätason näkymän seurattaviisi yhdellä silmäyksellä. Yksinkertainen, sähköpostin innoittama käyttöliittymä, jossa voit vaivattomasti järjestellä ja suodattaa julkaisuja."

#: src/pages/catchup.jsx:933
msgid "Preview of Catch-up UI"
msgstr "Kiinnikuronnan käyttöliittymän esikatselu"

#: src/pages/catchup.jsx:942
msgid "Let's catch up"
msgstr "Kurotaanpa kiinni"

#: src/pages/catchup.jsx:947
msgid "Let's catch up on the posts from your followings."
msgstr "Kurotaanpa seurattaviesi julkaisut kiinni."

#: src/pages/catchup.jsx:951
msgid "Show me all posts from…"
msgstr "Näytä kaikki julkaisut…"

#: src/pages/catchup.jsx:974
msgid "until the max"
msgstr "enimmäisaikaan asti"

#: src/pages/catchup.jsx:1004
msgid "Catch up"
msgstr "Kuro kiinni"

#: src/pages/catchup.jsx:1010
msgid "Overlaps with your last catch-up"
msgstr "Menee edellisen kiinnikurontasi päälle"

#. placeholder {0}: dtf.format(new Date(lastCatchupEndAt))
#: src/pages/catchup.jsx:1022
msgid "Until the last catch-up ({0})"
msgstr "Edelliseen kiinnikurontaasi asti ({0})"

#: src/pages/catchup.jsx:1031
msgid "Note: your instance might only show a maximum of 800 posts in the Home timeline regardless of the time range. Could be less or more."
msgstr "Huomaa: Instanssisi saattaa näyttää kotiaikajanalla enintään vain 800 julkaisua riippumatta valitusta aikavälistä. Määrä voi olla pienempi tai suurempi."

#: src/pages/catchup.jsx:1041
msgid "Previously…"
msgstr "Aiemmin…"

#. placeholder {0}: pc.count
#: src/pages/catchup.jsx:1059
msgid "{0, plural, one {# post} other {# posts}}"
msgstr "{0, plural, one {# julkaisu} other {# julkaisua}}"

#: src/pages/catchup.jsx:1069
msgid "Remove this catch-up?"
msgstr "Poistetaanko tämä kiinnikuronta?"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1072
msgid "Removing Catch-up {0}"
msgstr "Poistetaan kiinnikuronta {0}"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1076
msgid "Catch-up {0} removed"
msgstr "Kiinnikuronta {0} poistettu"

#: src/pages/catchup.jsx:1090
msgid "Note: Only max 3 will be stored. The rest will be automatically removed."
msgstr "Huomaa: Vain enintään 3 tallennetaan. Loput poistetaan automaattisesti."

#: src/pages/catchup.jsx:1105
msgid "Fetching posts…"
msgstr "Haetaan julkaisuja…"

#: src/pages/catchup.jsx:1108
msgid "This might take a while."
msgstr "Tämä saattaa kestää hetken."

#: src/pages/catchup.jsx:1143
msgid "Reset filters"
msgstr "Palauta suodattimet"

#: src/pages/catchup.jsx:1151
#: src/pages/catchup.jsx:1594
msgid "Top links"
msgstr "Suosituimmat linkit"

#. placeholder {0}: sharers.map((s) => { const { avatarStatic, displayName } = s; return ( <button type="button" class="plain" style={{ padding: 0, }} onClick={(e) => { e.preventDefault(); e.stopPropagation(); // Reset and filter to author const { id } = s; setSelectedAuthor(id); setSelectedFilterCategory('all'); }} > <Avatar url={avatarStatic} size="s" alt={displayName} /> </button> ); })
#: src/pages/catchup.jsx:1264
msgid "Shared by {0}"
msgstr "Jakanut {0}"

#: src/pages/catchup.jsx:1319
#: src/pages/mentions.jsx:154
#: src/pages/search.jsx:329
msgid "All"
msgstr "Kaikki"

#. placeholder {0}: authorCountsList.length
#: src/pages/catchup.jsx:1404
msgid "{0, plural, one {# author} other {# authors}}"
msgstr "{0, plural, one {# tekijä} other {# tekijää}}"

#: src/pages/catchup.jsx:1416
msgid "Sort"
msgstr "Järjestys"

#: src/pages/catchup.jsx:1447
msgid "Date"
msgstr "Päiväys"

#: src/pages/catchup.jsx:1451
msgid "Density"
msgstr "Tiheys"

#. js-lingui-explicit-id
#: src/pages/catchup.jsx:1474
msgid "group.filter"
msgstr "Ryhmä"

#: src/pages/catchup.jsx:1489
msgid "Authors"
msgstr "Tekijät"

#: src/pages/catchup.jsx:1490
msgid "None"
msgstr "Ei mikään"

#: src/pages/catchup.jsx:1506
msgid "Show all authors"
msgstr "Näytä kaikki tekijät"

#: src/pages/catchup.jsx:1557
msgid "You don't have to read everything."
msgstr "Sinun ei tarvitse lukea kaikkea."

#: src/pages/catchup.jsx:1558
msgid "That's all."
msgstr "Siinä kaikki."

#: src/pages/catchup.jsx:1566
msgid "Back to top"
msgstr "Takaisin ylös"

#: src/pages/catchup.jsx:1597
msgid "Links shared by followings, sorted by shared counts, boosts and likes."
msgstr "Seurattavien jakamat linkit järjestettynä jakomäärän mukaan, tehostukset ja tykkäykset."

#: src/pages/catchup.jsx:1603
msgid "Sort: Density"
msgstr "Järjestys: Tiheys"

#: src/pages/catchup.jsx:1606
msgid "Posts are sorted by information density or depth. Shorter posts are \"lighter\" while longer posts are \"heavier\". Posts with photos are \"heavier\" than posts without photos."
msgstr "Julkaisut järjestetään informaatiotiheyden tai -syvyyden mukaan. Lyhyemmät julkaisut ovat ”kevyempiä”, pidemmät taas ”painavampia”. Kuvan sisältävät julkaisut ovat ”paivavampia” kuin kuvattomat."

#: src/pages/catchup.jsx:1613
msgid "Group: Authors"
msgstr "Ryhmä: Tekijät"

#: src/pages/catchup.jsx:1616
msgid "Posts are grouped by authors, sorted by posts count per author."
msgstr "Julkaisut ryhmitellään tekijän mukaan ja järjestellään tekijäkohtaisen julkaisumäärän perusteella."

#: src/pages/catchup.jsx:1663
msgid "Next author"
msgstr "Seuraava tekijä"

#: src/pages/catchup.jsx:1671
msgid "Previous author"
msgstr "Edellinen tekijä"

#: src/pages/catchup.jsx:1687
msgid "Scroll to top"
msgstr "Vieritä ylös"

#: src/pages/favourites.jsx:28
msgid "No likes yet. Go like something!"
msgstr "Ei vielä tykkäyksiä. Mene ja tykkää jostakin!"

#: src/pages/favourites.jsx:29
msgid "Unable to load likes."
msgstr "Tykkäyksiä ei voitu ladata."

#: src/pages/filters.jsx:23
msgid "Home and lists"
msgstr "Koti ja listat"

#: src/pages/filters.jsx:25
msgid "Public timelines"
msgstr "Julkiset aikajanat"

#: src/pages/filters.jsx:26
msgid "Conversations"
msgstr "Keskustelut"

#: src/pages/filters.jsx:27
msgid "Profiles"
msgstr "Profiilit"

#: src/pages/filters.jsx:42
msgid "Never"
msgstr "Ei koskaan"

#: src/pages/filters.jsx:104
#: src/pages/filters.jsx:229
msgid "New filter"
msgstr "Uusi suodatin"

#. placeholder {0}: filters.length
#: src/pages/filters.jsx:152
msgid "{0, plural, one {# filter} other {# filters}}"
msgstr "{0, plural, one {# suodatin} other {# suodatinta}}"

#: src/pages/filters.jsx:167
msgid "Unable to load filters."
msgstr "Suodattimia ei voitu ladata."

#: src/pages/filters.jsx:171
msgid "No filters yet."
msgstr "Ei vielä suodattimia."

#: src/pages/filters.jsx:178
msgid "Add filter"
msgstr "Lisää suodatin"

#: src/pages/filters.jsx:229
msgid "Edit filter"
msgstr "Muokkaa suodatinta"

#: src/pages/filters.jsx:346
msgid "Unable to edit filter"
msgstr "Suodatinta ei voitu muokata"

#: src/pages/filters.jsx:347
msgid "Unable to create filter"
msgstr "Suodatinta ei voitu luoda"

#: src/pages/filters.jsx:356
msgid "Title"
msgstr "Nimi"

#: src/pages/filters.jsx:397
msgid "Whole word"
msgstr "Koko sana"

#: src/pages/filters.jsx:423
msgid "No keywords. Add one."
msgstr "Ei avainsanoja. Lisää sellainen."

#: src/pages/filters.jsx:450
msgid "Add keyword"
msgstr "Lisää avainsana"

#. placeholder {0}: filteredEditKeywords.length
#: src/pages/filters.jsx:454
msgid "{0, plural, one {# keyword} other {# keywords}}"
msgstr "{0, plural, one {# avainsana} other {# avainsanaa}}"

#: src/pages/filters.jsx:467
msgid "Filter from…"
msgstr "Suodata kohteesta…"

#: src/pages/filters.jsx:493
msgid "* Not implemented yet"
msgstr "* Ei vielä toteutettu"

#: src/pages/filters.jsx:499
msgid "Status: <0><1/></0>"
msgstr "Tila: <0><1/></0>"

#: src/pages/filters.jsx:508
msgid "Change expiry"
msgstr "Muuta vanhentumista"

#: src/pages/filters.jsx:508
msgid "Expiry"
msgstr "Vanhentuminen"

#: src/pages/filters.jsx:527
msgid "Filtered post will be…"
msgstr "Suodatetut julkaisut…"

#: src/pages/filters.jsx:538
msgid "obscured (media only)"
msgstr "piilotettu (vain media)"

#: src/pages/filters.jsx:552
msgid "minimized"
msgstr "pienennetään"

#: src/pages/filters.jsx:562
msgid "hidden"
msgstr "piilotetaan"

#: src/pages/filters.jsx:579
msgid "Delete this filter?"
msgstr "Poistetaanko tämä suodatin?"

#: src/pages/filters.jsx:592
msgid "Unable to delete filter."
msgstr "Suodatinta ei voitu poistaa."

#: src/pages/filters.jsx:625
msgid "Expired"
msgstr "Vanhentunut"

#: src/pages/filters.jsx:627
msgid "Expiring <0/>"
msgstr "Vanhenee <0/>"

#: src/pages/filters.jsx:631
msgid "Never expires"
msgstr "Ei vanhene koskaan"

#. placeholder {0}: followedHashtags.length
#: src/pages/followed-hashtags.jsx:71
msgid "{0, plural, one {# hashtag} other {# hashtags}}"
msgstr "{0, plural, one {# aihetunniste} other {# aihetunnistetta}}"

#: src/pages/followed-hashtags.jsx:86
msgid "Unable to load followed hashtags."
msgstr "Seurattavia aihetunnisteita ei voitu ladata."

#: src/pages/followed-hashtags.jsx:90
msgid "No hashtags followed yet."
msgstr "Ei vielä seurattavia aihetunnisteita."

#: src/pages/following.jsx:147
msgid "Nothing to see here."
msgstr "Täällä ei ole mitään nähtävää."

#: src/pages/following.jsx:148
#: src/pages/list.jsx:109
msgid "Unable to load posts."
msgstr "Julkaisuja ei voitu ladata."

#: src/pages/hashtag.jsx:56
msgid "{hashtagTitle} (Media only) on {instance}"
msgstr "{hashtagTitle} (vain media) instansissa {instance}"

#: src/pages/hashtag.jsx:57
msgid "{hashtagTitle} on {instance}"
msgstr "{hashtagTitle} instanssissa {instance}"

#: src/pages/hashtag.jsx:59
msgid "{hashtagTitle} (Media only)"
msgstr "{hashtagTitle} (vain media)"

#: src/pages/hashtag.jsx:60
msgid "{hashtagTitle}"
msgstr "{hashtagTitle}"

#: src/pages/hashtag.jsx:185
msgid "No one has posted anything with this tag yet."
msgstr "Kukaan ei ole julkaissut vielä mitään tällä tunnisteella."

#: src/pages/hashtag.jsx:186
msgid "Unable to load posts with this tag"
msgstr "Julkaisuja tällä tunnisteella ei voitu ladata"

#: src/pages/hashtag.jsx:212
msgid "Unfollow #{hashtag}?"
msgstr "Lopetetaanko tunnisteen #{hashtag} seuraaminen?"

#: src/pages/hashtag.jsx:227
msgid "Unfollowed #{hashtag}"
msgstr "Lopetettu tunnisteen #{hashtag} seuraaminen"

#: src/pages/hashtag.jsx:242
msgid "Followed #{hashtag}"
msgstr "Seurataan tunnistetta #{hashtag}"

#: src/pages/hashtag.jsx:258
msgid "Following…"
msgstr "Seurataan…"

#: src/pages/hashtag.jsx:287
msgid "Unfeatured on profile"
msgstr "Poistettu esiltä profiilista"

#: src/pages/hashtag.jsx:301
msgid "Unable to unfeature on profile"
msgstr "Ei voitu poistaa esiltä profiilista"

#: src/pages/hashtag.jsx:310
#: src/pages/hashtag.jsx:326
msgid "Featured on profile"
msgstr "Nostettu esille profiiliin"

#: src/pages/hashtag.jsx:398
msgid "{TOTAL_TAGS_LIMIT, plural, other {Max # tags}}"
msgstr "{TOTAL_TAGS_LIMIT, plural, other {Enintään # tunnistetta}}"

#: src/pages/hashtag.jsx:401
msgid "Add hashtag"
msgstr "Lisää aihetunniste"

#: src/pages/hashtag.jsx:433
msgid "Remove hashtag"
msgstr "Poista aihetunniste"

#: src/pages/hashtag.jsx:447
msgid "{SHORTCUTS_LIMIT, plural, one {Max # shortcut reached. Unable to add shortcut.} other {Max # shortcuts reached. Unable to add shortcut.}}"
msgstr "{SHORTCUTS_LIMIT, plural, one {Pikavalintojen enimmäismäärä # ylitetty. Pikavalintaa ei voitu lisätä.} other {Pikavalintojen enimmäismäärä # ylitetty. Pikavalintaa ei voitu lisätä.}}"

#: src/pages/hashtag.jsx:476
msgid "This shortcut already exists"
msgstr "Tämä pikavalinta on jo olemassa"

#: src/pages/hashtag.jsx:479
msgid "Hashtag shortcut added"
msgstr "Aihetunnisteen pikavalinta lisätty"

#: src/pages/hashtag.jsx:485
msgid "Add to Shortcuts"
msgstr "Lisää pikatoimintoihin"

#: src/pages/hashtag.jsx:491
#: src/pages/public.jsx:141
#: src/pages/trending.jsx:481
msgid "Enter a new instance e.g. \"mastodon.social\""
msgstr "Anna uusi instanssi, esim. ”mastodon.social”"

#: src/pages/hashtag.jsx:494
#: src/pages/public.jsx:144
#: src/pages/trending.jsx:484
msgid "Invalid instance"
msgstr "Virheellinen instanssi"

#: src/pages/hashtag.jsx:508
#: src/pages/public.jsx:158
#: src/pages/trending.jsx:496
msgid "Go to another instance…"
msgstr "Siirry toiseen instanssiin…"

#: src/pages/hashtag.jsx:521
#: src/pages/public.jsx:171
#: src/pages/trending.jsx:507
msgid "Go to my instance (<0>{currentInstance}</0>)"
msgstr "Siirry omalle instanssille (<0>{currentInstance}</0>)"

#: src/pages/home.jsx:224
msgid "Unable to fetch notifications."
msgstr "Ilmoituksia ei voitu hakea."

#: src/pages/home.jsx:245
msgid "<0>New</0> <1>Follow Requests</1>"
msgstr "<0>Uudet</0> <1>seurantapyynnöt</1>"

#: src/pages/home.jsx:251
msgid "See all"
msgstr "Katso kaikki"

#: src/pages/http-route.jsx:68
msgid "Resolving…"
msgstr "Resolvoidaan…"

#: src/pages/http-route.jsx:79
msgid "Unable to resolve URL"
msgstr "URL-osoitetta ei voitu resolvoida"

#: src/pages/list.jsx:108
msgid "Nothing yet."
msgstr "Ei vielä mitään."

#: src/pages/list.jsx:201
#: src/pages/list.jsx:307
msgid "Manage members"
msgstr "Hallitse jäseniä"

#. placeholder {0}: account.username
#: src/pages/list.jsx:342
msgid "Remove <0>@{0}</0> from list?"
msgstr "Poistetaanko <0>@{0}</0> listasta?"

#: src/pages/list.jsx:388
msgid "Remove…"
msgstr "Poista…"

#. placeholder {0}: lists.length
#: src/pages/lists.jsx:115
msgid "{0, plural, one {# list} other {# lists}}"
msgstr "{0, plural, one {# lista} other {# listaa}}"

#: src/pages/lists.jsx:135
msgid "No lists yet."
msgstr "Ei vielä listoja."

#: src/pages/login.jsx:122
#: src/pages/login.jsx:132
msgid "Failed to register application"
msgstr "Sovelluksen rekisteröinti epäonnistui"

#: src/pages/login.jsx:218
msgid "instance domain"
msgstr "instanssin verkkotunnus"

#: src/pages/login.jsx:242
msgid "e.g. “mastodon.social”"
msgstr "esim. ”mastodon.social”"

#: src/pages/login.jsx:253
msgid "Failed to log in. Please try again or try another instance."
msgstr "Sisäänkirjautuminen epäonnistui. Yritä uudelleen tai kokeile toista instanssia."

#: src/pages/login.jsx:265
msgid "Continue with {selectedInstanceText}"
msgstr "Jatka instanssilla {selectedInstanceText}"

#: src/pages/login.jsx:266
msgid "Continue"
msgstr "Jatka"

#: src/pages/login.jsx:274
msgid "Don't have an account? Create one!"
msgstr "Eikö sinulla ole tiliä? Luo sellainen!"

#: src/pages/mentions.jsx:21
msgid "Private mentions"
msgstr "Yksityismaininnat"

#: src/pages/mentions.jsx:166
msgid "Private"
msgstr "Yksityiset"

#: src/pages/mentions.jsx:176
msgid "No one mentioned you :("
msgstr "Kukaan ei ole maininnut sinua :("

#: src/pages/mentions.jsx:177
msgid "Unable to load mentions."
msgstr "Mainintoja ei voitu ladata."

#: src/pages/notifications.jsx:108
msgid "You don't follow"
msgstr "Joita et seuraa"

#: src/pages/notifications.jsx:109
msgid "Who don't follow you"
msgstr "Jotka eivät seuraa sinua"

#: src/pages/notifications.jsx:110
msgid "With a new account"
msgstr "Joilla on uusi tili"

#: src/pages/notifications.jsx:111
msgid "Who unsolicitedly private mention you"
msgstr "Jotka pyytämättä mainitsevat sinut yksityisesti"

#: src/pages/notifications.jsx:112
msgid "Who are limited by server moderators"
msgstr "Joita palvelimen moderaattorit ovat rajoittaneet"

#: src/pages/notifications.jsx:605
#: src/pages/notifications.jsx:946
msgid "Notifications settings"
msgstr "Ilmoitusasetukset"

#: src/pages/notifications.jsx:623
msgid "New notifications"
msgstr "Uudet ilmoitukset"

#. placeholder {0}: announcements.length
#: src/pages/notifications.jsx:634
msgid "{0, plural, one {Announcement} other {Announcements}}"
msgstr "{0, plural, one {Tiedote} other {Tiedotteet}}"

#: src/pages/notifications.jsx:681
#: src/pages/settings.jsx:1206
msgid "Follow requests"
msgstr "Seurauspyynnöt"

#. placeholder {0}: followRequests.length
#: src/pages/notifications.jsx:686
msgid "{0, plural, one {# follow request} other {# follow requests}}"
msgstr "{0, plural, one {# seurauspyyntö} other {# seurauspyyntöä}}"

#. placeholder {0}: notificationsPolicy.summary.pendingRequestsCount
#: src/pages/notifications.jsx:741
msgid "{0, plural, one {Filtered notifications from # person} other {Filtered notifications from # people}}"
msgstr "{0, plural, one {Suodatettu ilmoitukset # käyttäjältä} other {Suodatettu ilmoitukset # käyttäjältä}}"

#: src/pages/notifications.jsx:814
msgid "Only mentions"
msgstr "Vain maininnat"

#: src/pages/notifications.jsx:818
msgid "Today"
msgstr "Tänään"

#: src/pages/notifications.jsx:823
msgid "You're all caught up."
msgstr "Olet ajan tasalla."

#: src/pages/notifications.jsx:846
msgid "Yesterday"
msgstr "Eilen"

#: src/pages/notifications.jsx:894
msgid "Unable to load notifications"
msgstr "Ilmoituksia ei voitu ladata"

#: src/pages/notifications.jsx:973
msgid "Notifications settings updated"
msgstr "Päivitetty ilmoitusasetukset"

#: src/pages/notifications.jsx:981
msgid "Filter out notifications from people:"
msgstr "Suodata ilmoitukset pois käyttäjiltä:"

#: src/pages/notifications.jsx:995
msgid "Filter"
msgstr "Suodata"

#: src/pages/notifications.jsx:998
msgid "Ignore"
msgstr "Sivuuta"

#. placeholder {0}: niceDateTime(updatedAtDate)
#: src/pages/notifications.jsx:1071
msgid "Updated <0>{0}</0>"
msgstr "Päivitetty <0>{0}</0>"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1139
msgid "View notifications from <0>@{0}</0>"
msgstr "Näytä ilmoitukset käyttäjältä <0>@{0}</0>"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1160
msgid "Notifications from <0>@{0}</0>"
msgstr "Ilmoitukset käyttäjältä <0>@{0}</0>"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1228
msgid "Notifications from @{0} will not be filtered from now on."
msgstr "Ilmoituksia käyttäjältä @{0} ei enää tästä lähtien suodateta."

#: src/pages/notifications.jsx:1233
msgid "Unable to accept notification request"
msgstr "Ilmoituspyyntöä ei voitu hyväksyä"

#: src/pages/notifications.jsx:1238
msgid "Allow"
msgstr "Salli"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1258
msgid "Notifications from @{0} will not show up in Filtered notifications from now on."
msgstr "Ilmoitukset käyttäjältä @{0} eivät näy suodatetuissa ilmoituksissa tästä lähtien."

#: src/pages/notifications.jsx:1263
msgid "Unable to dismiss notification request"
msgstr "Ilmoituspyyntöä ei voitu hylätä"

#: src/pages/notifications.jsx:1268
msgid "Dismiss"
msgstr "Hylkää"

#: src/pages/notifications.jsx:1283
msgid "Dismissed"
msgstr "Hylätty"

#: src/pages/public.jsx:28
msgid "Local timeline ({instance})"
msgstr "Paikallinen aikajana ({instance})"

#: src/pages/public.jsx:29
msgid "Federated timeline ({instance})"
msgstr "Federoitu aikajana ({instance})"

#: src/pages/public.jsx:92
msgid "Local timeline"
msgstr "Paikallinen aikajana"

#: src/pages/public.jsx:92
msgid "Federated timeline"
msgstr "Federoitu aikajana"

#: src/pages/public.jsx:98
msgid "No one has posted anything yet."
msgstr "Kukaan ei ole vielä julkaissut mitään."

#: src/pages/public.jsx:125
msgid "Switch to Federated"
msgstr "Vaihda federoituun"

#: src/pages/public.jsx:132
msgid "Switch to Local"
msgstr "Vaihda paikalliseen"

#: src/pages/scheduled-posts.jsx:110
msgid "No scheduled posts."
msgstr "Ei ajastettuja julkaisuja."

#. Scheduled [in 1 day] ([Thu, Feb 27, 6:30:00 PM])
#. placeholder {0}: niceDateTime(scheduledAt, { formatOpts: { weekday: 'short', second: 'numeric', }, })
#: src/pages/scheduled-posts.jsx:207
msgid "Scheduled <0><1/></0> <2>({0})</2>"
msgstr "Ajastettu <0><1/></0> <2>({0})</2>"

#. Scheduled [in 1 day]
#: src/pages/scheduled-posts.jsx:263
msgid "Scheduled <0><1/></0>"
msgstr "Ajastettu <0><1/></0>"

#: src/pages/scheduled-posts.jsx:308
msgid "Scheduled post rescheduled"
msgstr "Julkaisu ajastettu uudelleen"

#: src/pages/scheduled-posts.jsx:315
msgid "Failed to reschedule post"
msgstr "Julkaisun uudelleenajastus epäonnistui"

#: src/pages/scheduled-posts.jsx:338
msgid "Reschedule"
msgstr "Ajasta uudelleen"

#: src/pages/scheduled-posts.jsx:344
msgid "Delete scheduled post?"
msgstr "Poistetaanko ajastettu julkaisu?"

#: src/pages/scheduled-posts.jsx:352
msgid "Scheduled post deleted"
msgstr "Ajastettu julkaisu poistettu"

#: src/pages/scheduled-posts.jsx:359
msgid "Failed to delete scheduled post"
msgstr "Ajastetun julkaisun poisto epäonnistui"

#: src/pages/search.jsx:50
msgid "Search: {q} (Posts)"
msgstr "Haku: {q} (julkaisut)"

#: src/pages/search.jsx:53
msgid "Search: {q} (Accounts)"
msgstr "Haku: {q} (tilit)"

#: src/pages/search.jsx:56
msgid "Search: {q} (Hashtags)"
msgstr "Haku: {q} (aihetunnisteet)"

#: src/pages/search.jsx:59
msgid "Search: {q}"
msgstr "Haku: {q}"

#: src/pages/search.jsx:339
#: src/pages/search.jsx:421
msgid "Hashtags"
msgstr "Aihetunnisteet"

#: src/pages/search.jsx:371
#: src/pages/search.jsx:425
#: src/pages/search.jsx:495
msgid "See more"
msgstr "Katso lisää"

#: src/pages/search.jsx:397
msgid "See more accounts"
msgstr "Katso lisää tilejä"

#: src/pages/search.jsx:411
msgid "No accounts found."
msgstr "Tilejä ei löytynyt."

#: src/pages/search.jsx:467
msgid "See more hashtags"
msgstr "Katso lisää aihetunnisteita"

#: src/pages/search.jsx:481
msgid "No hashtags found."
msgstr "Aihetunnisteita ei löytynyt."

#: src/pages/search.jsx:525
msgid "See more posts"
msgstr "Katso lisää julkaisuja"

#: src/pages/search.jsx:539
msgid "No posts found."
msgstr "Julkaisuja ei löytynyt."

#: src/pages/search.jsx:583
msgid "Enter your search term or paste a URL above to get started."
msgstr "Aloita kirjoittamalla hakutermi tai liittämällä URL-osoite yläpuolelle."

#: src/pages/settings.jsx:95
msgid "Settings"
msgstr "Asetukset"

#: src/pages/settings.jsx:104
msgid "Appearance"
msgstr "Ulkoasu"

#: src/pages/settings.jsx:180
msgid "Light"
msgstr "Tumma"

#: src/pages/settings.jsx:191
msgid "Dark"
msgstr "Vaalea"

#: src/pages/settings.jsx:204
msgid "Auto"
msgstr "Autom."

#: src/pages/settings.jsx:214
msgid "Text size"
msgstr "Tekstin koko"

#. Preview of one character, in smallest size
#. Preview of one character, in largest size
#: src/pages/settings.jsx:219
#: src/pages/settings.jsx:244
msgid "A"
msgstr "A"

#: src/pages/settings.jsx:258
msgid "Display language"
msgstr "Näyttökieli"

#: src/pages/settings.jsx:267
msgid "Volunteer translations"
msgstr "Vapaaehtoisten käännökset"

#: src/pages/settings.jsx:278
msgid "Posting"
msgstr "Julkaiseminen"

#: src/pages/settings.jsx:285
msgid "Default visibility"
msgstr "Oletusnäkyvyys"

#: src/pages/settings.jsx:286
#: src/pages/settings.jsx:332
msgid "Synced"
msgstr "Synkronoituva"

#: src/pages/settings.jsx:311
msgid "Failed to update posting privacy"
msgstr "Julkaisujen näkyvyyden päivitys epäonnistui"

#: src/pages/settings.jsx:334
msgid "Synced to your instance server's settings. <0>Go to your instance ({instance}) for more settings.</0>"
msgstr "Synkronoituu palvelimesi asetuksiin. <0>Siirry instanssiisi ({instance}), jos tarvitset lisäasetuksia.</0>"

#: src/pages/settings.jsx:349
msgid "Experiments"
msgstr "Kokeelliset ominaisuudet"

#: src/pages/settings.jsx:362
msgid "Auto refresh timeline posts"
msgstr "Päivitä aikajanan julkaisut automaattisesti"

#: src/pages/settings.jsx:374
msgid "Boosts carousel"
msgstr "Tehostuskaruselli"

#: src/pages/settings.jsx:391
msgid "Post translation"
msgstr "Julkaisujen kääntäminen"

#: src/pages/settings.jsx:402
msgid "Translate to "
msgstr "Käännä kielelle "

#: src/pages/settings.jsx:413
msgid "System language ({systemTargetLanguageText})"
msgstr "Järjestelmän kieli ({systemTargetLanguageText})"

#. placeholder {0}: snapStates.settings.contentTranslationHideLanguages .length
#: src/pages/settings.jsx:439
msgid "{0, plural, =0 {Hide \"Translate\" button for:} other {Hide \"Translate\" button for (#):}}"
msgstr "{0, plural, one {}=0 {Piilota Käännä-painike kieliltä:} other {Piilota Käännä-painike kieliltä (#):}}"

#: src/pages/settings.jsx:494
msgid "Note: This feature uses external translation services, powered by <0>{TRANSLATION_API_NAME}</0>."
msgstr "Huomaa: Tämä ominaisuus käyttää ulkoisia käännöspalveluja, jotka tarjoaa <0>{TRANSLATION_API_NAME}</0>."

#: src/pages/settings.jsx:522
msgid "Auto inline translation"
msgstr "Automaattinen käännös tekstin paikalla"

#: src/pages/settings.jsx:526
msgid "Automatically show translation for posts in timeline. Only works for <0>short</0> posts without content warning, media and poll."
msgstr "Näytä julkaisujen käännökset automaattisesti aikajanalla. Toimii vain <0>lyhyille</0> julkaisuille, joissa ei ole sisältövaroitusta, mediaa eikä äänestystä."

#: src/pages/settings.jsx:547
msgid "GIF Picker for composer"
msgstr "GIF-valitsin luontikentässä"

#: src/pages/settings.jsx:551
msgid "Note: This feature uses external GIF search service, powered by <0>GIPHY</0>. G-rated (suitable for viewing by all ages), tracking parameters are stripped, referrer information is omitted from requests, but search queries and IP address information will still reach their servers."
msgstr "Huomaa: Tämä ominaisuus käyttää ulkoista GIF-hakupalvelua, jonka tarjoaa <0>GIPHY</0>. Se on G-luokiteltu (katselu sopii kaikenikäisille), seurantaparametrit poistetaan ja viittaustieto jätetään pois pyynnöistä, mutta hakukyselyt ja tieto IP-osoitteesta päätyy silti palvelun palvelimille."

#: src/pages/settings.jsx:580
msgid "Image description generator"
msgstr "Kuvan kuvausgeneraattori"

#: src/pages/settings.jsx:585
msgid "Only for new images while composing new posts."
msgstr "Vain uusille kuville, uutta julkaisua luotaessa."

#: src/pages/settings.jsx:592
msgid "Note: This feature uses external AI service, powered by <0>img-alt-api</0>. May not work well. Only for images and in English."
msgstr "Huomaa: Tämä ominaisuus käyttää ulkoista tekoälypalvelua, jonka tarjoaa <0>img-alt-api</0>. Ei välttämättä toimi hyvin. Vain kuville ja englanniksi."

#: src/pages/settings.jsx:618
msgid "Server-side grouped notifications"
msgstr "Palvelimen puolella ryhmitellyt ilmoitukset"

#: src/pages/settings.jsx:622
msgid "Alpha-stage feature. Potentially improved grouping window but basic grouping logic."
msgstr "Alfavaiheen ominaisuus. Mahdollisesti parempi ryhmittelyikkuna, mutta perustason ryhmittelylogiikka."

#: src/pages/settings.jsx:643
msgid "\"Cloud\" import/export for shortcuts settings"
msgstr "Pikavalintojen asetusten tuonti/vienti ”pilven” kautta"

#: src/pages/settings.jsx:648
msgid "⚠️⚠️⚠️ Very experimental.<0/>Stored in your own profile’s notes. Profile (private) notes are mainly used for other profiles, and hidden for own profile."
msgstr "⚠️⚠️⚠️ Erittäin kokeellinen.<0/>Tallennetaan oman profiilisi merkintään. Profiilien (yksityisiä) merkintöjä käytetään enimmäkseen muille profiileille, ja omassa profiilissa ne ovat piilossa."

#: src/pages/settings.jsx:659
msgid "Note: This feature uses currently-logged-in instance server API."
msgstr "Huomaa: Tämä ominaisuus käyttää parhaillaan kirjautuneena olevan instanssin ohjelmointirajapintaa."

#: src/pages/settings.jsx:676
msgid "Cloak mode <0>(<1>Text</1> → <2>████</2>)</0>"
msgstr "Peittotila <0>(<1>Teksti</1> → <2>██████</2>)</0>"

#: src/pages/settings.jsx:685
msgid "Replace text as blocks, useful when taking screenshots, for privacy reasons."
msgstr "Korvaa tekstin lohkoilla, hyödyllinen näyttökuvia otettaessa, yksityisyyssyistä."

#: src/pages/settings.jsx:710
msgid "About"
msgstr "Tietoja"

#: src/pages/settings.jsx:749
msgid "<0>Built</0> by <1>@cheeaun</1>"
msgstr "<0>Kehittänyt</0> <1>@cheeaun</1>"

#: src/pages/settings.jsx:778
msgid "Sponsor"
msgstr "Sponsoroi"

#: src/pages/settings.jsx:786
msgid "Donate"
msgstr "Lahjoita"

#: src/pages/settings.jsx:802
msgid "What's new"
msgstr "Mitä uutta"

#: src/pages/settings.jsx:806
msgid "Privacy Policy"
msgstr "Tietosuojakäytäntö"

#. placeholder {0}: WEBSITE.replace(/https?:\/\//g, '').replace(/\/$/, '')
#: src/pages/settings.jsx:813
msgid "<0>Site:</0> {0}"
msgstr "<0>Sivusto:</0> {0}"

#. placeholder {0}: !__FAKE_COMMIT_HASH__ && ( <span class="ib insignificant"> ( <a href={`https://github.com/cheeaun/phanpy/commit/${__COMMIT_HASH__}`} target="_blank" rel="noopener" > <RelativeTime datetime={new Date(__BUILD_TIME__)} /> </a> ) </span> )
#: src/pages/settings.jsx:820
msgid "<0>Version:</0> <1/> {0}"
msgstr "<0>Versio:</0> <1/> {0}"

#: src/pages/settings.jsx:835
msgid "Version string copied"
msgstr "Version merkkijono kopioitu"

#: src/pages/settings.jsx:838
msgid "Unable to copy version string"
msgstr "Version merkkijonoa ei voitu kopioida"

#: src/pages/settings.jsx:1103
#: src/pages/settings.jsx:1108
msgid "Failed to update subscription. Please try again."
msgstr "Tilauksen päivitys epäonnistui. Yritä uudelleen."

#: src/pages/settings.jsx:1114
msgid "Failed to remove subscription. Please try again."
msgstr "Tilauksen poisto epäonnistui. Yritä uudelleen."

#: src/pages/settings.jsx:1121
msgid "Push Notifications (beta)"
msgstr "Puskuilmoitukset (beeta)"

#: src/pages/settings.jsx:1143
msgid "Push notifications are blocked. Please enable them in your browser settings."
msgstr "Puskuilmoitukset on estetty. Ota ne käyttöön selaimesi asetuksissa."

#. placeholder {0}: [ { value: 'all', label: t`anyone`, }, { value: 'followed', label: t`people I follow`, }, { value: 'follower', label: t`followers`, }, ].map((type) => ( <option value={type.value}>{type.label}</option> ))
#: src/pages/settings.jsx:1152
msgid "Allow from <0>{0}</0>"
msgstr "Salli <0>{0}</0>"

#: src/pages/settings.jsx:1161
msgid "anyone"
msgstr "keneltä tahansa"

#: src/pages/settings.jsx:1165
msgid "people I follow"
msgstr "käyttäjiltä, joita seuraan"

#: src/pages/settings.jsx:1169
msgid "followers"
msgstr "seuraajilta"

#: src/pages/settings.jsx:1202
msgid "Follows"
msgstr "Uudet seuraajat"

#: src/pages/settings.jsx:1210
msgid "Polls"
msgstr "Äänestykset"

#: src/pages/settings.jsx:1214
msgid "Post edits"
msgstr "Julkaisujen muokkaukset"

#: src/pages/settings.jsx:1235
msgid "Push permission was not granted since your last login. You'll need to <0><1>log in</1> again to grant push permission</0>."
msgstr "Puskulupaa ei myönnetty viimeisen kirjautumisesi jälkeen. Sinun täytyy <0><1>kirjautua sisään</1> uudelleen myönteeksesi puskuluvan</0>."

#: src/pages/settings.jsx:1251
msgid "NOTE: Push notifications only work for <0>one account</0>."
msgstr "HUOMAA: Puskuilmoitukset toimivat vain <0>yhdellä tilillä</0>."

#. js-lingui-explicit-id
#: src/pages/status.jsx:657
#: src/pages/status.jsx:1220
msgid "post.title"
msgstr "Julkaisu"

#: src/pages/status.jsx:911
msgid "You're not logged in. Interactions (reply, boost, etc) are not possible."
msgstr "Et ole kirjautuneena sisään. Vuorovaikutus (vastaaminen, tehostaminen jne.) ei ole mahdollista."

#: src/pages/status.jsx:931
msgid "This post is from another instance (<0>{instance}</0>). Interactions (reply, boost, etc) are not possible."
msgstr "Tämä julkaisu on toisesta instanssista (<0>{instance}</0>). Vuorovaikutus (vastaaminen, tehostaminen jne.) ei ole mahdollista."

#: src/pages/status.jsx:959
msgid "Error: {e}"
msgstr "Virhe: {e}"

#: src/pages/status.jsx:966
msgid "Switch to my instance to enable interactions"
msgstr "Vaihda omaan instanssiin, niin saat vuorovaikutuksen käyttöön"

#: src/pages/status.jsx:1068
msgid "Unable to load replies."
msgstr "Vastauksia ei voitu ladata."

#: src/pages/status.jsx:1180
msgid "Back"
msgstr "Takaisin"

#: src/pages/status.jsx:1211
msgid "Go to main post"
msgstr "Siirry pääjulkaisuun"

#. placeholder {0}: ancestors.length
#: src/pages/status.jsx:1234
msgid "{0} posts above ‒ Go to top"
msgstr "{0} julkaisua yläpuolella – Siitty ylös"

#: src/pages/status.jsx:1281
#: src/pages/status.jsx:1344
msgid "Switch to Side Peek view"
msgstr "Vaihda sivupaneelinäkymään"

#: src/pages/status.jsx:1345
msgid "Switch to Full view"
msgstr "Vaihda täyteen näkymään"

#: src/pages/status.jsx:1363
msgid "Show all sensitive content"
msgstr "Näytä kaikki arkaluonteinen sisältö"

#: src/pages/status.jsx:1368
msgid "Experimental"
msgstr "Kokeellinen"

#: src/pages/status.jsx:1377
msgid "Unable to switch"
msgstr "Ei voitu vaihtaa"

#. placeholder {0}: punycode.toUnicode( postInstance, )
#: src/pages/status.jsx:1384
msgid "Switch to post's instance ({0})"
msgstr "Vaihda julkaisun instanssiin ({0})"

#: src/pages/status.jsx:1387
msgid "Switch to post's instance"
msgstr "Vaihda julkaisun instanssiin"

#: src/pages/status.jsx:1445
msgid "Unable to load post"
msgstr "Julkaisua ei voitu ladata"

#. placeholder {0}: replies.length
#. placeholder {1}: shortenNumber(replies.length)
#: src/pages/status.jsx:1581
msgid "{0, plural, one {# reply} other {<0>{1}</0> replies}}"
msgstr "{0, plural, one {# vastaus} other {<0>{1}</0> vastausta}}"

#. placeholder {0}: shortenNumber(totalComments)
#: src/pages/status.jsx:1599
msgid "{totalComments, plural, one {# comment} other {<0>{0}</0> comments}}"
msgstr "{totalComments, plural, one {# kommentti} other {<0>{0}</0> kommenttia}}"

#: src/pages/status.jsx:1621
msgid "View post with its replies"
msgstr "Näytä julkaisu vastauksineen"

#: src/pages/trending.jsx:77
msgid "Trending ({instance})"
msgstr "Suositut ({instance})"

#: src/pages/trending.jsx:235
msgid "Trending News"
msgstr "Suositut uutiset"

#. By [Author]
#. placeholder {0}: author ? ( <NameText account={author} showAvatar /> ) : authorUrl ? ( <a href={authorUrl} target="_blank" rel="noopener" > {authorName} </a> ) : ( authorName )
#: src/pages/trending.jsx:350
msgid "By {0}"
msgstr "Tehnyt {0}"

#: src/pages/trending.jsx:411
msgid "Back to showing trending posts"
msgstr "Takaisin katsomaan suosittuja julkaisuja"

#. placeholder {0}: currentLink .replace(/^https?:\/\/(www\.)?/i, '') .replace(/\/$/, '')
#: src/pages/trending.jsx:416
msgid "Showing posts mentioning <0>{0}</0>"
msgstr "Näytetään julkaisut, joissa mainitaan <0>{0}</0>"

#: src/pages/trending.jsx:428
msgid "Trending posts"
msgstr "Suositut julkaisut"

#: src/pages/trending.jsx:451
msgid "No trending posts."
msgstr "Ei suosittuja julkaisuja."

#: src/pages/welcome.jsx:54
msgid "A minimalistic opinionated Mastodon web client."
msgstr "Minimalistinen, omintakeinen Mastodon-selainsovellus."

#: src/pages/welcome.jsx:65
msgid "Log in with Mastodon"
msgstr "Kirjaudu sisään Mastodon-tilillä"

#: src/pages/welcome.jsx:71
msgid "Sign up"
msgstr "Rekisteröidy"

#: src/pages/welcome.jsx:78
msgid "Connect your existing Mastodon/Fediverse account.<0/>Your credentials are not stored on this server."
msgstr "Yhdistä olemassa oleva Mastodon- tai fediversumin tilisi.<0/>Kirjautumistietojasi ei tallenneta tälle palvelimelle."

#: src/pages/welcome.jsx:95
msgid "<0>Built</0> by <1>@cheeaun</1>. <2>Privacy Policy</2>."
msgstr "<0>Kehittänyt</0> <1>@cheeaun</1>. <2>Tietosuojakäytäntö</2>."

#: src/pages/welcome.jsx:126
msgid "Screenshot of Boosts Carousel"
msgstr "Näyttökuva tehostuskarusellista"

#: src/pages/welcome.jsx:130
msgid "Boosts Carousel"
msgstr "Tehostuskaruselli"

#: src/pages/welcome.jsx:133
msgid "Visually separate original posts and re-shared posts (boosted posts)."
msgstr "Erottele alkuperäiset julkaisut visuaalisesti uudelleenjaetuista (tehostetuista) julkaisuista."

#: src/pages/welcome.jsx:142
msgid "Screenshot of nested comments thread"
msgstr "Näyttökuva sisäkkäisten kommenttien ketjusta"

#: src/pages/welcome.jsx:146
msgid "Nested comments thread"
msgstr "Sisäkkäisten kommenttien ketju"

#: src/pages/welcome.jsx:149
msgid "Effortlessly follow conversations. Semi-collapsible replies."
msgstr "Seuraa keskusteluja vaivatta. Osittain kutistettavat vastaukset."

#: src/pages/welcome.jsx:157
msgid "Screenshot of grouped notifications"
msgstr "Näyttökuva ryhmitellyistä ilmoituksista"

#: src/pages/welcome.jsx:161
msgid "Grouped notifications"
msgstr "Ryhmitellyt ilmoitukset"

#: src/pages/welcome.jsx:164
msgid "Similar notifications are grouped and collapsed to reduce clutter."
msgstr "Samankaltaiset ilmoitukset ryhmitellään ja supistetaan sekavuuden vähentämiseksi."

#: src/pages/welcome.jsx:173
msgid "Screenshot of multi-column UI"
msgstr "Näyttökuva usean sarakkeen käyttöliittymästä"

#: src/pages/welcome.jsx:177
msgid "Single or multi-column"
msgstr "Yksi tai useampi sarake"

#: src/pages/welcome.jsx:180
msgid "By default, single column for zen-mode seekers. Configurable multi-column for power users."
msgstr "Oletuksena yksi sarake zen-tilan hakijoille. Määritettävissä usean sarakkeen tila tehokäyttäjille."

#: src/pages/welcome.jsx:189
msgid "Screenshot of multi-hashtag timeline with a form to add more hashtags"
msgstr "Näyttökuva usean aihetunnisteen aikajanasta, jossa on lomake uusien aihetunnisteiden lisäämiseksi"

#: src/pages/welcome.jsx:193
msgid "Multi-hashtag timeline"
msgstr "Usean aihetunnisteen aikajana"

#: src/pages/welcome.jsx:196
msgid "Up to 5 hashtags combined into a single timeline."
msgstr "Enintään 5 aihetunnistetta yhdistettynä yhdelle aikajanalle."

#: src/utils/open-compose.js:24
msgid "Looks like your browser is blocking popups."
msgstr "Selaimesi näyttää estävän ponnahdusikkunat."

#: src/utils/show-compose.js:16
msgid "A draft post is currently minimized. Post or discard it before creating a new one."
msgstr "Julkaisuluonnos on parhaillaan pienennettynä. Julkaise tai hylkää se ennen kuin luot uuden."

#: src/utils/show-compose.js:21
msgid "A post is currently open. Post or discard it before creating a new one."
msgstr "Julkaisu on parhaillaan avoinna. Julkaise tai hylkää se ennen kuin luot uuden."

