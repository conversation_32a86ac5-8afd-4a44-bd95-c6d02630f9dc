msgid ""
msgstr ""
"POT-Creation-Date: 2025-05-03 21:08+0800\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: kab\n"
"Project-Id-Version: phanpy\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-07-15 09:08\n"
"Last-Translator: \n"
"Language-Team: Kabyle\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: phanpy\n"
"X-Crowdin-Project-ID: 703337\n"
"X-Crowdin-Language: kab\n"
"X-Crowdin-File: /main/src/locales/en.po\n"
"X-Crowdin-File-ID: 18\n"

#: src/components/account-block.jsx:137
msgid "Locked"
msgstr "Isekkeṛ"

#. placeholder {0}: shortenNumber(statusesCount)
#: src/components/account-block.jsx:143
msgid "Posts: {0}"
msgstr "Tisuffaɣ: {0}"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-block.jsx:148
msgid "Last posted: {0}"
msgstr "Tasuffeɣt taneggarut: {0}"

#: src/components/account-block.jsx:163
#: src/components/account-info.jsx:710
msgid "Automated"
msgstr "Awurman"

#: src/components/account-block.jsx:170
#: src/components/account-info.jsx:715
#: src/components/status.jsx:665
msgid "Group"
msgstr "Agraw"

#: src/components/account-block.jsx:180
msgid "Mutual"
msgstr "Temṭafaṛem"

#: src/components/account-block.jsx:184
#: src/components/account-info.jsx:1875
msgid "Requested"
msgstr "Yettwasra"

#: src/components/account-block.jsx:188
#: src/components/account-info.jsx:1866
msgid "Following"
msgstr "Yeṭṭafar"

#: src/components/account-block.jsx:192
#: src/components/account-info.jsx:1180
msgid "Follows you"
msgstr "Yeṭṭafaṛ-ik·ikem"

#: src/components/account-block.jsx:200
msgid "{followersCount, plural, one {# follower} other {# followers}}"
msgstr "{followersCount, plural, one {# aneḍfar} other {# ineḍfaren}}"

#: src/components/account-block.jsx:209
#: src/components/account-info.jsx:758
msgid "Verified"
msgstr "Yettwasenqed"

#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#: src/components/account-block.jsx:224
#: src/components/account-info.jsx:860
msgid "Joined <0>{0}</0>"
msgstr "Yerna-d ass <0>{0}</0>"

#: src/components/account-info.jsx:63
msgid "Forever"
msgstr "I lebda"

#: src/components/account-info.jsx:403
msgid "Unable to load account."
msgstr "Yegguma ad d-yali umiḍan."

#: src/components/account-info.jsx:418
msgid "Go to account page"
msgstr "Ddu ɣer usebter n umiḍan"

#: src/components/account-info.jsx:447
#: src/components/account-info.jsx:780
#: src/components/account-info.jsx:810
msgid "Followers"
msgstr "Imeḍfaṛen"

#. js-lingui-explicit-id
#: src/components/account-info.jsx:451
#: src/components/account-info.jsx:820
#: src/components/account-info.jsx:839
msgid "following.stats"
msgstr ""

#: src/components/account-info.jsx:454
#: src/components/account-info.jsx:856
#: src/pages/account-statuses.jsx:487
#: src/pages/search.jsx:344
#: src/pages/search.jsx:491
msgid "Posts"
msgstr "Tisuffaɣ"

#: src/components/account-info.jsx:462
#: src/components/account-info.jsx:1236
#: src/components/compose.jsx:2792
#: src/components/media-alt-modal.jsx:55
#: src/components/media-modal.jsx:363
#: src/components/status.jsx:1915
#: src/components/status.jsx:1932
#: src/components/status.jsx:2057
#: src/components/status.jsx:2686
#: src/components/status.jsx:2689
#: src/pages/account-statuses.jsx:531
#: src/pages/accounts.jsx:118
#: src/pages/hashtag.jsx:203
#: src/pages/list.jsx:171
#: src/pages/public.jsx:116
#: src/pages/scheduled-posts.jsx:89
#: src/pages/status.jsx:1305
#: src/pages/trending.jsx:474
msgid "More"
msgstr "Ugar"

#: src/components/account-info.jsx:474
msgid "<0>{displayName}</0> has indicated that their new account is now:"
msgstr "<0>{displayName}</0> ɣur-s tura amiḍan-a amaynut:"

#: src/components/account-info.jsx:619
#: src/components/account-info.jsx:1464
msgid "Handle copied"
msgstr "Isem n useqdac yettwanɣel"

#: src/components/account-info.jsx:622
#: src/components/account-info.jsx:1467
msgid "Unable to copy handle"
msgstr "D awezɣi anɣal n yisem n useqdac"

#: src/components/account-info.jsx:628
#: src/components/account-info.jsx:1473
msgid "Copy handle"
msgstr "Nɣel isem n useqdac"

#: src/components/account-info.jsx:634
msgid "Go to original profile page"
msgstr "Ddu ɣer usebter n umaɣnu"

#: src/components/account-info.jsx:652
msgid "View profile image"
msgstr "Wali tugna n umaɣnu"

#: src/components/account-info.jsx:670
msgid "View profile header"
msgstr "Wali aqerru n umaɣnu"

#: src/components/account-info.jsx:686
#: src/components/account-info.jsx:1774
#: src/components/account-info.jsx:2300
msgid "Edit profile"
msgstr "Ẓreg amaɣnu"

#: src/components/account-info.jsx:705
msgid "In Memoriam"
msgstr "In Memoriam"

#: src/components/account-info.jsx:787
#: src/components/account-info.jsx:830
msgid "This user has chosen to not make this information available."
msgstr "Aseqdac-a yefren ur tettili ara telɣut-a."

#. placeholder {0}: ( postingStats.originals / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {1}: ( postingStats.replies / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {2}: ( postingStats.boosts / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#: src/components/account-info.jsx:885
msgid "{0} original posts, {1} replies, {2} boosts"
msgstr "{0} tisuffaɣ tiɣbula, {1} tiririyin, {2} izuzar"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.daysSinceLastPost
#. placeholder {2}: postingStats.daysSinceLastPost
#. placeholder {3}: postingStats.daysSinceLastPost
#. placeholder {4}: postingStats.total
#. placeholder {5}: postingStats.total
#. placeholder {6}: postingStats.daysSinceLastPost
#: src/components/account-info.jsx:901
msgid "{0, plural, one {{1, plural, one {Last 1 post in the past 1 day} other {Last 1 post in the past {2} days}}} other {{3, plural, one {Last {4} posts in the past 1 day} other {Last {5} posts in the past {6} days}}}}"
msgstr "{0, plural, one {{1, plural, one {Tasuffeɣt taneggurt i wass-a} other {Tasuffeɣt taneggarut deg {2} wussan-a yezrin}}} other {{3, plural, one {{4} tsuffaɣ tineggura i wass-a} other {{5} tsuffaɣ tineggura deg {6} wussan-a yezrin}}}}"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.total
#: src/components/account-info.jsx:917
msgid "{0, plural, one {Last 1 post in the past year(s)} other {Last {1} posts in the past year(s)}}"
msgstr "{0, plural, one {1 tsuffeɣt taneggarut deg yiseggasen-a ineggura} other {{1} tsuffaɣ deg yiseggasen-a ineggura}}"

#: src/components/account-info.jsx:942
#: src/pages/catchup.jsx:70
msgid "Original"
msgstr "Aɣbalu"

#: src/components/account-info.jsx:946
#: src/components/status.jsx:2470
#: src/pages/catchup.jsx:71
#: src/pages/catchup.jsx:1448
#: src/pages/catchup.jsx:2061
#: src/pages/status.jsx:1024
#: src/pages/status.jsx:1651
msgid "Replies"
msgstr "Tiririt"

#: src/components/account-info.jsx:950
#: src/pages/catchup.jsx:72
#: src/pages/catchup.jsx:1450
#: src/pages/catchup.jsx:2073
#: src/pages/settings.jsx:1198
msgid "Boosts"
msgstr "Izuzar"

#: src/components/account-info.jsx:956
msgid "Post stats unavailable."
msgstr "Tidaddanin n yizen-a ulac-itent."

#: src/components/account-info.jsx:987
msgid "View post stats"
msgstr "Wali tidaddanin n tsuffeɣt"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-info.jsx:1184
msgid "Last post: <0>{0}</0>"
msgstr "Tasuffeɣt taneggarut: <0>{0}</0>"

#: src/components/account-info.jsx:1198
msgid "Muted"
msgstr "Yettwasgugem"

#: src/components/account-info.jsx:1203
msgid "Blocked"
msgstr "Yettusewḥel"

#: src/components/account-info.jsx:1212
msgid "Private note"
msgstr "Tazmilt tusligt"

#: src/components/account-info.jsx:1269
msgid "Mention <0>@{username}</0>"
msgstr "Bder <0>@{username}</0>"

#: src/components/account-info.jsx:1281
msgid "Translate bio"
msgstr "Suqel tudert"

#: src/components/account-info.jsx:1292
msgid "Edit private note"
msgstr "Ẓreg tazmilt tusligt"

#: src/components/account-info.jsx:1292
msgid "Add private note"
msgstr "Rnu tazmilt tusligt"

#: src/components/account-info.jsx:1312
msgid "Notifications enabled for @{username}'s posts."
msgstr ""

#: src/components/account-info.jsx:1313
msgid " Notifications disabled for @{username}'s posts."
msgstr ""

#: src/components/account-info.jsx:1325
msgid "Disable notifications"
msgstr "Sens ulɣuten"

#: src/components/account-info.jsx:1326
msgid "Enable notifications"
msgstr "Rmed ulɣuten"

#: src/components/account-info.jsx:1343
msgid "Boosts from @{username} enabled."
msgstr "Izuzar s-ɣur @{username} ttwaremden."

#: src/components/account-info.jsx:1344
msgid "Boosts from @{username} disabled."
msgstr "Izuzar s-ɣur @{username} ttwasensen."

#: src/components/account-info.jsx:1355
msgid "Disable boosts"
msgstr "Sens izuzar"

#: src/components/account-info.jsx:1355
msgid "Enable boosts"
msgstr "Sermed izuzar"

#: src/components/account-info.jsx:1374
msgid "@{username} is no longer featured on your profile."
msgstr ""

#: src/components/account-info.jsx:1384
msgid "@{username} is now featured on your profile."
msgstr ""

#: src/components/account-info.jsx:1392
msgid "Unable to unfeature @{username} on your profile."
msgstr ""

#: src/components/account-info.jsx:1396
msgid "Unable to feature @{username} on your profile."
msgstr ""

#: src/components/account-info.jsx:1405
msgid "Don't feature on profile"
msgstr ""

#: src/components/account-info.jsx:1406
#: src/pages/hashtag.jsx:333
msgid "Feature on profile"
msgstr "Welleh fell-as deg umaɣnu-k"

#: src/components/account-info.jsx:1415
msgid "Show featured profiles"
msgstr ""

#: src/components/account-info.jsx:1430
#: src/components/account-info.jsx:1440
#: src/components/account-info.jsx:2046
msgid "Add/Remove from Lists"
msgstr "Rnu/Kkes seg tebdarin"

#: src/components/account-info.jsx:1490
#: src/components/status.jsx:1337
msgid "Link copied"
msgstr "Yettwanɣel wasaɣ"

#: src/components/account-info.jsx:1493
#: src/components/status.jsx:1340
msgid "Unable to copy link"
msgstr "D awezɣi ad d-yenɣel useɣwen"

#: src/components/account-info.jsx:1499
#: src/components/shortcuts-settings.jsx:1059
#: src/components/status.jsx:1346
#: src/components/status.jsx:3464
msgid "Copy"
msgstr "Nɣel"

#: src/components/account-info.jsx:1514
#: src/components/shortcuts-settings.jsx:1077
#: src/components/status.jsx:1362
msgid "Sharing doesn't seem to work."
msgstr "Beṭṭu yettban ur yeddi ara."

#: src/components/account-info.jsx:1520
#: src/components/status.jsx:1368
msgid "Share…"
msgstr "Bḍu…"

#: src/components/account-info.jsx:1540
msgid "Unmuted @{username}"
msgstr "Yettwakkes usgugem ɣef @{username}"

#: src/components/account-info.jsx:1552
msgid "Unmute <0>@{username}</0>"
msgstr "Kkes asgugem <0>@{username}</0>"

#: src/components/account-info.jsx:1568
msgid "Mute <0>@{username}</0>…"
msgstr "Sgugem <0>@{username}</0>…"

#. placeholder {0}: typeof MUTE_DURATIONS_LABELS[duration] === 'function' ? MUTE_DURATIONS_LABELS[duration]() : _(MUTE_DURATIONS_LABELS[duration])
#: src/components/account-info.jsx:1600
msgid "Muted @{username} for {0}"
msgstr "Sgugem @{username} i {0}"

#: src/components/account-info.jsx:1612
msgid "Unable to mute @{username}"
msgstr "D awezɣi asgugem n @{username}"

#: src/components/account-info.jsx:1633
msgid "Remove <0>@{username}</0> from followers?"
msgstr "Kkes <0>@{username}</0> seg yineḍfaren?"

#: src/components/account-info.jsx:1653
msgid "@{username} removed from followers"
msgstr "@{username} yettwakkes seg yineḍfaren"

#: src/components/account-info.jsx:1665
msgid "Remove follower…"
msgstr "Kkes aneḍfar…"

#: src/components/account-info.jsx:1676
msgid "Block <0>@{username}</0>?"
msgstr "Sewḥel <0>@{username}</0>?"

#: src/components/account-info.jsx:1700
msgid "Unblocked @{username}"
msgstr "Yettwakkes usewḥel ɣef @{username}"

#: src/components/account-info.jsx:1708
msgid "Blocked @{username}"
msgstr "Yettusewḥel @{username}"

#: src/components/account-info.jsx:1716
msgid "Unable to unblock @{username}"
msgstr "D awezɣi tukksa n usewḥel ɣef @{username}"

#: src/components/account-info.jsx:1718
msgid "Unable to block @{username}"
msgstr "D awezɣi asewḥel n @{username}"

#: src/components/account-info.jsx:1728
msgid "Unblock <0>@{username}</0>"
msgstr "Kkes asewḥel <0>@{username}</0>"

#: src/components/account-info.jsx:1737
msgid "Block <0>@{username}</0>…"
msgstr "Sewḥel <0>@{username}</0>…"

#: src/components/account-info.jsx:1754
msgid "Report <0>@{username}</0>…"
msgstr "Cetki ɣef <0>@{username}</0>…"

#: src/components/account-info.jsx:1810
msgid "Withdraw follow request?"
msgstr ""

#. placeholder {0}: info.acct || info.username
#: src/components/account-info.jsx:1811
msgid "Unfollow @{0}?"
msgstr "Ur ṭṭafar ara @{0}?"

#: src/components/account-info.jsx:1869
msgid "Unfollow…"
msgstr "Ur ṭṭafar ara…"

#: src/components/account-info.jsx:1878
msgid "Withdraw…"
msgstr "Kkes…"

#: src/components/account-info.jsx:1885
#: src/components/account-info.jsx:1889
#: src/pages/hashtag.jsx:265
msgid "Follow"
msgstr "Ḍfeṛ"

#: src/components/account-info.jsx:1986
#: src/components/account-info.jsx:2041
#: src/components/account-info.jsx:2175
#: src/components/account-info.jsx:2295
#: src/components/account-sheet.jsx:38
#: src/components/compose.jsx:891
#: src/components/compose.jsx:2748
#: src/components/compose.jsx:3228
#: src/components/compose.jsx:3437
#: src/components/compose.jsx:3667
#: src/components/drafts.jsx:59
#: src/components/embed-modal.jsx:13
#: src/components/generic-accounts.jsx:151
#: src/components/keyboard-shortcuts-help.jsx:43
#: src/components/list-add-edit.jsx:37
#: src/components/media-alt-modal.jsx:43
#: src/components/media-modal.jsx:327
#: src/components/notification-service.jsx:157
#: src/components/report-modal.jsx:118
#: src/components/shortcuts-settings.jsx:230
#: src/components/shortcuts-settings.jsx:583
#: src/components/shortcuts-settings.jsx:783
#: src/components/status.jsx:3188
#: src/components/status.jsx:3428
#: src/components/status.jsx:3937
#: src/pages/accounts.jsx:45
#: src/pages/catchup.jsx:1584
#: src/pages/filters.jsx:225
#: src/pages/list.jsx:302
#: src/pages/notifications.jsx:942
#: src/pages/scheduled-posts.jsx:259
#: src/pages/settings.jsx:90
#: src/pages/status.jsx:1392
msgid "Close"
msgstr "Mdel"

#: src/components/account-info.jsx:1991
msgid "Translated Bio"
msgstr "Tettwasuqqel tudert"

#: src/components/account-info.jsx:2086
msgid "Unable to remove from list."
msgstr "D awezɣi ad yettwakkes seg tebdart."

#: src/components/account-info.jsx:2087
msgid "Unable to add to list."
msgstr "D awezɣi ad yettwarnu ɣer tebdart."

#: src/components/account-info.jsx:2106
#: src/pages/lists.jsx:131
msgid "Unable to load lists."
msgstr "D awezɣi ad d-alint tebdarin."

#: src/components/account-info.jsx:2110
msgid "No lists."
msgstr "Ulac tibdarin."

#: src/components/account-info.jsx:2121
#: src/components/list-add-edit.jsx:41
#: src/pages/lists.jsx:62
msgid "New list"
msgstr "Tabdart tamaynutt"

#. placeholder {0}: account?.username || account?.acct
#: src/components/account-info.jsx:2180
msgid "Private note about <0>@{0}</0>"
msgstr "Tazmilt tusligt ɣef <0>@{0}</0>"

#: src/components/account-info.jsx:2210
msgid "Unable to update private note."
msgstr "D awezɣi aleqqem n tezmilt tusligt."

#: src/components/account-info.jsx:2233
#: src/components/account-info.jsx:2531
msgid "Cancel"
msgstr "Sefsex"

#: src/components/account-info.jsx:2238
msgid "Save & close"
msgstr "Sekles sakkin mdel"

#: src/components/account-info.jsx:2355
msgid "Unable to update profile."
msgstr "D awezɣi aleqqem n umaɣnu."

#: src/components/account-info.jsx:2362
msgid "Header picture"
msgstr "Tugna n tqacuct"

#: src/components/account-info.jsx:2414
msgid "Profile picture"
msgstr "Tugna n umaɣnu"

#: src/components/account-info.jsx:2466
#: src/components/list-add-edit.jsx:106
msgid "Name"
msgstr "Isem"

#: src/components/account-info.jsx:2479
msgid "Bio"
msgstr "Tameddurt"

#: src/components/account-info.jsx:2492
msgid "Extra fields"
msgstr "Urtiyen niḍen"

#: src/components/account-info.jsx:2498
msgid "Label"
msgstr "Tabzimt"

#: src/components/account-info.jsx:2501
msgid "Content"
msgstr "Agbur"

#: src/components/account-info.jsx:2534
#: src/components/list-add-edit.jsx:152
#: src/components/shortcuts-settings.jsx:715
#: src/pages/filters.jsx:570
#: src/pages/notifications.jsx:1008
msgid "Save"
msgstr "Sekles"

#: src/components/account-info.jsx:2588
msgid "username"
msgstr "isem n useqdac"

#: src/components/account-info.jsx:2592
msgid "server domain name"
msgstr "isem n taɣult n uqeddac"

#. placeholder {0}: info.username
#: src/components/account-info.jsx:2658
msgid "Profiles featured by @{0}"
msgstr ""

#: src/components/account-info.jsx:2684
msgid "No featured profiles."
msgstr ""

#: src/components/background-service.jsx:158
msgid "Cloak mode disabled"
msgstr "Askar uffir yensa"

#: src/components/background-service.jsx:158
msgid "Cloak mode enabled"
msgstr "Askar uffir yermed"

#: src/components/columns.jsx:27
#: src/components/nav-menu.jsx:181
#: src/components/shortcuts-settings.jsx:139
#: src/components/timeline.jsx:469
#: src/pages/catchup.jsx:883
#: src/pages/filters.jsx:90
#: src/pages/followed-hashtags.jsx:41
#: src/pages/home.jsx:54
#: src/pages/notifications.jsx:587
#: src/pages/scheduled-posts.jsx:74
msgid "Home"
msgstr "Agejdan"

#: src/components/compose-button.jsx:150
#: src/compose.jsx:38
msgid "Compose"
msgstr "Suddes"

#: src/components/compose-button.jsx:177
#: src/components/nav-menu.jsx:265
#: src/pages/scheduled-posts.jsx:31
#: src/pages/scheduled-posts.jsx:78
msgid "Scheduled Posts"
msgstr ""

#: src/components/compose-button.jsx:190
msgid "Add to thread"
msgstr "Rnu ar usqerdec"

#: src/components/compose.jsx:211
msgid "Take photo or video"
msgstr "Ṭṭef tawlaft neɣ tavidyut"

#: src/components/compose.jsx:212
msgid "Add media"
msgstr "Rnu amidya"

#: src/components/compose.jsx:213
msgid "Add custom emoji"
msgstr "Timerna n imuji udmawan"

#: src/components/compose.jsx:214
msgid "Add GIF"
msgstr "Rnu tugna GIF"

#: src/components/compose.jsx:215
msgid "Add poll"
msgstr "Rnu asisten"

#: src/components/compose.jsx:216
msgid "Schedule post"
msgstr ""

#: src/components/compose.jsx:416
msgid "You have unsaved changes. Discard this post?"
msgstr "Ɣur-k isenfal ur yettwaskelsen ara. Sefsex tasuffeɣt-a?"

#. placeholder {0}: unsupportedFiles.length
#. placeholder {1}: unsupportedFiles[0].name
#. placeholder {2}: lf.format( unsupportedFiles.map((f) => f.name), )
#: src/components/compose.jsx:654
msgid "{0, plural, one {File {1} is not supported.} other {Files {2} are not supported.}}"
msgstr ""

#: src/components/compose.jsx:664
#: src/components/compose.jsx:682
#: src/components/compose.jsx:1794
#: src/components/compose.jsx:1919
msgid "{maxMediaAttachments, plural, one {You can only attach up to 1 file.} other {You can only attach up to # files.}}"
msgstr "{maxMediaAttachments, plural, one {Tzemreḍ ad tsedduḍ 1 ufaylu kan.} other {Tzemreḍ ad tsedduḍ # yifuyla.}}"

#: src/components/compose.jsx:872
msgid "Pop out"
msgstr "Ldi deg ufaylu udhim"

#: src/components/compose.jsx:879
msgid "Minimize"
msgstr "Semẓẓi"

#: src/components/compose.jsx:915
msgid "Looks like you closed the parent window."
msgstr "Waqil tmedleḍ asfaylu amaraw."

#: src/components/compose.jsx:922
msgid "Looks like you already have a compose field open in the parent window and currently publishing. Please wait for it to be done and try again later."
msgstr "Yettban-d ɣur-k yakan urti ideg turiḍ yeldi deg usfaylu amaraw, ha-t-an ad t-id-tessuffɣeḍ. Ttxil-k, ṛǧu ad yemmed syen εreḍ ticki."

#: src/components/compose.jsx:927
msgid "Looks like you already have a compose field open in the parent window. Popping in this window will discard the changes you made in the parent window. Continue?"
msgstr "Yettban tesεiḍ yakan urti ideg turiḍ yeldi deg usfaylu amaraw. Tiririt n usfaylu-a ad issefsex tira n tsuffeɣt deg usfaylu-a war asekles. Kemmel?"

#: src/components/compose.jsx:970
msgid "Pop in"
msgstr "Err-d seg usfaylu agejdan"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#. placeholder {1}: rtf.format(-replyToStatusMonthsAgo, 'month')
#: src/components/compose.jsx:980
msgid "Replying to @{0}’s post (<0>{1}</0>)"
msgstr "Tiririt ɣef tsuffeɣt n @{0} (<0>{1}</0>)"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#: src/components/compose.jsx:990
msgid "Replying to @{0}’s post"
msgstr "Tiririt ɣef tsuffeɣt n @{0}"

#: src/components/compose.jsx:1003
msgid "Editing source post"
msgstr "Aẓrag n tsuffeɣt n uɣbalu"

#: src/components/compose.jsx:1056
msgid "Poll must have at least 2 options"
msgstr "Asisten ilaq ad yesεu ma drus snat textiṛiyin"

#: src/components/compose.jsx:1060
msgid "Some poll choices are empty"
msgstr "Kra n yifranen n usisten d ilmawen"

#: src/components/compose.jsx:1073
msgid "Some media have no descriptions. Continue?"
msgstr "Kra yimidyaten ulac ɣer-sen aglam. Kemmel?"

#: src/components/compose.jsx:1125
msgid "Attachment #{i} failed"
msgstr "Amedday #{i} yecceḍ"

#: src/components/compose.jsx:1221
#: src/components/status.jsx:2245
#: src/components/timeline.jsx:1023
msgid "Content warning"
msgstr "Yir agbur"

#: src/components/compose.jsx:1237
msgid "Content warning or sensitive media"
msgstr "Yir agbur neɣ amidya amḥulfu"

#: src/components/compose.jsx:1273
#: src/components/status.jsx:99
#: src/pages/settings.jsx:318
msgid "Public"
msgstr "Azayaz"

#: src/components/compose.jsx:1278
#: src/components/nav-menu.jsx:349
#: src/components/shortcuts-settings.jsx:165
#: src/components/status.jsx:100
msgid "Local"
msgstr "Adigan"

#: src/components/compose.jsx:1282
#: src/components/status.jsx:101
#: src/pages/settings.jsx:321
msgid "Unlisted"
msgstr "War abdar"

#: src/components/compose.jsx:1285
#: src/components/status.jsx:102
#: src/pages/settings.jsx:324
msgid "Followers only"
msgstr "Imeḍfaṛen kan"

#: src/components/compose.jsx:1288
#: src/components/status.jsx:103
#: src/components/status.jsx:2121
msgid "Private mention"
msgstr "Abdar uslig"

#: src/components/compose.jsx:1297
msgid "Post your reply"
msgstr "Suffeɣ tiririt-ik·im"

#: src/components/compose.jsx:1299
msgid "Edit your post"
msgstr "Ẓreg tasuffeɣt-ik·im"

#: src/components/compose.jsx:1300
msgid "What are you doing?"
msgstr "Acu i txeddmeḍ?"

#: src/components/compose.jsx:1379
msgid "Mark media as sensitive"
msgstr "Creḍ allal n teywalt d anafri"

#: src/components/compose.jsx:1416
msgid "Posting on <0/>"
msgstr "Teffɣed ass n <0/>"

#: src/components/compose.jsx:1447
#: src/components/compose.jsx:3286
#: src/components/shortcuts-settings.jsx:715
#: src/pages/list.jsx:388
msgid "Add"
msgstr "Rnu"

#: src/components/compose.jsx:1675
msgid "Schedule"
msgstr "Sɣiwes"

#: src/components/compose.jsx:1677
#: src/components/keyboard-shortcuts-help.jsx:155
#: src/components/status.jsx:1109
#: src/components/status.jsx:1895
#: src/components/status.jsx:1896
#: src/components/status.jsx:2590
msgid "Reply"
msgstr "Err"

#: src/components/compose.jsx:1679
msgid "Update"
msgstr "Leqqem"

#: src/components/compose.jsx:1680
msgctxt "Submit button in composer"
msgid "Post"
msgstr "Suffeɣ"

#: src/components/compose.jsx:1806
msgid "Downloading GIF…"
msgstr "Asader n GIF…"

#: src/components/compose.jsx:1834
msgid "Failed to download GIF"
msgstr "Yecceḍ usader n GIF"

#: src/components/compose.jsx:2049
#: src/components/compose.jsx:2126
#: src/components/nav-menu.jsx:244
msgid "More…"
msgstr "Ugar…"

#: src/components/compose.jsx:2562
msgid "Uploaded"
msgstr "Yuli-d"

#: src/components/compose.jsx:2575
msgid "Image description"
msgstr "Aglam n tugna"

#: src/components/compose.jsx:2576
msgid "Video description"
msgstr "Aglam n tvidyutt"

#: src/components/compose.jsx:2577
msgid "Audio description"
msgstr "Aglam n useklas ameslaw"

#. placeholder {0}: prettyBytes( imageSize, )
#. placeholder {0}: prettyBytes( videoSize, )
#. placeholder {1}: prettyBytes(imageSizeLimit)
#. placeholder {1}: prettyBytes(videoSizeLimit)
#: src/components/compose.jsx:2612
#: src/components/compose.jsx:2632
msgid "File size too large. Uploading might encounter issues. Try reduce the file size from {0} to {1} or lower."
msgstr "Teɣzi n ufaylu meqqret aṭas. asali-ines yezmer ad yesεu uguren. Ԑreḍ ad tesneqseḍ deg teɣzi seg {0} ɣer {1} neɣ ugar."

#. placeholder {0}: i18n.number( width, )
#. placeholder {1}: i18n.number(height)
#. placeholder {2}: i18n.number(newWidth)
#. placeholder {3}: i18n.number( newHeight, )
#: src/components/compose.jsx:2624
#: src/components/compose.jsx:2644
msgid "Dimension too large. Uploading might encounter issues. Try reduce dimension from {0}×{1}px to {2}×{3}px."
msgstr ""

#: src/components/compose.jsx:2652
msgid "Frame rate too high. Uploading might encounter issues."
msgstr "Afmiḍi meqqer aṭas. Asali-s yezmer ad d-yeglu s wuguren."

#: src/components/compose.jsx:2712
#: src/components/compose.jsx:2962
#: src/components/shortcuts-settings.jsx:726
#: src/pages/catchup.jsx:1081
#: src/pages/filters.jsx:413
msgid "Remove"
msgstr "Kkes"

#: src/components/compose.jsx:2729
#: src/compose.jsx:84
msgid "Error"
msgstr "Tuccḍa"

#: src/components/compose.jsx:2754
msgid "Edit image description"
msgstr "Ẓreg aglam n tugna"

#: src/components/compose.jsx:2755
msgid "Edit video description"
msgstr "Ẓreg aglam n tvidyut"

#: src/components/compose.jsx:2756
msgid "Edit audio description"
msgstr "Ẓreg aglam n useklas ameslaw"

#: src/components/compose.jsx:2801
#: src/components/compose.jsx:2850
msgid "Generating description. Please wait…"
msgstr "Asirew n uglam. Ttxil-k ṛǧu…"

#. placeholder {0}: e.message
#: src/components/compose.jsx:2821
msgid "Failed to generate description: {0}"
msgstr ""

#: src/components/compose.jsx:2822
msgid "Failed to generate description"
msgstr "Yecceḍ usirew n uglam"

#: src/components/compose.jsx:2834
#: src/components/compose.jsx:2840
#: src/components/compose.jsx:2886
msgid "Generate description…"
msgstr "Sirew aglam…"

#. placeholder {0}: e?.message ? `: ${e.message}` : ''
#: src/components/compose.jsx:2873
msgid "Failed to generate description{0}"
msgstr ""

#. placeholder {0}: localeCode2Text(lang)
#: src/components/compose.jsx:2888
msgid "({0}) <0>— experimental</0>"
msgstr "({0}) <0>— armitan</0>"

#: src/components/compose.jsx:2907
msgid "Done"
msgstr "Yemmed"

#. placeholder {0}: i + 1
#: src/components/compose.jsx:2943
msgid "Choice {0}"
msgstr "Afran {0}"

#: src/components/compose.jsx:2990
msgid "Multiple choices"
msgstr "Aṭas n tferniwin"

#: src/components/compose.jsx:2993
msgid "Duration"
msgstr "Tanzagt"

#: src/components/compose.jsx:3024
msgid "Remove poll"
msgstr "Kkes afmiḍi"

#: src/components/compose.jsx:3245
msgid "Search accounts"
msgstr "Nadi imiḍanen"

#: src/components/compose.jsx:3299
#: src/components/generic-accounts.jsx:236
msgid "Error loading accounts"
msgstr "Tuccḍa deg usali n imiḍanen"

#: src/components/compose.jsx:3443
msgid "Custom emojis"
msgstr "Imujiten udmawanen"

#: src/components/compose.jsx:3463
msgid "Search emoji"
msgstr "Nadi imujiten"

#: src/components/compose.jsx:3494
msgid "Error loading custom emojis"
msgstr "Tuccḍa deg usali n yimujiten udmawanen"

#: src/components/compose.jsx:3505
msgid "Recently used"
msgstr "Wid yettwasqdacen melmi kan"

#: src/components/compose.jsx:3506
msgid "Others"
msgstr "Wiyyaḍ"

#. placeholder {0}: i18n.number(emojis.length - max)
#: src/components/compose.jsx:3544
msgid "{0} more…"
msgstr "{0} d wugar…"

#: src/components/compose.jsx:3682
msgid "Search GIFs"
msgstr "Nadi tugniwin GIF"

#: src/components/compose.jsx:3697
msgid "Powered by GIPHY"
msgstr "S lmendad n GIPHY"

#: src/components/compose.jsx:3705
msgid "Type to search GIFs"
msgstr "Aru i unadi n GIFs"

#: src/components/compose.jsx:3803
#: src/components/media-modal.jsx:469
#: src/components/timeline.jsx:927
msgid "Previous"
msgstr "Uzwir"

#: src/components/compose.jsx:3821
#: src/components/media-modal.jsx:488
#: src/components/timeline.jsx:944
msgid "Next"
msgstr "Uḍfir"

#: src/components/compose.jsx:3838
msgid "Error loading GIFs"
msgstr "Tuccḍa deg usali GIFs"

#: src/components/drafts.jsx:64
#: src/pages/settings.jsx:702
msgid "Unsent drafts"
msgstr "Ur ttazen ara irewwayen"

#: src/components/drafts.jsx:69
msgid "Looks like you have unsent drafts. Let's continue where you left off."
msgstr "Yettban ɣur-k irewwayen ur yettwaznen ara. Kemmel ansi i ten-teǧǧiḍ."

#: src/components/drafts.jsx:103
msgid "Delete this draft?"
msgstr "Kkes arewway-a?"

#: src/components/drafts.jsx:118
msgid "Error deleting draft! Please try again."
msgstr "Tuccḍa deg tukksa n urewway! Ttxil εreḍ tikkelt niḍen."

#: src/components/drafts.jsx:128
#: src/components/list-add-edit.jsx:188
#: src/components/status.jsx:1512
#: src/pages/filters.jsx:603
#: src/pages/scheduled-posts.jsx:369
msgid "Delete…"
msgstr "Kkes…"

#: src/components/drafts.jsx:147
msgid "Error fetching reply-to status!"
msgstr "Tuccḍa deg tririt n waddad n tririt!"

#: src/components/drafts.jsx:172
msgid "Delete all drafts?"
msgstr "Kkes akk irewwayen?"

#: src/components/drafts.jsx:190
msgid "Error deleting drafts! Please try again."
msgstr "Tuccḍa deg tukksa n yirewwayen! Ttxil εreḍ tikkelt niḍen."

#: src/components/drafts.jsx:202
msgid "Delete all…"
msgstr "Kkes-iten akk…"

#: src/components/drafts.jsx:210
msgid "No drafts found."
msgstr "Ulac irewwayen i yettwafen."

#: src/components/drafts.jsx:247
#: src/pages/catchup.jsx:1932
msgid "Poll"
msgstr "Asisten"

#: src/components/drafts.jsx:250
#: src/pages/account-statuses.jsx:367
msgid "Media"
msgstr "Amidya"

#: src/components/embed-modal.jsx:18
msgid "Open in new window"
msgstr "Ldi deg usfaylu amaynut"

#: src/components/follow-request-buttons.jsx:43
#: src/pages/notifications.jsx:992
msgid "Accept"
msgstr "Qbel"

#: src/components/follow-request-buttons.jsx:69
msgid "Reject"
msgstr "Agi"

#: src/components/follow-request-buttons.jsx:76
#: src/pages/notifications.jsx:1276
msgid "Accepted"
msgstr "Yettwaqbal"

#: src/components/follow-request-buttons.jsx:80
msgid "Rejected"
msgstr "Yettwagi"

#: src/components/generic-accounts.jsx:154
#: src/components/notification.jsx:455
#: src/pages/accounts.jsx:50
#: src/pages/search.jsx:334
#: src/pages/search.jsx:367
msgid "Accounts"
msgstr "Imiḍanen"

#: src/components/generic-accounts.jsx:214
#: src/components/timeline.jsx:551
#: src/pages/list.jsx:321
#: src/pages/notifications.jsx:922
#: src/pages/search.jsx:561
#: src/pages/status.jsx:1425
msgid "Show more…"
msgstr "Sken-d ugar…"

#: src/components/generic-accounts.jsx:219
#: src/components/timeline.jsx:556
#: src/pages/search.jsx:566
msgid "The end."
msgstr "Taggara."

#: src/components/generic-accounts.jsx:240
msgid "Nothing to show"
msgstr "Ulac ara yettwaskanen"

#: src/components/keyboard-shortcuts-help.jsx:47
#: src/components/nav-menu.jsx:368
#: src/pages/catchup.jsx:1622
msgid "Keyboard shortcuts"
msgstr "Inegzumen n unasiw"

#: src/components/keyboard-shortcuts-help.jsx:55
msgid "Keyboard shortcuts help"
msgstr "Tallelt n yinegzumen n unasiw"

#: src/components/keyboard-shortcuts-help.jsx:59
#: src/pages/catchup.jsx:1647
msgid "Next post"
msgstr "Tasuffeɣt tuḍfirt"

#: src/components/keyboard-shortcuts-help.jsx:63
#: src/pages/catchup.jsx:1655
msgid "Previous post"
msgstr "Tasuffeɣt iɛeddan"

#: src/components/keyboard-shortcuts-help.jsx:67
msgid "Skip carousel to next post"
msgstr "Zgel akarusel ɣer tsuffeɣt tuḍfirt"

#: src/components/keyboard-shortcuts-help.jsx:69
msgid "<0>Shift</0> + <1>j</1>"
msgstr "<0>Shift</0> + <1>j</1>"

#: src/components/keyboard-shortcuts-help.jsx:75
msgid "Skip carousel to previous post"
msgstr "Zgel akarusel ɣer tsuffeɣt tudfirt"

#: src/components/keyboard-shortcuts-help.jsx:77
msgid "<0>Shift</0> + <1>k</1>"
msgstr "<0>Shift</0> + <1>k</1>"

#: src/components/keyboard-shortcuts-help.jsx:83
msgid "Load new posts"
msgstr "Sali-d tisuffaɣ timaynutin"

#: src/components/keyboard-shortcuts-help.jsx:87
#: src/pages/catchup.jsx:1679
msgid "Open post details"
msgstr "Ldi talqayt n tsuffeɣt"

#: src/components/keyboard-shortcuts-help.jsx:89
msgid "<0>Enter</0> or <1>o</1>"
msgstr "<0>Sekcem</0> neɣ <1>o</1>"

#: src/components/keyboard-shortcuts-help.jsx:96
msgid "Expand content warning or<0/>toggle expanded/collapsed thread"
msgstr "Alɣu ɣef usumɣer n ugbur neɣ <0/>abeddel n usqerdec semɣer/semẓẓi"

#: src/components/keyboard-shortcuts-help.jsx:105
msgid "Close post or dialogs"
msgstr "Mdel tasuffeɣt neɣ idiwenniyen"

#: src/components/keyboard-shortcuts-help.jsx:107
msgid "<0>Esc</0> or <1>Backspace</1>"
msgstr "<0>Esc</0> neɣ <1>Backspace</1>"

#: src/components/keyboard-shortcuts-help.jsx:113
msgid "Focus column in multi-column mode"
msgstr "Tigejdit Focus deg uskar n waṭas n tgejda"

#: src/components/keyboard-shortcuts-help.jsx:115
msgid "<0>1</0> to <1>9</1>"
msgstr "<0>1</0> ɣer <1>9</1>"

#: src/components/keyboard-shortcuts-help.jsx:121
msgid "Focus next column in multi-column mode"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:125
msgid "Focus previous column in multi-column mode"
msgstr ""

#: src/components/keyboard-shortcuts-help.jsx:129
msgid "Compose new post"
msgstr "Aru Tasuffeɣt tamaynut"

#: src/components/keyboard-shortcuts-help.jsx:133
msgid "Compose new post (new window)"
msgstr "Aru tasuffeɣt tamaynut (asfaylu amaynut)"

#: src/components/keyboard-shortcuts-help.jsx:136
msgid "<0>Shift</0> + <1>c</1>"
msgstr "<0>Shift</0> + <1>c</1>"

#: src/components/keyboard-shortcuts-help.jsx:142
msgid "Send post"
msgstr "Azen tasuffeɣt"

#: src/components/keyboard-shortcuts-help.jsx:144
msgid "<0>Ctrl</0> + <1>Enter</1> or <2>⌘</2> + <3>Enter</3>"
msgstr "<0>Ctrl</0> + <1>Enter</1> neɣ <2>⌘</2> + <3>Enter</3>"

#: src/components/keyboard-shortcuts-help.jsx:151
#: src/components/nav-menu.jsx:337
#: src/components/search-form.jsx:73
#: src/components/shortcuts-settings.jsx:52
#: src/components/shortcuts-settings.jsx:179
#: src/pages/search.jsx:46
#: src/pages/search.jsx:316
msgid "Search"
msgstr "Nadi"

#: src/components/keyboard-shortcuts-help.jsx:159
msgid "Reply (new window)"
msgstr "Err (asfaylu amaynut)"

#: src/components/keyboard-shortcuts-help.jsx:162
msgid "<0>Shift</0> + <1>r</1>"
msgstr "<0>Shift</0> + <1>r</1>"

#: src/components/keyboard-shortcuts-help.jsx:168
msgid "Like (favourite)"
msgstr "Ḥemmel (asmenyaf)"

#: src/components/keyboard-shortcuts-help.jsx:170
msgid "<0>l</0> or <1>f</1>"
msgstr "<0>l</0> or <1>f</1>"

#: src/components/keyboard-shortcuts-help.jsx:176
#: src/components/status.jsx:1117
#: src/components/status.jsx:2617
#: src/components/status.jsx:2640
#: src/components/status.jsx:2641
msgid "Boost"
msgstr "Zuzer"

#: src/components/keyboard-shortcuts-help.jsx:178
msgid "<0>Shift</0> + <1>b</1>"
msgstr "<0>Shift</0> + <1>b</1>"

#: src/components/keyboard-shortcuts-help.jsx:184
#: src/components/status.jsx:1180
#: src/components/status.jsx:2665
#: src/components/status.jsx:2666
msgid "Bookmark"
msgstr "Ticreḍt n usebtar"

#: src/components/keyboard-shortcuts-help.jsx:188
msgid "Toggle Cloak mode"
msgstr "Rmed/Sens askar uffir"

#: src/components/keyboard-shortcuts-help.jsx:190
msgid "<0>Shift</0> + <1>Alt</1> + <2>k</2>"
msgstr "<0>Shift</0> + <1>Alt</1> + <2>k</2>"

#: src/components/list-add-edit.jsx:41
msgid "Edit list"
msgstr "Ẓreg tabdart"

#: src/components/list-add-edit.jsx:97
msgid "Unable to edit list."
msgstr "D awezɣi ad tettwaẓrag tebdart."

#: src/components/list-add-edit.jsx:98
msgid "Unable to create list."
msgstr "D awezɣi timerna n tebdart."

#: src/components/list-add-edit.jsx:126
msgid "Show replies to list members"
msgstr "Sken tiririyin i yiεeggalen n tebdart"

#: src/components/list-add-edit.jsx:129
msgid "Show replies to people I follow"
msgstr "Sken tiririyin i yimdanen i ṭṭafareɣ"

#: src/components/list-add-edit.jsx:132
msgid "Don't show replies"
msgstr "Ur skan ara tiririyin"

#: src/components/list-add-edit.jsx:146
msgid "Hide posts on this list from Home/Following"
msgstr "Ffer tisuffaɣ deg tebdart-a seg ugejdan/Aḍfar"

#: src/components/list-add-edit.jsx:152
#: src/pages/filters.jsx:570
msgid "Create"
msgstr "Snulfu-d"

#: src/components/list-add-edit.jsx:159
msgid "Delete this list?"
msgstr "Kkes tabdart-a?"

#: src/components/list-add-edit.jsx:178
msgid "Unable to delete list."
msgstr "D awezɣi tukksa n tebdart."

#: src/components/list-exclusive-badge.jsx:14
#: src/pages/list.jsx:179
#: src/pages/lists.jsx:107
msgid "Posts on this list are hidden from Home/Following"
msgstr ""

#: src/components/media-alt-modal.jsx:48
#: src/components/media.jsx:51
msgid "Media description"
msgstr "Aglam n umidya"

#: src/components/media-alt-modal.jsx:67
#: src/components/status.jsx:1223
#: src/components/status.jsx:1232
#: src/components/translation-block.jsx:239
msgid "Translate"
msgstr "Suqel"

#: src/components/media-alt-modal.jsx:78
#: src/components/status.jsx:1251
msgid "Speak"
msgstr "Mmeslay"

#: src/components/media-modal.jsx:374
msgid "Open original media in new window"
msgstr "Ldi amidya aɣbalu deg usfaylu amaynut"

#: src/components/media-modal.jsx:378
msgid "Open original media"
msgstr "Ldi amidya aɣbalu"

#: src/components/media-modal.jsx:394
msgid "Attempting to describe image. Please wait…"
msgstr "Aneεruḍ n uglam n tugna. Ttxil-k rǧu…"

#: src/components/media-modal.jsx:409
msgid "Failed to describe image"
msgstr "Yecceḍ uglam n tugna"

#: src/components/media-modal.jsx:419
msgid "Describe image…"
msgstr "Glem tugna…"

#: src/components/media-modal.jsx:443
msgid "View post"
msgstr "Wali tasuffeɣt"

#: src/components/media-post.jsx:127
msgid "Sensitive media"
msgstr "Amidya amḥulfu"

#: src/components/media-post.jsx:132
msgid "Filtered: {filterTitleStr}"
msgstr "Yettwasizdeg: {filterTitleStr}"

#: src/components/media-post.jsx:133
#: src/components/status.jsx:3767
#: src/components/status.jsx:3863
#: src/components/status.jsx:3941
#: src/components/timeline.jsx:1012
#: src/pages/catchup.jsx:75
#: src/pages/catchup.jsx:1880
msgid "Filtered"
msgstr "Tettwasizdeg"

#: src/components/media.jsx:477
msgid "Open file"
msgstr "Ldi afaylu"

#: src/components/modals.jsx:75
msgid "Post scheduled"
msgstr ""

#: src/components/modals.jsx:76
msgid "Post published. Check it out."
msgstr "Yeffeɣ-d yizen-nni. Mmuqqel-it."

#: src/components/modals.jsx:78
msgid "Reply scheduled"
msgstr ""

#: src/components/modals.jsx:79
msgid "Reply posted. Check it out."
msgstr "Tiririt ɣef tsuffeɣt. Senqed-itt."

#: src/components/modals.jsx:80
msgid "Post updated. Check it out."
msgstr "Yettwalqam yizen-nni. Mmuqel-it."

#: src/components/nav-menu.jsx:123
msgid "Menu"
msgstr "Umuɣ"

#: src/components/nav-menu.jsx:159
msgid "Reload page now to update?"
msgstr "Ales asali n usebter tura i uleqqem?"

#: src/components/nav-menu.jsx:171
msgid "New update available…"
msgstr "Yella lqem amaynut…"

#. js-lingui-explicit-id
#: src/components/nav-menu.jsx:190
#: src/components/shortcuts-settings.jsx:140
#: src/pages/following.jsx:23
#: src/pages/following.jsx:145
msgid "following.title"
msgstr "yettwaḍeffren"

#: src/components/nav-menu.jsx:197
#: src/pages/catchup.jsx:878
msgid "Catch-up"
msgstr "Alukem"

#: src/components/nav-menu.jsx:204
#: src/components/shortcuts-settings.jsx:58
#: src/components/shortcuts-settings.jsx:146
#: src/pages/home.jsx:240
#: src/pages/mentions.jsx:21
#: src/pages/mentions.jsx:174
#: src/pages/settings.jsx:1190
#: src/pages/trending.jsx:384
msgid "Mentions"
msgstr "Ibdaren"

#: src/components/nav-menu.jsx:211
#: src/components/shortcuts-settings.jsx:49
#: src/components/shortcuts-settings.jsx:152
#: src/pages/filters.jsx:24
#: src/pages/home.jsx:86
#: src/pages/home.jsx:198
#: src/pages/notifications.jsx:117
#: src/pages/notifications.jsx:591
msgid "Notifications"
msgstr "Alɣu"

#: src/components/nav-menu.jsx:214
msgid "New"
msgstr "Amaynut"

#: src/components/nav-menu.jsx:225
msgid "Profile"
msgstr "Amaɣnu"

#: src/components/nav-menu.jsx:233
#: src/components/shortcuts-settings.jsx:54
#: src/components/shortcuts-settings.jsx:195
#: src/pages/bookmarks.jsx:12
#: src/pages/bookmarks.jsx:26
msgid "Bookmarks"
msgstr "Ticraḍ n yisebtar"

#: src/components/nav-menu.jsx:253
#: src/components/shortcuts-settings.jsx:55
#: src/components/shortcuts-settings.jsx:201
#: src/pages/catchup.jsx:1449
#: src/pages/catchup.jsx:2067
#: src/pages/favourites.jsx:12
#: src/pages/favourites.jsx:26
#: src/pages/settings.jsx:1194
msgid "Likes"
msgstr "Iεǧeb-as"

#: src/components/nav-menu.jsx:259
#: src/pages/followed-hashtags.jsx:15
#: src/pages/followed-hashtags.jsx:45
msgid "Followed Hashtags"
msgstr "Ihacṭagen yettwaḍfaren"

#: src/components/nav-menu.jsx:273
#: src/pages/account-statuses.jsx:333
#: src/pages/filters.jsx:55
#: src/pages/filters.jsx:94
#: src/pages/hashtag.jsx:344
msgid "Filters"
msgstr "Imzizdigen"

#: src/components/nav-menu.jsx:281
msgid "Muted users"
msgstr "Iseqdacen yettwasgugmen"

#: src/components/nav-menu.jsx:289
msgid "Muted users…"
msgstr "Iseqdacen yettwasgugmen…"

#: src/components/nav-menu.jsx:296
msgid "Blocked users"
msgstr "Imiḍanen yettusḥebsen"

#: src/components/nav-menu.jsx:304
msgid "Blocked users…"
msgstr "Imiḍanen yettusḥebsen…"

#: src/components/nav-menu.jsx:316
msgid "Accounts…"
msgstr "Imiḍanen…"

#: src/components/nav-menu.jsx:326
#: src/pages/login.jsx:32
#: src/pages/login.jsx:199
#: src/pages/status.jsx:924
#: src/pages/welcome.jsx:65
msgid "Log in"
msgstr "Qqen"

#: src/components/nav-menu.jsx:343
#: src/components/shortcuts-settings.jsx:57
#: src/components/shortcuts-settings.jsx:172
#: src/pages/trending.jsx:444
msgid "Trending"
msgstr "Mucaεen"

#: src/components/nav-menu.jsx:355
#: src/components/shortcuts-settings.jsx:165
msgid "Federated"
msgstr "Asedduklan"

#: src/components/nav-menu.jsx:378
msgid "Shortcuts / Columns…"
msgstr "Inegzumen / Ijga…"

#: src/components/nav-menu.jsx:388
#: src/components/nav-menu.jsx:402
msgid "Settings…"
msgstr "Iɣewwaṛen…"

#: src/components/nav-menu.jsx:432
#: src/components/nav-menu.jsx:467
#: src/components/shortcuts-settings.jsx:50
#: src/components/shortcuts-settings.jsx:158
#: src/pages/list.jsx:127
#: src/pages/lists.jsx:18
#: src/pages/lists.jsx:54
msgid "Lists"
msgstr "Tibdarin"

#: src/components/nav-menu.jsx:440
#: src/components/shortcuts.jsx:241
#: src/pages/list.jsx:139
msgid "All Lists"
msgstr "Akk tibdarin"

#: src/components/notification-service.jsx:161
msgid "Notification"
msgstr "Ulɣu"

#: src/components/notification-service.jsx:167
msgid "This notification is from your other account."
msgstr "Talɣut-a seg umiḍan-ik niḍen."

#: src/components/notification-service.jsx:196
msgid "View all notifications"
msgstr "Wali akk ulɣuten"

#: src/components/notification.jsx:71
msgid "{account} reacted to your post with {emojiObject}"
msgstr "{account} yerra-d ɣef tsuffeɣt-ik s {emojiObject}"

#: src/components/notification.jsx:78
msgid "{account} published a post."
msgstr "Y·Tesuffeɣ-d {account} tasuffeɣt."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:86
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted your reply.} other {{account} boosted your post.}}} other {{account} boosted {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted your reply.} other {<2><3>{1}</3> people</2> boosted your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} yesnerna tiririt-ik·im.} other {{account} yesnerna tasuffeɣt-ik·im.}}} other {{account} yesnerna {postsCount} n tsuffaɣ-ik.}}} other {{postType, select, reply {<0><1>{0}</1> imdanen</0> snernan tiririt-ik·im.} other {<2><3>{1}</3> imdanen</2> zuzren tasuffeɣt-ik·im.}}}}"

#. placeholder {0}: shortenNumber(count)
#: src/components/notification.jsx:129
msgid "{count, plural, =1 {{account} followed you.} other {<0><1>{0}</1> people</0> followed you.}}"
msgstr "{count, plural, one {}=1 {{account} yeṭṭafar-ik.} other {<0><1>{0}</1> imdanen</0> ṭṭafaren-k.}}"

#: src/components/notification.jsx:143
msgid "{account} requested to follow you."
msgstr "{account} yessuter ad k-yeḍfer."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:152
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} liked your reply.} other {{account} liked your post.}}} other {{account} liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> liked your reply.} other {<2><3>{1}</3> people</2> liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} teεǧeb-as tririt-ik·im.} other {{account} teεǧeb-as tsuffeɣt-ik·im.}}} other {{account} εeǧbent-as {postsCount} tsuffaɣ-ik·im.}}} other {{postType, select, reply {<0><1>{0}</1> imdanen</0> teεǧeb-asen·t tririt-ik·im.} other {<2><3>{1}</3> imdanen</2> teεǧeb-asen·t tsuffeɣt-ik·im.}}}}"

#: src/components/notification.jsx:194
msgid "A poll you have voted in or created has ended."
msgstr "Asisten i tferneḍ neɣ i terniḍ ifukk."

#: src/components/notification.jsx:195
msgid "A poll you have created has ended."
msgstr "Asisten i terniḍ ifukk."

#: src/components/notification.jsx:196
msgid "A poll you have voted in has ended."
msgstr "Ifukk usisten ideg tettekkaḍ."

#: src/components/notification.jsx:197
msgid "A post you interacted with has been edited."
msgstr "Tasuffeɣt iɣef twennteḍ tettwaẓreg."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:205
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted & liked your reply.} other {{account} boosted & liked your post.}}} other {{account} boosted & liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted & liked your reply.} other {<2><3>{1}</3> people</2> boosted & liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} izuzer & teεǧeb-as tririt-ik·im.} other {{account} izuzer & teεǧeb-as tsuffeɣt-ik·im.}}} other {{account} izuzer & εeǧbent-as {postsCount} n tsuffaɣ-ik·im.}}} other {{postType, select, reply {<0><1>{0}</1> imdanen</0> zuzren & teεǧeb-asen·t tririt-ik·im.} other {<2><3>{1}</3> imdanen</2> zuzren & teεǧeb-asen·t tsuffeɣt-ik·im.}}}}"

#: src/components/notification.jsx:247
msgid "{account} signed up."
msgstr "{account} yeffeɣ."

#: src/components/notification.jsx:249
msgid "{account} reported {targetAccount}"
msgstr "{account} yettwasmater {targetAccount}"

#: src/components/notification.jsx:254
msgid "Lost connections with <0>{name}</0>."
msgstr "Ruḥent tuqqniwin akked <0>{name}</0>."

#: src/components/notification.jsx:260
msgid "Moderation warning"
msgstr "Alɣu n uqeεεed"

#: src/components/notification.jsx:265
msgid "Your {year} #Wrapstodon is here!"
msgstr ""

#: src/components/notification.jsx:271
msgid "An admin from <0>{from}</0> has suspended <1>{targetName}</1>, which means you can no longer receive updates from them or interact with them."
msgstr "Anedbal seg <0>{from}</0> yettwaḥbes <1>{targetName}</1>, dayen ulac ileqman ara d-yawḍen sɣur-s neɣ amyigew yid-s."

#: src/components/notification.jsx:277
msgid "An admin from <0>{from}</0> has blocked <1>{targetName}</1>. Affected followers: {followersCount}, followings: {followingCount}."
msgstr "Anedbal seg <0>{from}</0> yettusewḥel <1>{targetName}</1>. Ineḍfaren yettwaḥuzan: {followersCount}, ineḍfaren: {followingCount}."

#: src/components/notification.jsx:283
msgid "You have blocked <0>{targetName}</0>. Removed followers: {followersCount}, followings: {followingCount}."
msgstr "Tesweḥleḍ <0>{targetName}</0>. Ineḍfaren yettwakksen: {followersCount}, ineḍfaren: {followingCount}."

#: src/components/notification.jsx:291
msgid "Your account has received a moderation warning."
msgstr "Amiḍan-ik yeṭṭef-d alɣu n uqeεεed."

#: src/components/notification.jsx:292
msgid "Your account has been disabled."
msgstr "Amiḍan-ik yensa."

#: src/components/notification.jsx:293
msgid "Some of your posts have been marked as sensitive."
msgstr "Kra seg tsuffaɣ-ik ttwacerḍent d timḥulfa."

#: src/components/notification.jsx:294
msgid "Some of your posts have been deleted."
msgstr "Kra seg tsuffaɣ-ik ttwakksent."

#: src/components/notification.jsx:295
msgid "Your posts will be marked as sensitive from now on."
msgstr "Tisuffaɣ-ik ad ttwacerḍent d tisuffaɣ timṣulfa sya d asawen."

#: src/components/notification.jsx:296
msgid "Your account has been limited."
msgstr "Amiḍan-ik yesεa tilas."

#: src/components/notification.jsx:297
msgid "Your account has been suspended."
msgstr "Yettwaseḥbes umiḍan-ik."

#: src/components/notification.jsx:374
msgid "[Unknown notification type: {type}]"
msgstr "[Anaw n wulɣu d arussin: {type}]"

#: src/components/notification.jsx:451
#: src/components/status.jsx:1194
#: src/components/status.jsx:1204
msgid "Boosted/Liked by…"
msgstr "Izuzer-it/Iḥemmel-it…"

#: src/components/notification.jsx:452
msgid "Liked by…"
msgstr "Iεǧeb-as i…"

#: src/components/notification.jsx:453
msgid "Boosted by…"
msgstr "Izuzer-it…"

#: src/components/notification.jsx:454
msgid "Followed by…"
msgstr "Iḍfer-it…"

#: src/components/notification.jsx:535
#: src/components/notification.jsx:551
msgid "Learn more <0/>"
msgstr "Issin ugar <0/>"

#: src/components/notification.jsx:560
msgid "View #Wrapstodon"
msgstr "Wali #Wrapstodon"

#: src/components/notification.jsx:801
#: src/components/status.jsx:403
msgid "Read more →"
msgstr "Γer ugar →"

#: src/components/poll.jsx:113
msgid "Voted"
msgstr "Yefren"

#: src/components/poll.jsx:119
msgid "{optionVotesCount, plural, one {# vote} other {# votes}}"
msgstr ""

#: src/components/poll.jsx:139
#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Hide results"
msgstr "Ffer igmaḍ"

#: src/components/poll.jsx:188
msgid "Vote"
msgstr "Fren"

#: src/components/poll.jsx:208
#: src/components/poll.jsx:210
#: src/pages/scheduled-posts.jsx:100
#: src/pages/status.jsx:1294
#: src/pages/status.jsx:1317
msgid "Refresh"
msgstr "Smiren"

#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Show results"
msgstr "Sken igmaḍ"

#. placeholder {0}: shortenNumber(votesCount)
#. placeholder {1}: shortenNumber(votesCount)
#: src/components/poll.jsx:231
msgid "{votesCount, plural, one {<0>{0}</0> vote} other {<1>{1}</1> votes}}"
msgstr "{votesCount, plural, one {<0>{0}</0> afran} other {<1>{1}</1> ifranen}}"

#. placeholder {0}: shortenNumber(votersCount)
#. placeholder {1}: shortenNumber(votersCount)
#: src/components/poll.jsx:248
msgid "{votersCount, plural, one {<0>{0}</0> voter} other {<1>{1}</1> voters}}"
msgstr "{votersCount, plural, one {<0>{0}</0> amefran} other {<1>{1}</1> imefranen}}"

#: src/components/poll.jsx:268
msgid "Ended <0/>"
msgstr "Ifukk <0/>"

#: src/components/poll.jsx:272
msgid "Ended"
msgstr "Ifukk"

#: src/components/poll.jsx:275
msgid "Ending <0/>"
msgstr "Yettfakk <0/>"

#: src/components/poll.jsx:279
msgid "Ending"
msgstr "Yettfakk"

#. Relative time in seconds, as short as possible
#. placeholder {0}: seconds < 1 ? 1 : Math.floor(seconds)
#: src/components/relative-time.jsx:61
msgid "{0}s"
msgstr "{0}tsn"

#. Relative time in minutes, as short as possible
#. placeholder {0}: Math.floor(seconds / minute)
#: src/components/relative-time.jsx:66
msgid "{0}m"
msgstr "{0}tsd"

#. Relative time in hours, as short as possible
#. placeholder {0}: Math.floor(seconds / hour)
#: src/components/relative-time.jsx:71
msgid "{0}h"
msgstr "{0}srg"

#: src/components/report-modal.jsx:30
msgid "Spam"
msgstr "D aspam"

#: src/components/report-modal.jsx:31
msgid "Malicious links, fake engagement, or repetitive replies"
msgstr "Yir iseɣwan, yir agman d tririyin i d-yettuɣalen"

#: src/components/report-modal.jsx:34
msgid "Illegal"
msgstr "Arusḍif"

#: src/components/report-modal.jsx:35
msgid "Violates the law of your or the server's country"
msgstr "Ur iquder ara asaḍuf n tmurt-ik neɣ asaḍuf n uqeddac"

#: src/components/report-modal.jsx:38
msgid "Server rule violation"
msgstr "Takriṭ n ulugen n uqeddac"

#: src/components/report-modal.jsx:39
msgid "Breaks specific server rules"
msgstr "Yerẓa ilugan uzzigen n uqeddac"

#: src/components/report-modal.jsx:40
msgid "Violation"
msgstr "Takriṭ"

#: src/components/report-modal.jsx:43
msgid "Other"
msgstr "Ayen nniḍen"

#: src/components/report-modal.jsx:44
msgid "Issue doesn't fit other categories"
msgstr "Ugur ur yemmezg ara d taggayin niḍen"

#: src/components/report-modal.jsx:111
msgid "Report Post"
msgstr "Mmel tasuffeɣt"

#: src/components/report-modal.jsx:111
msgid "Report @{username}"
msgstr "Mmel @{username}"

#: src/components/report-modal.jsx:147
msgid "Pending review"
msgstr "Ittraǧu acegger"

#: src/components/report-modal.jsx:189
msgid "Post reported"
msgstr "Tasuffeɣt tattwammel"

#: src/components/report-modal.jsx:189
msgid "Profile reported"
msgstr "Amaɣnu yettwammel"

#: src/components/report-modal.jsx:197
msgid "Unable to report post"
msgstr "D awezɣi tummla n tsuffeɣt"

#: src/components/report-modal.jsx:198
msgid "Unable to report profile"
msgstr "D awezɣi tummla n umaɣnu"

#: src/components/report-modal.jsx:206
msgid "What's the issue with this post?"
msgstr "D acu i d ugur deg tsuffeɣt-a?"

#: src/components/report-modal.jsx:207
msgid "What's the issue with this profile?"
msgstr "D acu i d ugur deg umaɣnu-a?"

#: src/components/report-modal.jsx:276
msgid "Additional info"
msgstr "Talɣut niḍen"

#: src/components/report-modal.jsx:299
msgid "Forward to <0>{domain}</0>"
msgstr "Welleh ɣer <0>{domain}</0>"

#: src/components/report-modal.jsx:309
msgid "Send Report"
msgstr "Azen aneqqis"

#: src/components/report-modal.jsx:318
msgid "Muted {username}"
msgstr "Yettwasgugem {username}"

#: src/components/report-modal.jsx:321
msgid "Unable to mute {username}"
msgstr "D awezɣi asgugem n {username}"

#: src/components/report-modal.jsx:326
msgid "Send Report <0>+ Mute profile</0>"
msgstr "Azen aneqqis <0>+ Sgugem amaɣnu</0>"

#: src/components/report-modal.jsx:337
msgid "Blocked {username}"
msgstr "Yettusewḥel {username}"

#: src/components/report-modal.jsx:340
msgid "Unable to block {username}"
msgstr "D awezɣi asewḥel i {username}"

#: src/components/report-modal.jsx:345
msgid "Send Report <0>+ Block profile</0>"
msgstr "Azen aneqqis <0>+ Sewḥel amaɣnu</0>"

#: src/components/search-form.jsx:203
msgid "{query} <0>‒ accounts, hashtags & posts</0>"
msgstr "{query} <0>‒ imiḍanen, ihacṭagen & tisuffaɣ</0>"

#: src/components/search-form.jsx:216
msgid "Posts with <0>{query}</0>"
msgstr "Tisuffaɣ yesɛan <0>{query}</0>"

#. placeholder {0}: query.replace(/^#/, '')
#: src/components/search-form.jsx:228
msgid "Posts tagged with <0>#{0}</0>"
msgstr "Tisuffaɣ yettwaweccmen <0>#{0}</0>"

#: src/components/search-form.jsx:242
msgid "Look up <0>{query}</0>"
msgstr "Wali <0>{query}</0>"

#: src/components/search-form.jsx:253
msgid "Accounts with <0>{query}</0>"
msgstr "Imiḍanen yesɛan <0>{query}</0>"

#: src/components/shortcuts-settings.jsx:48
msgid "Home / Following"
msgstr "Agejdan / Aḍfar"

#: src/components/shortcuts-settings.jsx:51
msgid "Public (Local / Federated)"
msgstr "Azayez (Adigan / Asedduklan)"

#: src/components/shortcuts-settings.jsx:53
msgid "Account"
msgstr "Amiḍan"

#: src/components/shortcuts-settings.jsx:56
msgid "Hashtag"
msgstr "Ahacṭag"

#: src/components/shortcuts-settings.jsx:63
msgid "List ID"
msgstr "Asulay n tebdart"

#: src/components/shortcuts-settings.jsx:70
msgid "Local only"
msgstr "Adigan kan"

#: src/components/shortcuts-settings.jsx:75
#: src/components/shortcuts-settings.jsx:84
#: src/components/shortcuts-settings.jsx:122
#: src/pages/login.jsx:203
msgid "Instance"
msgstr "Aqeddac"

#: src/components/shortcuts-settings.jsx:78
#: src/components/shortcuts-settings.jsx:87
#: src/components/shortcuts-settings.jsx:125
msgid "Optional, e.g. mastodon.social"
msgstr "Afrayan, am. mastodon.social"

#: src/components/shortcuts-settings.jsx:93
msgid "Search term"
msgstr "Nadi awal"

#: src/components/shortcuts-settings.jsx:96
msgid "Optional, unless for multi-column mode"
msgstr "Afrayan, slid i uskar n waṭas n tgejda"

#: src/components/shortcuts-settings.jsx:113
msgid "e.g. PixelArt (Max 5, space-separated)"
msgstr "e.g. PixelArt (ugar n 5, ferqen s tallunt)"

#: src/components/shortcuts-settings.jsx:117
#: src/pages/hashtag.jsx:360
msgid "Media only"
msgstr "Amidya kan"

#: src/components/shortcuts-settings.jsx:235
#: src/components/shortcuts.jsx:218
msgid "Shortcuts"
msgstr "Inegzumen"

#: src/components/shortcuts-settings.jsx:243
msgid "beta"
msgstr "beta"

#: src/components/shortcuts-settings.jsx:249
msgid "Specify a list of shortcuts that'll appear as:"
msgstr "Suzzeg tabdart n yinegzumen ara d-ibanen  am:"

#: src/components/shortcuts-settings.jsx:255
msgid "Floating button"
msgstr "Tiqeffilt yettifliwen"

#: src/components/shortcuts-settings.jsx:260
msgid "Tab/Menu bar"
msgstr "Iccer/Afeggag n wumuɣ"

#: src/components/shortcuts-settings.jsx:265
msgid "Multi-column"
msgstr "Aṭas n tgejdiyin"

#: src/components/shortcuts-settings.jsx:332
msgid "Not available in current view mode"
msgstr "Ulac deg uskar n uskan amiran"

#: src/components/shortcuts-settings.jsx:351
#: src/pages/accounts.jsx:188
msgid "Move up"
msgstr "Nkez d asawen"

#: src/components/shortcuts-settings.jsx:367
#: src/pages/accounts.jsx:203
msgid "Move down"
msgstr "Nkez d akessar"

#: src/components/shortcuts-settings.jsx:379
#: src/components/status.jsx:1474
#: src/pages/list.jsx:195
msgid "Edit"
msgstr "Ẓreg"

#: src/components/shortcuts-settings.jsx:400
msgid "Add more than one shortcut/column to make this work."
msgstr "Rnu ugar n unegzum/tgejdit i wakken ad yekker."

#: src/components/shortcuts-settings.jsx:411
msgid "No columns yet. Tap on the Add column button."
msgstr "Ulac tigejdit akka tura. Sit ɣef tqeffalt \"Rnu tigejdit\"."

#: src/components/shortcuts-settings.jsx:412
msgid "No shortcuts yet. Tap on the Add shortcut button."
msgstr "Ulac inegzumen akka tura. Sit ɣef tqeffalt \"Rnu anegzum\"."

#: src/components/shortcuts-settings.jsx:415
msgid "Not sure what to add?<0/>Try adding <1>Home / Following and Notifications</1> first."
msgstr "Ur tt-tefriḍ ara ɣef wacu ara ternuḍ?<0/>Ԑreḍ timerna<1>n ugejdan / Aḍfar akked ilɣa</1> d imezwura."

#: src/components/shortcuts-settings.jsx:443
msgid "Max {SHORTCUTS_LIMIT} columns"
msgstr "Ugar n tgejda {SHORTCUTS_LIMIT}"

#: src/components/shortcuts-settings.jsx:444
msgid "Max {SHORTCUTS_LIMIT} shortcuts"
msgstr "Ugar n {SHORTCUTS_LIMIT} yinegzumen"

#: src/components/shortcuts-settings.jsx:458
msgid "Import/export"
msgstr "Kter/sifeḍ"

#: src/components/shortcuts-settings.jsx:468
msgid "Add column…"
msgstr "Rnu ajgu…"

#: src/components/shortcuts-settings.jsx:469
msgid "Add shortcut…"
msgstr "Rnu anegzum…"

#: src/components/shortcuts-settings.jsx:516
msgid "Specific list is optional. For multi-column mode, list is required, else the column will not be shown."
msgstr "Tabdart tuzzigt d tafrayant. I uskar s waṭas n tgejda, tabdart tettwasra, ma ulac tigejdit ad teffer."

#: src/components/shortcuts-settings.jsx:517
msgid "For multi-column mode, search term is required, else the column will not be shown."
msgstr "I uskar s waṭas n tgejda, awal n unadi yettwasra, ma ulac tigejdit ad teffer."

#: src/components/shortcuts-settings.jsx:518
msgid "Multiple hashtags are supported. Space-separated."
msgstr "Aṭas ihacṭagen ttwasefraken. Ferqen s tallunt."

#: src/components/shortcuts-settings.jsx:587
msgid "Edit shortcut"
msgstr "Ẓreg anegzum"

#: src/components/shortcuts-settings.jsx:587
msgid "Add shortcut"
msgstr "Rnu anegzum"

#: src/components/shortcuts-settings.jsx:623
msgid "Timeline"
msgstr "Tasnimirt"

#: src/components/shortcuts-settings.jsx:649
msgid "List"
msgstr "Tabdart"

#: src/components/shortcuts-settings.jsx:788
msgid "Import/Export <0>Shortcuts</0>"
msgstr "Kter/Sifeḍ<0>Inegzumen</0>"

#: src/components/shortcuts-settings.jsx:798
msgid "Import"
msgstr "Kter"

#: src/components/shortcuts-settings.jsx:806
msgid "Paste shortcuts here"
msgstr "Senṭeḍ inegzumen da"

#: src/components/shortcuts-settings.jsx:822
msgid "Downloading saved shortcuts from instance server…"
msgstr "Asader inegzumen i yettwaskelsen seg uqeddac n tummant…"

#: src/components/shortcuts-settings.jsx:851
msgid "Unable to download shortcuts"
msgstr "D awezɣi asader n yinegzumen"

#: src/components/shortcuts-settings.jsx:854
msgid "Download shortcuts from instance server"
msgstr "Sader inegzumen seg uqeddac n tummant"

#: src/components/shortcuts-settings.jsx:912
msgid "* Exists in current shortcuts"
msgstr "* Yella deg yinegzumen imiranen"

#: src/components/shortcuts-settings.jsx:917
msgid "List may not work if it's from a different account."
msgstr "Tabdart yezmer ur tetteddu ara ma yella tekka-d seg umiḍan niḍen."

#: src/components/shortcuts-settings.jsx:927
msgid "Invalid settings format"
msgstr "Amasal n yiɣewwaren d arameɣtu"

#: src/components/shortcuts-settings.jsx:935
msgid "Append to current shortcuts?"
msgstr "Semselsi inegzumen imiranen?"

#: src/components/shortcuts-settings.jsx:938
msgid "Only shortcuts that don’t exist in current shortcuts will be appended."
msgstr "Slid inegzumen i yulac deg yinegzumen imiranen ara yettwarnun."

#: src/components/shortcuts-settings.jsx:960
msgid "No new shortcuts to import"
msgstr "Ulac inegzumen imaynuten i uktar"

#: src/components/shortcuts-settings.jsx:975
msgid "Shortcuts imported. Exceeded max {SHORTCUTS_LIMIT}, so the rest are not imported."
msgstr "Inegzumen ttwaktaren-d. Ԑeddan talast n {SHORTCUTS_LIMIT}, ɣef waya llan wid ur d-yettwaktaren ara."

#: src/components/shortcuts-settings.jsx:976
#: src/components/shortcuts-settings.jsx:1000
msgid "Shortcuts imported"
msgstr "Inegzumen ttwaketren"

#: src/components/shortcuts-settings.jsx:986
msgid "Import & append…"
msgstr "Kter & semselsi…"

#: src/components/shortcuts-settings.jsx:994
msgid "Override current shortcuts?"
msgstr "Zgel inegzumen imiranen?"

#: src/components/shortcuts-settings.jsx:995
msgid "Import shortcuts?"
msgstr "Kter inegzumen?"

#: src/components/shortcuts-settings.jsx:1009
msgid "or override…"
msgstr "neɣ zgel…"

#: src/components/shortcuts-settings.jsx:1009
msgid "Import…"
msgstr "Kter…"

#: src/components/shortcuts-settings.jsx:1018
msgid "Export"
msgstr "Sifeḍ"

#: src/components/shortcuts-settings.jsx:1033
msgid "Shortcuts copied"
msgstr "Inegzumen ttwaneɣlen"

#: src/components/shortcuts-settings.jsx:1036
msgid "Unable to copy shortcuts"
msgstr "D awezɣi anɣal n yinegzumen"

#: src/components/shortcuts-settings.jsx:1050
msgid "Shortcut settings copied"
msgstr "Iɣewwaren n unegzum ttwaneɣlen"

#: src/components/shortcuts-settings.jsx:1053
msgid "Unable to copy shortcut settings"
msgstr "D awezɣi anɣal n yiɣewwaren n yinegzumen"

#: src/components/shortcuts-settings.jsx:1083
msgid "Share"
msgstr "Bḍu"

#: src/components/shortcuts-settings.jsx:1122
msgid "Saving shortcuts to instance server…"
msgstr "Asekles n yinegzumen ɣef uqeddac n tummant…"

#: src/components/shortcuts-settings.jsx:1129
msgid "Shortcuts saved"
msgstr "Inegzumen ttwaskelsen"

#: src/components/shortcuts-settings.jsx:1134
msgid "Unable to save shortcuts"
msgstr "D awezɣi asekles n yinegzumen"

#: src/components/shortcuts-settings.jsx:1137
msgid "Sync to instance server"
msgstr "Mtawi akked uqeddac n tummant"

#. placeholder {0}: shortcutsStr.length
#: src/components/shortcuts-settings.jsx:1145
msgid "{0, plural, one {# character} other {# characters}}"
msgstr "{0, plural, one {# asekkil} other {# isekkilen}}"

#: src/components/shortcuts-settings.jsx:1157
msgid "Raw Shortcuts JSON"
msgstr "Inegzumen n yizirig JSON"

#: src/components/shortcuts-settings.jsx:1170
msgid "Import/export settings from/to instance server (Very experimental)"
msgstr "Kter/sifeḍ iɣewwaren seg/ɣer uqeddac n tummant (d armitan ugar)"

#: src/components/status.jsx:277
msgid "Unable to format math"
msgstr ""

#: src/components/status.jsx:292
msgid "Math expressions found."
msgstr ""

#: src/components/status.jsx:294
msgid "Show markup"
msgstr ""

#: src/components/status.jsx:294
msgid "Format math"
msgstr ""

#: src/components/status.jsx:689
msgid "<0/> <1>boosted</1>"
msgstr "<0/><1>izuzer-it</1>"

#: src/components/status.jsx:792
msgid "Sorry, your current logged-in instance can't interact with this post from another instance."
msgstr "Nesḥassef, tummant-ik tamirantur yeqqnen ur tezmir ara ad temyigewt akked tsuffaɣt -a seg tummant tayeḍ."

#. placeholder {0}: username || acct
#: src/components/status.jsx:946
msgid "Unliked @{0}'s post"
msgstr "Yekkes-as uεǧab i tsuffeɣt n @{0}"

#. placeholder {0}: username || acct
#: src/components/status.jsx:947
msgid "Liked @{0}'s post"
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:986
msgid "Unbookmarked @{0}'s post"
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:987
msgid "Bookmarked @{0}'s post"
msgstr ""

#: src/components/status.jsx:1086
msgid "Some media have no descriptions."
msgstr "Kra yimidyaten ulac ɣer-sen aglam."

#. placeholder {0}: rtf.format(-statusMonthsAgo, 'month')
#: src/components/status.jsx:1093
msgid "Old post (<0>{0}</0>)"
msgstr "Tasuffeɣt taqburt (<0>{0}</0>)"

#: src/components/status.jsx:1117
#: src/components/status.jsx:1157
#: src/components/status.jsx:2617
#: src/components/status.jsx:2640
msgid "Unboost"
msgstr "Sefsex azuzer"

#: src/components/status.jsx:1133
#: src/components/status.jsx:2632
msgid "Quote"
msgstr "Tanebdurt"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1145
#: src/components/status.jsx:1611
msgid "Unboosted @{0}'s post"
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:1146
#: src/components/status.jsx:1612
msgid "Boosted @{0}'s post"
msgstr ""

#: src/components/status.jsx:1158
msgid "Boost…"
msgstr "Zuzer…"

#: src/components/status.jsx:1170
#: src/components/status.jsx:1905
#: src/components/status.jsx:2653
msgid "Unlike"
msgstr "Kkes aεǧab"

#: src/components/status.jsx:1171
#: src/components/status.jsx:1905
#: src/components/status.jsx:1906
#: src/components/status.jsx:2653
#: src/components/status.jsx:2654
msgid "Like"
msgstr "Ḥemmel"

#: src/components/status.jsx:1180
#: src/components/status.jsx:2665
msgid "Unbookmark"
msgstr "Kkes-as ticreḍt"

#: src/components/status.jsx:1263
msgid "Post text copied"
msgstr ""

#: src/components/status.jsx:1266
msgid "Unable to copy post text"
msgstr ""

#: src/components/status.jsx:1272
msgid "Copy post text"
msgstr ""

#. placeholder {0}: username || acct
#: src/components/status.jsx:1290
msgid "View post by <0>@{0}</0>"
msgstr "Wali tasuffeɣt sɣur <0>@{0}</0>"

#: src/components/status.jsx:1311
msgid "Show Edit History"
msgstr "Sken azray n teẓrigin"

#: src/components/status.jsx:1314
msgid "Edited: {editedDateText}"
msgstr "Yettwaẓreg: {editedDateText}"

#: src/components/status.jsx:1381
#: src/components/status.jsx:3433
msgid "Embed post"
msgstr "Sidef tasuffeɣt"

#: src/components/status.jsx:1395
msgid "Conversation unmuted"
msgstr "Yettwafsi usgugem ɣef udiwenni"

#: src/components/status.jsx:1395
msgid "Conversation muted"
msgstr "Yettwasgugem udiwenni"

#: src/components/status.jsx:1401
msgid "Unable to unmute conversation"
msgstr "D awezɣi tukksa n usgugem i udiwenni"

#: src/components/status.jsx:1402
msgid "Unable to mute conversation"
msgstr "D awezɣi asgugem n udiwenni"

#: src/components/status.jsx:1411
msgid "Unmute conversation"
msgstr "Kkes asgugem n udiwenni"

#: src/components/status.jsx:1418
msgid "Mute conversation"
msgstr "Sgugem adiwenni"

#: src/components/status.jsx:1434
msgid "Post unpinned from profile"
msgstr "Tasuffeɣt tettwakkes seg umaɣnu"

#: src/components/status.jsx:1435
msgid "Post pinned to profile"
msgstr "Tasuffeɣt tettwasenteḍ ɣer umaɣnu"

#: src/components/status.jsx:1440
msgid "Unable to unpin post"
msgstr "D awezɣi aserreḥ n tsuffeɣt"

#: src/components/status.jsx:1440
msgid "Unable to pin post"
msgstr "D awezɣi asenteḍ n tsuffeɣt"

#: src/components/status.jsx:1449
msgid "Unpin from profile"
msgstr "Kkes asenteḍ seg umaɣnu"

#: src/components/status.jsx:1456
msgid "Pin to profile"
msgstr "Senteḍ ɣef umaɣnu"

#: src/components/status.jsx:1485
msgid "Delete this post?"
msgstr "Kkes tasuffeɣt-a?"

#: src/components/status.jsx:1501
msgid "Post deleted"
msgstr "Tasuffeɣt tettwakkes"

#: src/components/status.jsx:1504
msgid "Unable to delete post"
msgstr "D awezɣi tukksa n tsuffeɣt"

#: src/components/status.jsx:1532
msgid "Report post…"
msgstr "Mmel tasuffeɣt…"

#: src/components/status.jsx:1906
#: src/components/status.jsx:1942
#: src/components/status.jsx:2654
msgid "Liked"
msgstr "Iḥemmel"

#: src/components/status.jsx:1939
#: src/components/status.jsx:2641
msgid "Boosted"
msgstr "Tettwazuzer"

#: src/components/status.jsx:1949
#: src/components/status.jsx:2666
msgid "Bookmarked"
msgstr "Yettwacreḍ"

#: src/components/status.jsx:1953
msgid "Pinned"
msgstr "Yettwasenteḍ"

#: src/components/status.jsx:1999
#: src/components/status.jsx:2478
msgid "Deleted"
msgstr "Yettwakkes"

#: src/components/status.jsx:2040
msgid "{repliesCount, plural, one {# reply} other {# replies}}"
msgstr "{repliesCount, plural, one {# tiririt} other {# tiririyin}}"

#. placeholder {0}: snapStates.statusThreadNumber[sKey] ? ` ${snapStates.statusThreadNumber[sKey]}/X` : ''
#: src/components/status.jsx:2130
msgid "Thread{0}"
msgstr "Asqerdec{0}"

#: src/components/status.jsx:2208
#: src/components/status.jsx:2270
#: src/components/status.jsx:2374
msgid "Show less"
msgstr "Ssken cwiṭ"

#: src/components/status.jsx:2208
#: src/components/status.jsx:2270
msgid "Show content"
msgstr "Sken agbur"

#. placeholder {0}: filterInfo.titlesStr
#. placeholder {0}: filterInfo?.titlesStr
#: src/components/status.jsx:2370
#: src/pages/catchup.jsx:1879
msgid "Filtered: {0}"
msgstr "Yettwasizdeg: {0}"

#: src/components/status.jsx:2374
msgid "Show media"
msgstr "Sken amidya"

#: src/components/status.jsx:2514
msgid "Edited"
msgstr "Yettwaẓrag"

#: src/components/status.jsx:2591
msgid "Comments"
msgstr "Iwenniten"

#. More from [Author]
#: src/components/status.jsx:2891
msgid "More from <0/>"
msgstr "Ugar sɣur <0/>"

#: src/components/status.jsx:3193
msgid "Edit History"
msgstr "Azray n teẓrigin"

#: src/components/status.jsx:3197
msgid "Failed to load history"
msgstr "Yecceḍ usali n uzray"

#: src/components/status.jsx:3202
#: src/pages/annual-report.jsx:45
msgid "Loading…"
msgstr "Yessalay-d…"

#: src/components/status.jsx:3438
msgid "HTML Code"
msgstr "Tangalt HTML"

#: src/components/status.jsx:3455
msgid "HTML code copied"
msgstr "Tangalt HTML tettwanɣal"

#: src/components/status.jsx:3458
msgid "Unable to copy HTML code"
msgstr "D awezɣi anɣal n tengalt HTML"

#: src/components/status.jsx:3470
msgid "Media attachments:"
msgstr "Imeddayen n umidya:"

#: src/components/status.jsx:3492
msgid "Account Emojis:"
msgstr "Imujiṭen n umiḍan:"

#: src/components/status.jsx:3523
#: src/components/status.jsx:3568
msgid "static URL"
msgstr "URL n tdaddanin"

#: src/components/status.jsx:3537
msgid "Emojis:"
msgstr "Imujiten:"

#: src/components/status.jsx:3582
msgid "Notes:"
msgstr "Tizmilin:"

#: src/components/status.jsx:3586
msgid "This is static, unstyled and scriptless. You may need to apply your own styles and edit as needed."
msgstr "Tidaddanin-a war talɣa, war tira. Ahat yessefk ad tesnaseḍ talɣiwin-ik, ad ten-tbeddleḍ melmi i tebɣiḍ."

#: src/components/status.jsx:3592
msgid "Polls are not interactive, becomes a list with vote counts."
msgstr "Isestanen ur myigawen ara, ttuɣalen d tabdart s umḍan n ufran."

#: src/components/status.jsx:3597
msgid "Media attachments can be images, videos, audios or any file types."
msgstr "Imeddayen n umidya zemren ad ilin d tugniwin, d tividyutin, d iseklasen imeslawen neɣ kra n wawan n ufaylu."

#: src/components/status.jsx:3603
msgid "Post could be edited or deleted later."
msgstr "Tasuffeɣt tezmer ad tettwaẓrag neɣ ad tettwakkes ticki."

#: src/components/status.jsx:3609
msgid "Preview"
msgstr "Askan"

#: src/components/status.jsx:3618
msgid "Note: This preview is lightly styled."
msgstr "Tamawt: Taskant-a tettufeṣṣel s wudem afessas."

#. [Name] [Visibility icon] boosted
#: src/components/status.jsx:3871
msgid "<0/> <1/> boosted"
msgstr "Izuzer-itt <0/><1/>"

#: src/components/status.jsx:3973
msgid "Post hidden by your filters"
msgstr ""

#: src/components/status.jsx:3974
msgid "Post removed by author."
msgstr ""

#: src/components/status.jsx:3975
msgid "You’re not authorized to view this post."
msgstr ""

#: src/components/status.jsx:3976
msgid "Post pending author approval."
msgstr ""

#: src/components/status.jsx:3977
#: src/components/status.jsx:3978
msgid "Quoting not allowed by the author."
msgstr ""

#: src/components/timeline.jsx:485
#: src/pages/settings.jsx:1218
msgid "New posts"
msgstr "Tisuffaɣ timaynutin"

#: src/components/timeline.jsx:586
#: src/pages/home.jsx:228
#: src/pages/notifications.jsx:898
#: src/pages/status.jsx:1077
#: src/pages/status.jsx:1454
msgid "Try again"
msgstr "Ɛreḍ tikkelt-nniḍen"

#. placeholder {0}: fItems.length
#: src/components/timeline.jsx:621
msgid "{0, plural, one {# Boost} other {# Boosts}}"
msgstr "{0, plural, one {# n uzuzer} other {# n yizurar}}"

#: src/components/timeline.jsx:626
msgid "Pinned posts"
msgstr "Tisuffaɣ yettwasenṭḍen"

#: src/components/timeline.jsx:985
#: src/components/timeline.jsx:992
#: src/pages/catchup.jsx:1897
msgid "Thread"
msgstr "Asqerdec"

#. placeholder {0}: filterInfo.titlesStr
#: src/components/timeline.jsx:1007
msgid "<0>Filtered</0>: <1>{0}</1>"
msgstr "<0>Yettwasizdeg</0>: <1>{0}</1>"

#: src/components/translation-block.jsx:196
msgid "Auto-translated from {sourceLangText}"
msgstr "Tasuqilt tawurmant seg {sourceLangText}"

#: src/components/translation-block.jsx:234
msgid "Translating…"
msgstr "Yessuqul…"

#: src/components/translation-block.jsx:237
msgid "Translate from {sourceLangText} (auto-detected)"
msgstr "Suqel seg {sourceLangText} (tifin tawurmant)"

#: src/components/translation-block.jsx:238
msgid "Translate from {sourceLangText}"
msgstr "Ssuqel seg {sourceLangText}"

#. placeholder {0}: detectedLang ?? '…'
#: src/components/translation-block.jsx:266
msgid "Auto ({0})"
msgstr "Awurman ({0})"

#: src/components/translation-block.jsx:279
msgid "Failed to translate"
msgstr "Tuccḍa deg tsuqilt"

#: src/compose.jsx:33
msgid "Editing source status"
msgstr "Aẓrag n waddad n uɣbalu"

#. placeholder {0}: replyToStatus.account?.acct || replyToStatus.account?.username
#: src/compose.jsx:35
msgid "Replying to @{0}"
msgstr "Tiririt i @{0}"

#: src/compose.jsx:63
msgid "You may close this page now."
msgstr "Tzemreḍ ad tmedleḍ asebtar tura."

#: src/compose.jsx:71
msgid "Close window"
msgstr "Mdel asfaylu"

#: src/compose.jsx:87
msgid "Login required."
msgstr "Tuqqna tettwasra."

#: src/compose.jsx:91
#: src/pages/annual-report.jsx:164
#: src/pages/http-route.jsx:91
#: src/pages/login.jsx:280
msgid "Go home"
msgstr "Uɣal ɣer ugejdan"

#: src/pages/account-statuses.jsx:238
msgid "Account posts"
msgstr "Tisuffaɣ n umiḍan"

#: src/pages/account-statuses.jsx:245
msgid "{accountDisplay} (+ Replies)"
msgstr "{accountDisplay} (+ tririyin)"

#: src/pages/account-statuses.jsx:247
msgid "{accountDisplay} (- Boosts)"
msgstr "{accountDisplay} (- Izuzar)"

#: src/pages/account-statuses.jsx:249
msgid "{accountDisplay} (#{tagged})"
msgstr "{accountDisplay} (#{tagged})"

#: src/pages/account-statuses.jsx:251
msgid "{accountDisplay} (Media)"
msgstr "{accountDisplay} (Amidya)"

#: src/pages/account-statuses.jsx:257
msgid "{accountDisplay} ({monthYear})"
msgstr "{accountDisplay} ({monthYear})"

#: src/pages/account-statuses.jsx:323
msgid "Clear filters"
msgstr "Sfeḍ imsizedgen"

#: src/pages/account-statuses.jsx:326
msgid "Clear"
msgstr "Sfeḍ"

#: src/pages/account-statuses.jsx:340
msgid "Showing post with replies"
msgstr "Askan n tsuffeɣt s tririyin"

#: src/pages/account-statuses.jsx:345
msgid "+ Replies"
msgstr "ugar n tririyin"

#: src/pages/account-statuses.jsx:351
msgid "Showing posts without boosts"
msgstr "Sken tisuffaɣ war azuzer"

#: src/pages/account-statuses.jsx:356
msgid "- Boosts"
msgstr "- Izuzar"

#: src/pages/account-statuses.jsx:362
msgid "Showing posts with media"
msgstr "Askan n tisuffaɣ s umidya"

#. placeholder {0}: tag.name
#: src/pages/account-statuses.jsx:379
msgid "Showing posts tagged with #{0}"
msgstr "Askan n tsuffaɣ yettwabdaren s #{0}"

#. placeholder {0}: date.toLocaleString(i18n.locale, { month: 'long', year: 'numeric', })
#: src/pages/account-statuses.jsx:418
msgid "Showing posts in {0}"
msgstr ""

#: src/pages/account-statuses.jsx:508
msgid "Nothing to see here yet."
msgstr "Ulac acu ara twaliḍ dagi akka ar tura."

#: src/pages/account-statuses.jsx:509
#: src/pages/public.jsx:99
#: src/pages/trending.jsx:452
msgid "Unable to load posts"
msgstr "D awezɣi alluy n tsuffaɣ"

#: src/pages/account-statuses.jsx:550
#: src/pages/account-statuses.jsx:580
msgid "Unable to fetch account info"
msgstr "D awezɣi tiririt n telɣut n umiḍan"

#. placeholder {0}: accountInstance ? ( <> {' '} (<b>{punycode.toUnicode(accountInstance)}</b>) </> ) : null
#: src/pages/account-statuses.jsx:557
msgid "Switch to account's instance {0}"
msgstr "Uɣal ɣer tummant n umiḍan {0}"

#: src/pages/account-statuses.jsx:587
msgid "Switch to my instance (<0>{currentInstance}</0>)"
msgstr "Uɣal ɣer tummant-iw (<0>{currentInstance}</0>)"

#: src/pages/account-statuses.jsx:660
msgid "Month"
msgstr "Ayyur"

#: src/pages/accounts.jsx:64
msgid "Current"
msgstr "Melmi kan"

#: src/pages/accounts.jsx:110
msgid "Default"
msgstr "Amezwer"

#: src/pages/accounts.jsx:132
msgid "Switch to this account"
msgstr "Uɣal ɣer umiḍan-a"

#: src/pages/accounts.jsx:141
msgid "Switch in new tab/window"
msgstr "Uɣal ɣer yiccer/usfaylu amaynut"

#: src/pages/accounts.jsx:155
msgid "View profile…"
msgstr "Wali amaɣnu…"

#: src/pages/accounts.jsx:173
msgid "Set as default"
msgstr "Sbadu-t d amezwer"

#. placeholder {0}: account.info.acct
#: src/pages/accounts.jsx:215
msgid "Log out <0>@{0}</0>?"
msgstr "Ffeɣ <0>@{0}</0>?"

#: src/pages/accounts.jsx:244
msgid "Log out…"
msgstr "Ffeɣ…"

#. placeholder {0}: niceDateTime(account.createdAt)
#: src/pages/accounts.jsx:251
msgid "Connected on {0} (<0/>)"
msgstr ""

#: src/pages/accounts.jsx:268
msgid "Add an existing account"
msgstr "Rnu amiḍan yellan yakan"

#: src/pages/accounts.jsx:275
msgid "Note: <0>Default</0> account will always be used for first load. Switched accounts will persist during the session."
msgstr "Tamawt: Amiḍan <0>amezwer</0> ad yezg yettwaseqdac deg usali amezwaru. Imiḍanen yettwasneflen ad qqimen ɣef teɣzi n tɣimit."

#: src/pages/bookmarks.jsx:28
msgid "No bookmarks yet. Go bookmark something!"
msgstr "Werɛad ur tesɛiḍ ara ticraḍ. Ddu ad d-tcerḍeḍ kra!"

#: src/pages/bookmarks.jsx:29
msgid "Unable to load bookmarks."
msgstr "D awezɣi ad d-alint tecraḍ n yisebtar."

#: src/pages/catchup.jsx:54
msgid "last 1 hour"
msgstr "1 usrag aneggaru"

#: src/pages/catchup.jsx:55
msgid "last 2 hours"
msgstr "2 yisragen ineggura"

#: src/pages/catchup.jsx:56
msgid "last 3 hours"
msgstr "3 yisragen ineggura"

#: src/pages/catchup.jsx:57
msgid "last 4 hours"
msgstr "4 yisragen ineggura"

#: src/pages/catchup.jsx:58
msgid "last 5 hours"
msgstr "5 yisragen ineggura"

#: src/pages/catchup.jsx:59
msgid "last 6 hours"
msgstr "6 yisragen ineggura"

#: src/pages/catchup.jsx:60
msgid "last 7 hours"
msgstr "7 yisragen ineggura"

#: src/pages/catchup.jsx:61
msgid "last 8 hours"
msgstr "8 yisragen ineggura"

#: src/pages/catchup.jsx:62
msgid "last 9 hours"
msgstr "9 yisragen ineggura"

#: src/pages/catchup.jsx:63
msgid "last 10 hours"
msgstr "10 yisragen ineggura"

#: src/pages/catchup.jsx:64
msgid "last 11 hours"
msgstr "11 yisragen ineggura"

#: src/pages/catchup.jsx:65
msgid "last 12 hours"
msgstr "12 yisragen ineggura"

#: src/pages/catchup.jsx:66
msgid "beyond 12 hours"
msgstr "ugar n 12 yisragen"

#: src/pages/catchup.jsx:73
msgid "Followed tags"
msgstr "Tibzimin yettwaḍefren"

#: src/pages/catchup.jsx:74
msgid "Groups"
msgstr "Igrawen"

#: src/pages/catchup.jsx:597
msgid "Showing {selectedFilterCategory, select, all {all posts} original {original posts} replies {replies} boosts {boosts} followedTags {followed tags} groups {groups} filtered {filtered posts}}, {sortBy, select, createdAt {{sortOrder, select, asc {oldest} desc {latest}}} reblogsCount {{sortOrder, select, asc {fewest boosts} desc {most boosts}}} favouritesCount {{sortOrder, select, asc {fewest likes} desc {most likes}}} repliesCount {{sortOrder, select, asc {fewest replies} desc {most replies}}} density {{sortOrder, select, asc {least dense} desc {most dense}}}} first{groupBy, select, account {, grouped by authors} other {}}"
msgstr "Askan n {selectedFilterCategory, select, all {akk tisuffaɣ} original {tisuffaɣ tiɣbula} replies {tiririyin} boosts {izuzar} followedTags {tibzimin yettwaḍefren} groups {igrawzen} filtered {tisuffaɣ yettwaszedgen}}, {sortBy, select, createdAt {{sortOrder, select, asc {tiqburin akk} desc {tingurra akk}}} reblogsCount {{sortOrder, select, asc {drus n yizuzar} desc {aṭas n yizuzar}}} favouritesCount {{sortOrder, select, asc {drus n yismenyaf} desc {aṭas n yismenyaf}}} repliesCount {{sortOrder, select, asc {drus n tririyin} desc {aṭas n tririyin}}} density {{sortOrder, select, asc {adday n tneẓẓit} desc {afellay n tneẓẓit}}}} amezwaru{groupBy, select, account {, ttusgarwen almend n yimeskar} other {}}"

#: src/pages/catchup.jsx:889
#: src/pages/catchup.jsx:913
msgid "Catch-up <0>beta</0>"
msgstr "Alukem <0>beta</0>"

#: src/pages/catchup.jsx:903
#: src/pages/catchup.jsx:1588
msgid "Help"
msgstr "Tallelt"

#: src/pages/catchup.jsx:919
msgid "What is this?"
msgstr "D acu-t wa?"

#: src/pages/catchup.jsx:922
msgid "Catch-up is a separate timeline for your followings, offering a high-level view at a glance, with a simple, email-inspired interface to effortlessly sort and filter through posts."
msgstr "Alukem d tasnimirt niḍen ii yineḍfaren-ik, i d-yettmuddun udem s uswir εlayen deg uṛmac n tiṭ, s ugrudem afessas igan am wudem n yimayl i ufran n tsuffaɣ d usizdeg-nsent."

#: src/pages/catchup.jsx:933
msgid "Preview of Catch-up UI"
msgstr "Taskant n ugrudem n ulukem"

#: src/pages/catchup.jsx:942
msgid "Let's catch up"
msgstr "Yebda"

#: src/pages/catchup.jsx:947
msgid "Let's catch up on the posts from your followings."
msgstr "Lukem tisuffaɣ n yineḍfaren-ik."

#: src/pages/catchup.jsx:951
msgid "Show me all posts from…"
msgstr "Sken-iyi-d akk tisuffaɣ sɣur…"

#: src/pages/catchup.jsx:974
msgid "until the max"
msgstr "alamma dayen"

#: src/pages/catchup.jsx:1004
msgid "Catch up"
msgstr "Alukem"

#: src/pages/catchup.jsx:1010
msgid "Overlaps with your last catch-up"
msgstr "Yemyikcam akked ulukem-ik aneggaru"

#. placeholder {0}: dtf.format(new Date(lastCatchupEndAt))
#: src/pages/catchup.jsx:1022
msgid "Until the last catch-up ({0})"
msgstr "Alamma d alukem aneggaru ({0})"

#: src/pages/catchup.jsx:1031
msgid "Note: your instance might only show a maximum of 800 posts in the Home timeline regardless of the time range. Could be less or more."
msgstr "Tamawt: tummant-ik tezmer kan ad tesken ugar n 800 tsuffaɣ deg tesnimirt n ugejdan akken yebɣu yili uzrar n wakud. Aya yezmer ad yili ugar neɣ drus."

#: src/pages/catchup.jsx:1041
msgid "Previously…"
msgstr "Uqbel…"

#. placeholder {0}: pc.count
#: src/pages/catchup.jsx:1059
msgid "{0, plural, one {# post} other {# posts}}"
msgstr "{0, plural, one {# tasuffeɣt} other {# tisuffaɣ}}"

#: src/pages/catchup.jsx:1069
msgid "Remove this catch-up?"
msgstr "Kkes alukem-a?"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1072
msgid "Removing Catch-up {0}"
msgstr ""

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1076
msgid "Catch-up {0} removed"
msgstr ""

#: src/pages/catchup.jsx:1090
msgid "Note: Only max 3 will be stored. The rest will be automatically removed."
msgstr "Tamawt: Ugar kan n 3 ad d-ttwarrent. Ayen d-yeqqimen ad ttwakksent s wudem awurman."

#: src/pages/catchup.jsx:1105
msgid "Fetching posts…"
msgstr "Asali n tsuffaɣ…"

#: src/pages/catchup.jsx:1108
msgid "This might take a while."
msgstr "Aya yezmer ad yeṭṭef kra n wakud."

#: src/pages/catchup.jsx:1143
msgid "Reset filters"
msgstr "Wennez imsizedgen"

#: src/pages/catchup.jsx:1151
#: src/pages/catchup.jsx:1594
msgid "Top links"
msgstr "Iseɣwan ufrinen"

#. placeholder {0}: sharers.map((s) => { const { avatarStatic, displayName } = s; return ( <button type="button" class="plain" style={{ padding: 0, }} onClick={(e) => { e.preventDefault(); e.stopPropagation(); // Reset and filter to author const { id } = s; setSelectedAuthor(id); setSelectedFilterCategory('all'); }} > <Avatar url={avatarStatic} size="s" alt={displayName} /> </button> ); })
#: src/pages/catchup.jsx:1264
msgid "Shared by {0}"
msgstr "Yebḍa-tt {0}"

#: src/pages/catchup.jsx:1319
#: src/pages/mentions.jsx:154
#: src/pages/search.jsx:329
msgid "All"
msgstr "Akk"

#. placeholder {0}: authorCountsList.length
#: src/pages/catchup.jsx:1404
msgid "{0, plural, one {# author} other {# authors}}"
msgstr "{0, plural, one {# ameskar} other {# imeskaren}}"

#: src/pages/catchup.jsx:1416
msgid "Sort"
msgstr "Semyizwer"

#: src/pages/catchup.jsx:1447
msgid "Date"
msgstr "Azemz"

#: src/pages/catchup.jsx:1451
msgid "Density"
msgstr "Taneẓẓi"

#. js-lingui-explicit-id
#: src/pages/catchup.jsx:1474
msgid "group.filter"
msgstr ""

#: src/pages/catchup.jsx:1489
msgid "Authors"
msgstr "Imeskaren"

#: src/pages/catchup.jsx:1490
msgid "None"
msgstr "Ula yiwen"

#: src/pages/catchup.jsx:1506
msgid "Show all authors"
msgstr "Wali akk imeskaren"

#: src/pages/catchup.jsx:1557
msgid "You don't have to read everything."
msgstr "Fiḥel ad teɣreḍ kullec."

#: src/pages/catchup.jsx:1558
msgid "That's all."
msgstr "D ayen kan."

#: src/pages/catchup.jsx:1566
msgid "Back to top"
msgstr "Uɣal ar tqacuct"

#: src/pages/catchup.jsx:1597
msgid "Links shared by followings, sorted by shared counts, boosts and likes."
msgstr "Iseɣwan yettwabḍan sɣur yineḍfaren, myezwaren almend n umḍan n beṭṭu, izuzar d yismenyifen."

#: src/pages/catchup.jsx:1603
msgid "Sort: Density"
msgstr "Semyizwer: Taneẓẓi"

#: src/pages/catchup.jsx:1606
msgid "Posts are sorted by information density or depth. Shorter posts are \"lighter\" while longer posts are \"heavier\". Posts with photos are \"heavier\" than posts without photos."
msgstr "Tisuffaɣ myezwarent almend n tneẓẓit n telɣut neɣ s telqayt. Tisuffaɣ timeẓẓyanin \"fessusit\" ma yella d tisuffaɣ tiɣezzfanin \"ẓẓayit\". Tisuffaɣ s tewlafin \"ẓẓayit\" ɣef tsuffaɣ war tiwlafin."

#: src/pages/catchup.jsx:1613
msgid "Group: Authors"
msgstr "Agraw: Imeskaren"

#: src/pages/catchup.jsx:1616
msgid "Posts are grouped by authors, sorted by posts count per author."
msgstr "Tisuffaɣ ttusgarwent almend n yimeskar, myizwarent almend n umḍan n tsuffaɣ i umeskar."

#: src/pages/catchup.jsx:1663
msgid "Next author"
msgstr "Ameskar uḍfir"

#: src/pages/catchup.jsx:1671
msgid "Previous author"
msgstr "Ameskar udfir"

#: src/pages/catchup.jsx:1687
msgid "Scroll to top"
msgstr "Ali d asawen"

#: src/pages/favourites.jsx:28
msgid "No likes yet. Go like something!"
msgstr ""

#: src/pages/favourites.jsx:29
msgid "Unable to load likes."
msgstr "D awezɣi asali n yismenyafen."

#: src/pages/filters.jsx:23
msgid "Home and lists"
msgstr "Agejdan akked tebdarin"

#: src/pages/filters.jsx:25
msgid "Public timelines"
msgstr "Tisnimirin tizayazin"

#: src/pages/filters.jsx:26
msgid "Conversations"
msgstr "Idiwenniyen"

#: src/pages/filters.jsx:27
msgid "Profiles"
msgstr "Imuɣna"

#: src/pages/filters.jsx:42
msgid "Never"
msgstr "Werǧin"

#: src/pages/filters.jsx:104
#: src/pages/filters.jsx:229
msgid "New filter"
msgstr "Amsizdeg amaynut"

#. placeholder {0}: filters.length
#: src/pages/filters.jsx:152
msgid "{0, plural, one {# filter} other {# filters}}"
msgstr "{0, plural, one {# imsizdeg} other {# imsizedgen}}"

#: src/pages/filters.jsx:167
msgid "Unable to load filters."
msgstr "D awezɣi ad d-alin imsizedgen."

#: src/pages/filters.jsx:171
msgid "No filters yet."
msgstr "Ulac imsizdeg akka tura."

#: src/pages/filters.jsx:178
msgid "Add filter"
msgstr "Rnu imsizdeg"

#: src/pages/filters.jsx:229
msgid "Edit filter"
msgstr "Ẓreg amsizdeg"

#: src/pages/filters.jsx:346
msgid "Unable to edit filter"
msgstr "D awezɣi asiẓreg n yimsizdeg"

#: src/pages/filters.jsx:347
msgid "Unable to create filter"
msgstr "D awezɣi timerna n yimsizdeg"

#: src/pages/filters.jsx:356
msgid "Title"
msgstr "Azwel"

#: src/pages/filters.jsx:397
msgid "Whole word"
msgstr "Awal ummid"

#: src/pages/filters.jsx:423
msgid "No keywords. Add one."
msgstr "Ulaw awalen n tsura. Rnu yiwen."

#: src/pages/filters.jsx:450
msgid "Add keyword"
msgstr "Rnu awal tasarut"

#. placeholder {0}: filteredEditKeywords.length
#: src/pages/filters.jsx:454
msgid "{0, plural, one {# keyword} other {# keywords}}"
msgstr "{0, plural, one {# awal n tsarut} other {# awalen n tsura}}"

#: src/pages/filters.jsx:467
msgid "Filter from…"
msgstr "Sizdeg seg…"

#: src/pages/filters.jsx:493
msgid "* Not implemented yet"
msgstr "* Mazal ur yebdid ara ar tura"

#: src/pages/filters.jsx:499
msgid "Status: <0><1/></0>"
msgstr "Addad: <0><1/></0>"

#: src/pages/filters.jsx:508
msgid "Change expiry"
msgstr "Senfel taggara"

#: src/pages/filters.jsx:508
msgid "Expiry"
msgstr "Keffu"

#: src/pages/filters.jsx:527
msgid "Filtered post will be…"
msgstr "Tisuffaɣ yettwasezdgen ad…"

#: src/pages/filters.jsx:538
msgid "obscured (media only)"
msgstr ""

#: src/pages/filters.jsx:552
msgid "minimized"
msgstr "yettwasemẓẓin"

#: src/pages/filters.jsx:562
msgid "hidden"
msgstr "yettwaffer"

#: src/pages/filters.jsx:579
msgid "Delete this filter?"
msgstr "Kkes imsizdeg-a?"

#: src/pages/filters.jsx:592
msgid "Unable to delete filter."
msgstr "D awezɣi tukksa n yimsizdeg."

#: src/pages/filters.jsx:625
msgid "Expired"
msgstr "Ad yekfu"

#: src/pages/filters.jsx:627
msgid "Expiring <0/>"
msgstr "Ad yekfu <0/>"

#: src/pages/filters.jsx:631
msgid "Never expires"
msgstr "Ur ikeffu ara"

#. placeholder {0}: followedHashtags.length
#: src/pages/followed-hashtags.jsx:71
msgid "{0, plural, one {# hashtag} other {# hashtags}}"
msgstr "{0, plural, one {# ahacṭag} other {# ihacṭagen}}"

#: src/pages/followed-hashtags.jsx:86
msgid "Unable to load followed hashtags."
msgstr "D awezɣi asali n yihacṭagen i yettwaḍfaren."

#: src/pages/followed-hashtags.jsx:90
msgid "No hashtags followed yet."
msgstr "Ulac ihacṭagen i yettwaḍfaren akka ar tura."

#: src/pages/following.jsx:147
msgid "Nothing to see here."
msgstr "Ulac acu ara twaliḍ dagi."

#: src/pages/following.jsx:148
#: src/pages/list.jsx:109
msgid "Unable to load posts."
msgstr "D awezɣi alluy n tsuffaɣ."

#: src/pages/hashtag.jsx:56
msgid "{hashtagTitle} (Media only) on {instance}"
msgstr "{hashtagTitle} (Amidya kan) ɣef {instance}"

#: src/pages/hashtag.jsx:57
msgid "{hashtagTitle} on {instance}"
msgstr "{hashtagTitle} ɣef {instance}"

#: src/pages/hashtag.jsx:59
msgid "{hashtagTitle} (Media only)"
msgstr "{hashtagTitle} (Amidya kan)"

#: src/pages/hashtag.jsx:60
msgid "{hashtagTitle}"
msgstr "{hashtagTitle}"

#: src/pages/hashtag.jsx:185
msgid "No one has posted anything with this tag yet."
msgstr "Ulac win i d-isuffɣen kra s tebzimt-a akka ar tura."

#: src/pages/hashtag.jsx:186
msgid "Unable to load posts with this tag"
msgstr "D awezɣi asali n tsuffaɣ s tebzimt-a"

#: src/pages/hashtag.jsx:212
msgid "Unfollow #{hashtag}?"
msgstr "Ur ṭṭafar ara #{hashtag}?"

#: src/pages/hashtag.jsx:227
msgid "Unfollowed #{hashtag}"
msgstr "Ur yettwaḍfar ara #{hashtag}"

#: src/pages/hashtag.jsx:242
msgid "Followed #{hashtag}"
msgstr "Yettwaḍfar #{hashtag}"

#: src/pages/hashtag.jsx:258
msgid "Following…"
msgstr "Yeṭṭafar…"

#: src/pages/hashtag.jsx:287
msgid "Unfeatured on profile"
msgstr "Yettwakkes seg umaɣnu"

#: src/pages/hashtag.jsx:301
msgid "Unable to unfeature on profile"
msgstr "D awezɣi tukksa seg umaɣnu"

#: src/pages/hashtag.jsx:310
#: src/pages/hashtag.jsx:326
msgid "Featured on profile"
msgstr "Yella ɣef umaɣnu-k"

#: src/pages/hashtag.jsx:398
msgid "{TOTAL_TAGS_LIMIT, plural, other {Max # tags}}"
msgstr "{TOTAL_TAGS_LIMIT, plural, one {}other {Ugar # tibzimin}}"

#: src/pages/hashtag.jsx:401
msgid "Add hashtag"
msgstr "Rnu ahacṭag"

#: src/pages/hashtag.jsx:433
msgid "Remove hashtag"
msgstr "Kkes ahacṭag"

#: src/pages/hashtag.jsx:447
msgid "{SHORTCUTS_LIMIT, plural, one {Max # shortcut reached. Unable to add shortcut.} other {Max # shortcuts reached. Unable to add shortcut.}}"
msgstr "{SHORTCUTS_LIMIT, plural, one {Amḍan afellay n # unegzum yewweḍ. D awezɣi timerna n unegzum.} other {Amḍan afellay # yinegzumen yewweḍ. D awezɣi timerna n unegzum.}}"

#: src/pages/hashtag.jsx:476
msgid "This shortcut already exists"
msgstr "Anegzum-a yella yakan"

#: src/pages/hashtag.jsx:479
msgid "Hashtag shortcut added"
msgstr "Anegzum n uhacṭag yettwarna"

#: src/pages/hashtag.jsx:485
msgid "Add to Shortcuts"
msgstr "Rnu ɣer inezgumen"

#: src/pages/hashtag.jsx:491
#: src/pages/public.jsx:141
#: src/pages/trending.jsx:481
msgid "Enter a new instance e.g. \"mastodon.social\""
msgstr "Sekcem-d aqedac amaynut, amedya \"mastodon.social\""

#: src/pages/hashtag.jsx:494
#: src/pages/public.jsx:144
#: src/pages/trending.jsx:484
msgid "Invalid instance"
msgstr "Yir tummant"

#: src/pages/hashtag.jsx:508
#: src/pages/public.jsx:158
#: src/pages/trending.jsx:496
msgid "Go to another instance…"
msgstr "Ddu ɣer uqeddac-nniḍen…"

#: src/pages/hashtag.jsx:521
#: src/pages/public.jsx:171
#: src/pages/trending.jsx:507
msgid "Go to my instance (<0>{currentInstance}</0>)"
msgstr "Ddu ɣer tummant-iw (<0>{currentInstance}</0>)"

#: src/pages/home.jsx:224
msgid "Unable to fetch notifications."
msgstr "D awazeɣi asali n yilɣa."

#: src/pages/home.jsx:245
msgid "<0>New</0> <1>Follow Requests</1>"
msgstr "<0>Amaynut</0> <1>Ḍfer issutar</1>"

#: src/pages/home.jsx:251
msgid "See all"
msgstr "Wali-ten akk"

#: src/pages/http-route.jsx:68
msgid "Resolving…"
msgstr "Iferru…"

#: src/pages/http-route.jsx:79
msgid "Unable to resolve URL"
msgstr "D awezɣi ferru n URL"

#: src/pages/list.jsx:108
msgid "Nothing yet."
msgstr "Ulac i yellan akka tura."

#: src/pages/list.jsx:201
#: src/pages/list.jsx:307
msgid "Manage members"
msgstr "Sefrek imttekkiyen"

#. placeholder {0}: account.username
#: src/pages/list.jsx:342
msgid "Remove <0>@{0}</0> from list?"
msgstr "Kkes <0>@{0}</0> seg tebdart?"

#: src/pages/list.jsx:388
msgid "Remove…"
msgstr "Kkes…"

#. placeholder {0}: lists.length
#: src/pages/lists.jsx:115
msgid "{0, plural, one {# list} other {# lists}}"
msgstr "{0, plural, one {# tabdart} other {# tibdarin}}"

#: src/pages/lists.jsx:135
msgid "No lists yet."
msgstr "Ulac tibdarinakka tura."

#: src/pages/login.jsx:122
#: src/pages/login.jsx:132
msgid "Failed to register application"
msgstr "Ur yessaweḍ ara ad ijerred asnas-nni"

#: src/pages/login.jsx:218
msgid "instance domain"
msgstr "taɣult n useqdac"

#: src/pages/login.jsx:242
msgid "e.g. “mastodon.social”"
msgstr "amedya \"mastodon.social\""

#: src/pages/login.jsx:253
msgid "Failed to log in. Please try again or try another instance."
msgstr "Ur yessaweḍ ara ad yekcem. Ttxil-k·m, ɛreḍ tikkelt nniḍen neɣ ɛreḍ aqeddac nniḍen."

#: src/pages/login.jsx:265
msgid "Continue with {selectedInstanceText}"
msgstr "Kemmel s {selectedInstanceText}"

#: src/pages/login.jsx:266
msgid "Continue"
msgstr "Kemmel"

#: src/pages/login.jsx:274
msgid "Don't have an account? Create one!"
msgstr "Ur tesεiḍ ara amiḍan? Snulfu-d yiwen!"

#: src/pages/mentions.jsx:21
msgid "Private mentions"
msgstr "Tabdarin tusligin"

#: src/pages/mentions.jsx:166
msgid "Private"
msgstr "Usligen"

#: src/pages/mentions.jsx:176
msgid "No one mentioned you :("
msgstr "Ulac win k-id-ibedren :("

#: src/pages/mentions.jsx:177
msgid "Unable to load mentions."
msgstr "D awezɣi ad d-alin ibdaren."

#: src/pages/notifications.jsx:108
msgid "You don't follow"
msgstr "Ur teṭtafareḍ ara"

#: src/pages/notifications.jsx:109
msgid "Who don't follow you"
msgstr "Anwa ur k-neṭṭafar ara"

#: src/pages/notifications.jsx:110
msgid "With a new account"
msgstr "S umiḍan amaynut"

#: src/pages/notifications.jsx:111
msgid "Who unsolicitedly private mention you"
msgstr "D anwa i k-id-ibedren s wudem uslig war ma yessuter-ak-d"

#: src/pages/notifications.jsx:112
msgid "Who are limited by server moderators"
msgstr "D anwi iwumi gan imḍebbren n uqeddac tilas"

#: src/pages/notifications.jsx:605
#: src/pages/notifications.jsx:946
msgid "Notifications settings"
msgstr "Iɣewwaṛen n wulɣu"

#: src/pages/notifications.jsx:623
msgid "New notifications"
msgstr "Ulɣuten imaynuten"

#. placeholder {0}: announcements.length
#: src/pages/notifications.jsx:634
msgid "{0, plural, one {Announcement} other {Announcements}}"
msgstr "{0, plural, one {Alɣu} other {Alɣuten}}"

#: src/pages/notifications.jsx:681
#: src/pages/settings.jsx:1206
msgid "Follow requests"
msgstr "Issutar n uḍfar"

#. placeholder {0}: followRequests.length
#: src/pages/notifications.jsx:686
msgid "{0, plural, one {# follow request} other {# follow requests}}"
msgstr "{0, plural, one {# ḍfer assuter} other {# ḍfer issutar}}"

#. placeholder {0}: notificationsPolicy.summary.pendingRequestsCount
#: src/pages/notifications.jsx:741
msgid "{0, plural, one {Filtered notifications from # person} other {Filtered notifications from # people}}"
msgstr "{0, plural, one {Ttwasezdgen yilɣa sɣur # amdan} other {Ttwasezdgen yilɣa sɣur # imdanen}}"

#: src/pages/notifications.jsx:814
msgid "Only mentions"
msgstr "Ibdaren kan"

#: src/pages/notifications.jsx:818
msgid "Today"
msgstr "Ass-a"

#: src/pages/notifications.jsx:823
msgid "You're all caught up."
msgstr "Tessawḍeḍ kullec."

#: src/pages/notifications.jsx:846
msgid "Yesterday"
msgstr "Iḍelli"

#: src/pages/notifications.jsx:894
msgid "Unable to load notifications"
msgstr "D awezɣi ad d-alin ilɣa"

#: src/pages/notifications.jsx:973
msgid "Notifications settings updated"
msgstr "Iɣewwaṛen n wulɣu ttwaleqqmen"

#: src/pages/notifications.jsx:981
msgid "Filter out notifications from people:"
msgstr "Sizdeg ilɣa n yimdanen:"

#: src/pages/notifications.jsx:995
msgid "Filter"
msgstr "Imsizdeg"

#: src/pages/notifications.jsx:998
msgid "Ignore"
msgstr "Anef"

#. placeholder {0}: niceDateTime(updatedAtDate)
#: src/pages/notifications.jsx:1071
msgid "Updated <0>{0}</0>"
msgstr "Yettwaleqqem <0>{0}</0>"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1139
msgid "View notifications from <0>@{0}</0>"
msgstr "Wali ilɣa seg <0>@{0}</0>"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1160
msgid "Notifications from <0>@{0}</0>"
msgstr "Ilɣa sɣur <0>@{0}</0>"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1228
msgid "Notifications from @{0} will not be filtered from now on."
msgstr "Ilɣa sɣur @{0} ad uɣalen ad ffren sya ar sdat."

#: src/pages/notifications.jsx:1233
msgid "Unable to accept notification request"
msgstr "D awezεi aqbal n ussuter n wulɣu"

#: src/pages/notifications.jsx:1238
msgid "Allow"
msgstr "Sireg"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1258
msgid "Notifications from @{0} will not show up in Filtered notifications from now on."
msgstr ""

#: src/pages/notifications.jsx:1263
msgid "Unable to dismiss notification request"
msgstr "D awezεi tigtin n ussuter n wulɣu"

#: src/pages/notifications.jsx:1268
msgid "Dismiss"
msgstr "Zgel"

#: src/pages/notifications.jsx:1283
msgid "Dismissed"
msgstr "Yettwazgel"

#: src/pages/public.jsx:28
msgid "Local timeline ({instance})"
msgstr "Tasuddemt tadigant ({instance})"

#: src/pages/public.jsx:29
msgid "Federated timeline ({instance})"
msgstr "Tasuddemt tasedduklant ({instance})"

#: src/pages/public.jsx:92
msgid "Local timeline"
msgstr "Tasuddemt tadigant"

#: src/pages/public.jsx:92
msgid "Federated timeline"
msgstr "Tasnimirt tazayazt tamatut"

#: src/pages/public.jsx:98
msgid "No one has posted anything yet."
msgstr "Ulac win i d-isuffɣen kra akka ar tura."

#: src/pages/public.jsx:125
msgid "Switch to Federated"
msgstr "Bren ɣer tesdduklant"

#: src/pages/public.jsx:132
msgid "Switch to Local"
msgstr "Uɣal ɣer udigan"

#: src/pages/scheduled-posts.jsx:110
msgid "No scheduled posts."
msgstr ""

#. Scheduled [in 1 day] ([Thu, Feb 27, 6:30:00 PM])
#. placeholder {0}: niceDateTime(scheduledAt, { formatOpts: { weekday: 'short', second: 'numeric', }, })
#: src/pages/scheduled-posts.jsx:207
msgid "Scheduled <0><1/></0> <2>({0})</2>"
msgstr ""

#. Scheduled [in 1 day]
#: src/pages/scheduled-posts.jsx:263
msgid "Scheduled <0><1/></0>"
msgstr ""

#: src/pages/scheduled-posts.jsx:308
msgid "Scheduled post rescheduled"
msgstr ""

#: src/pages/scheduled-posts.jsx:315
msgid "Failed to reschedule post"
msgstr ""

#: src/pages/scheduled-posts.jsx:338
msgid "Reschedule"
msgstr ""

#: src/pages/scheduled-posts.jsx:344
msgid "Delete scheduled post?"
msgstr ""

#: src/pages/scheduled-posts.jsx:352
msgid "Scheduled post deleted"
msgstr ""

#: src/pages/scheduled-posts.jsx:359
msgid "Failed to delete scheduled post"
msgstr ""

#: src/pages/search.jsx:50
msgid "Search: {q} (Posts)"
msgstr "Nadi: {q} (Tisuffaɣ)"

#: src/pages/search.jsx:53
msgid "Search: {q} (Accounts)"
msgstr "Nadi: {q} (Imiḍanen)"

#: src/pages/search.jsx:56
msgid "Search: {q} (Hashtags)"
msgstr "Nadi: {q} (Ihacṭagen)"

#: src/pages/search.jsx:59
msgid "Search: {q}"
msgstr "Nadi: {q}"

#: src/pages/search.jsx:339
#: src/pages/search.jsx:421
msgid "Hashtags"
msgstr "Ihacṭagen"

#: src/pages/search.jsx:371
#: src/pages/search.jsx:425
#: src/pages/search.jsx:495
msgid "See more"
msgstr "Wali ugar"

#: src/pages/search.jsx:397
msgid "See more accounts"
msgstr "Wali ugar n imiḍanen"

#: src/pages/search.jsx:411
msgid "No accounts found."
msgstr "Ulac imiḍanen yettwafen."

#: src/pages/search.jsx:467
msgid "See more hashtags"
msgstr "Wali ugar n ihacṭagen"

#: src/pages/search.jsx:481
msgid "No hashtags found."
msgstr "Ulac ihacṭagen i yettwafen."

#: src/pages/search.jsx:525
msgid "See more posts"
msgstr "Wali ugar n tsuffaɣ"

#: src/pages/search.jsx:539
msgid "No posts found."
msgstr "Ulac tisuffaɣ i yettwafen."

#: src/pages/search.jsx:583
msgid "Enter your search term or paste a URL above to get started."
msgstr "Sekcem awal-ik·im n unadi neɣ senteḍ URL nnig i wakken ad tebduḍ."

#: src/pages/settings.jsx:95
msgid "Settings"
msgstr "Iɣewwaṛen"

#: src/pages/settings.jsx:104
msgid "Appearance"
msgstr "Udem"

#: src/pages/settings.jsx:180
msgid "Light"
msgstr "Aceɛlal"

#: src/pages/settings.jsx:191
msgid "Dark"
msgstr "Ubrik"

#: src/pages/settings.jsx:204
msgid "Auto"
msgstr "Awurman"

#: src/pages/settings.jsx:214
msgid "Text size"
msgstr "Teɣzi n uḍris"

#. Preview of one character, in smallest size
#. Preview of one character, in largest size
#: src/pages/settings.jsx:219
#: src/pages/settings.jsx:244
msgid "A"
msgstr "A"

#: src/pages/settings.jsx:258
msgid "Display language"
msgstr "Tutlayt n uskan"

#: src/pages/settings.jsx:267
msgid "Volunteer translations"
msgstr "Iwaziwen n tsuqilin"

#: src/pages/settings.jsx:278
msgid "Posting"
msgstr "Asuffeɣ"

#: src/pages/settings.jsx:285
msgid "Default visibility"
msgstr "Abani amezwer"

#: src/pages/settings.jsx:286
#: src/pages/settings.jsx:332
msgid "Synced"
msgstr "Yemtawa"

#: src/pages/settings.jsx:311
msgid "Failed to update posting privacy"
msgstr "Ur yessaweḍ ara ad ileqqem tabaḍnit n usuffeɣ"

#: src/pages/settings.jsx:334
msgid "Synced to your instance server's settings. <0>Go to your instance ({instance}) for more settings.</0>"
msgstr "Yemtawa akked yiɣewwaren n uqeddac n tummant-ik. <0>Ddu ɣer tummant-ik ({instance}) i wugar n yiɣewwaren.</0>"

#: src/pages/settings.jsx:349
msgid "Experiments"
msgstr "Tirmatin"

#: src/pages/settings.jsx:362
msgid "Auto refresh timeline posts"
msgstr "Asmiren awurman n tsuffaɣ n tesnimirt"

#: src/pages/settings.jsx:374
msgid "Boosts carousel"
msgstr "Kaṛusel n yizuzar"

#: src/pages/settings.jsx:391
msgid "Post translation"
msgstr "Asuqqel n tsuffeɣt"

#: src/pages/settings.jsx:402
msgid "Translate to "
msgstr "Suqel ɣer "

#: src/pages/settings.jsx:413
msgid "System language ({systemTargetLanguageText})"
msgstr "Tutlayt n unagraw ({systemTargetLanguageText})"

#. placeholder {0}: snapStates.settings.contentTranslationHideLanguages .length
#: src/pages/settings.jsx:439
msgid "{0, plural, =0 {Hide \"Translate\" button for:} other {Hide \"Translate\" button for (#):}}"
msgstr "{0, plural, one {}=0 {Ffer taqeffalt \"Suqqel\" i:} other {Ffer taqeffalt \"Suqqel\" i (#):}}"

#: src/pages/settings.jsx:494
msgid "Note: This feature uses external translation services, powered by <0>{TRANSLATION_API_NAME}</0>."
msgstr ""

#: src/pages/settings.jsx:522
msgid "Auto inline translation"
msgstr "Tasuqilt tawurmant srid"

#: src/pages/settings.jsx:526
msgid "Automatically show translation for posts in timeline. Only works for <0>short</0> posts without content warning, media and poll."
msgstr "Sken tisuqilin s wudem awurman i tsuffaɣ deg tesnimirt. Tetteddu kan i tsuffaɣ <0>timeẓẓyanin</0> war alɣu n ugbur, amidya akked usisten."

#: src/pages/settings.jsx:547
msgid "GIF Picker for composer"
msgstr "Amefran n GIF i umsuddes"

#: src/pages/settings.jsx:551
msgid "Note: This feature uses external GIF search service, powered by <0>GIPHY</0>. G-rated (suitable for viewing by all ages), tracking parameters are stripped, referrer information is omitted from requests, but search queries and IP address information will still reach their servers."
msgstr "Tamawt: Tamahilt-a tesseqdac ameẓlu n unadi n GIF azɣaray, s ttawil n <0>GIPHY</0>. Asesmel G (yemmezg akked meṛṛa iwtayen), iɣewwaren n uḍfar ttwakksen, talɣut n temselɣut tettwasfeḍ seg yissutar, maca issutar n unadi d telɣut n tansa IP ad wwḍen yal tikkelt ɣer yiqeddacen-nsen."

#: src/pages/settings.jsx:580
msgid "Image description generator"
msgstr "Asaraw n uglam n tugna"

#: src/pages/settings.jsx:585
msgid "Only for new images while composing new posts."
msgstr "I tugniwin timaynutin kan mi ara taruḍ tisuffaɣ timaynutin."

#: src/pages/settings.jsx:592
msgid "Note: This feature uses external AI service, powered by <0>img-alt-api</0>. May not work well. Only for images and in English."
msgstr "Tamawt: Tamahilt-a tesseqdac ameẓlu AI azɣaray, s ttawil n <0>img-alt-api</0>. YEzmer ur iteddu ara. Slid i tugniwin yerna s Teglizit."

#: src/pages/settings.jsx:618
msgid "Server-side grouped notifications"
msgstr "Ilɣa ttusgarwen deg yidis n uqeddac"

#: src/pages/settings.jsx:622
msgid "Alpha-stage feature. Potentially improved grouping window but basic grouping logic."
msgstr "Tamahilt deg takkayt n Alfa. Asfaylu n usegrew abruyan tettwasnerna d acu tameẓla n usegrew tazadurt."

#: src/pages/settings.jsx:643
msgid "\"Cloud\" import/export for shortcuts settings"
msgstr "Aktar/asifeḍ n \"Asigna\" i yiɣewwaren n yinegzumen"

#: src/pages/settings.jsx:648
msgid "⚠️⚠️⚠️ Very experimental.<0/>Stored in your own profile’s notes. Profile (private) notes are mainly used for other profiles, and hidden for own profile."
msgstr "⚠️⚠️⚠️ D armitan ugar.<0/>Yettwasekles deg tezmilin n umaɣnu-k. Tizmilin-a n umaɣnu (tusligin) ttuseqdacent s umata ɣef yimuɣna niḍen, ffrent ɣef umaɣnu-k."

#: src/pages/settings.jsx:659
msgid "Note: This feature uses currently-logged-in instance server API."
msgstr "Tamawt: Tamahilt-a tesseqdac API n uqeddac n usesteb yellan akka tura."

#: src/pages/settings.jsx:676
msgid "Cloak mode <0>(<1>Text</1> → <2>████</2>)</0>"
msgstr "Askar uffir <0>(<1>Aḍris</1> → <2>████</2>)</0>"

#: src/pages/settings.jsx:685
msgid "Replace text as blocks, useful when taking screenshots, for privacy reasons."
msgstr "Semselsi aḍris s yiḥedren, lhan i tuṭṭfiwin n ugdil, i tiɣẓinin n tbaḍnit."

#: src/pages/settings.jsx:710
msgid "About"
msgstr "Ɣef"

#: src/pages/settings.jsx:749
msgid "<0>Built</0> by <1>@cheeaun</1>"
msgstr "<0>Built</0> sɣur <1>@cheeaun</1>"

#: src/pages/settings.jsx:778
msgid "Sponsor"
msgstr "S lmendad"

#: src/pages/settings.jsx:786
msgid "Donate"
msgstr "Mudd tawsa"

#: src/pages/settings.jsx:802
msgid "What's new"
msgstr ""

#: src/pages/settings.jsx:806
msgid "Privacy Policy"
msgstr "Tasertit tabaḍnit"

#. placeholder {0}: WEBSITE.replace(/https?:\/\//g, '').replace(/\/$/, '')
#: src/pages/settings.jsx:813
msgid "<0>Site:</0> {0}"
msgstr "<0>Asmel:</0> {0}"

#. placeholder {0}: !__FAKE_COMMIT_HASH__ && ( <span class="ib insignificant"> ( <a href={`https://github.com/cheeaun/phanpy/commit/${__COMMIT_HASH__}`} target="_blank" rel="noopener" > <RelativeTime datetime={new Date(__BUILD_TIME__)} /> </a> ) </span> )
#: src/pages/settings.jsx:820
msgid "<0>Version:</0> <1/> {0}"
msgstr "<0>Lqem:</0> <1/> {0}"

#: src/pages/settings.jsx:835
msgid "Version string copied"
msgstr "Azrir n lqem yettwanɣel"

#: src/pages/settings.jsx:838
msgid "Unable to copy version string"
msgstr "D awezɣi anɣal n uzrir n lqem"

#: src/pages/settings.jsx:1103
#: src/pages/settings.jsx:1108
msgid "Failed to update subscription. Please try again."
msgstr "Yecceḍ uleqqem n uglam. Ttxil-k, εreḍ tikkelt niḍen."

#: src/pages/settings.jsx:1114
msgid "Failed to remove subscription. Please try again."
msgstr "Yecceḍ tukksa n uglam. Ttxil-k, εreḍ tikkelt niḍen."

#: src/pages/settings.jsx:1121
msgid "Push Notifications (beta)"
msgstr "Ilɣa Push (beta)"

#: src/pages/settings.jsx:1143
msgid "Push notifications are blocked. Please enable them in your browser settings."
msgstr "Ilɣa Push ttusweḥlen. Ttxil-k rmed-iten deg yiɣewwaren n yiminig."

#. placeholder {0}: [ { value: 'all', label: t`anyone`, }, { value: 'followed', label: t`people I follow`, }, { value: 'follower', label: t`followers`, }, ].map((type) => ( <option value={type.value}>{type.label}</option> ))
#: src/pages/settings.jsx:1152
msgid "Allow from <0>{0}</0>"
msgstr "Sireg seg <0>{0}</0>"

#: src/pages/settings.jsx:1161
msgid "anyone"
msgstr "yal yiwen"

#: src/pages/settings.jsx:1165
msgid "people I follow"
msgstr "imdanen I aneḍfar"

#: src/pages/settings.jsx:1169
msgid "followers"
msgstr "imeḍfaṛen"

#: src/pages/settings.jsx:1202
msgid "Follows"
msgstr "T·Yeṭafaṛ"

#: src/pages/settings.jsx:1210
msgid "Polls"
msgstr "Isestanen"

#: src/pages/settings.jsx:1214
msgid "Post edits"
msgstr "Ibeddilen n tsuffeɣt"

#: src/pages/settings.jsx:1235
msgid "Push permission was not granted since your last login. You'll need to <0><1>log in</1> again to grant push permission</0>."
msgstr "Tisirag Push ur ttwamuddent ara seg unekcum aneggaru. Tesriḍ <0><1>ad teqqneḍ</1> tikkelt niḍen i wakken ad tmuddeḍ tisirag push</0>."

#: src/pages/settings.jsx:1251
msgid "NOTE: Push notifications only work for <0>one account</0>."
msgstr "Tamawt: Ilɣa Push teddun kan i <0>yiwen n umiḍan</0>."

#. js-lingui-explicit-id
#: src/pages/status.jsx:657
#: src/pages/status.jsx:1220
msgid "post.title"
msgstr "Tasuffeɣt"

#: src/pages/status.jsx:911
msgid "You're not logged in. Interactions (reply, boost, etc) are not possible."
msgstr "Ur teqqineḍ ara. Amiyigew (tiririt, azuzer, rtg) ur teddun ara."

#: src/pages/status.jsx:931
msgid "This post is from another instance (<0>{instance}</0>). Interactions (reply, boost, etc) are not possible."
msgstr "Tasuffeɣt-a seg tummant niḍen (<0>{instance}</0>). Amyigew (tiririt, azuzer, rtg) ur ddint ara."

#: src/pages/status.jsx:959
msgid "Error: {e}"
msgstr "Tuccḍa: {e}"

#: src/pages/status.jsx:966
msgid "Switch to my instance to enable interactions"
msgstr "Uɣal ɣer tummant-iw i urmad n umyigew"

#: src/pages/status.jsx:1068
msgid "Unable to load replies."
msgstr "D awezɣi ad d-alint tririyin."

#: src/pages/status.jsx:1180
msgid "Back"
msgstr "Tuɣalin"

#: src/pages/status.jsx:1211
msgid "Go to main post"
msgstr "Ddu ɣer tsuffeɣt tagejdant"

#. placeholder {0}: ancestors.length
#: src/pages/status.jsx:1234
msgid "{0} posts above ‒ Go to top"
msgstr "{0} n tsuffaɣ nnig - Ali d asawen"

#: src/pages/status.jsx:1281
#: src/pages/status.jsx:1344
msgid "Switch to Side Peek view"
msgstr "Ddu ɣer yidis n uskan Peek"

#: src/pages/status.jsx:1345
msgid "Switch to Full view"
msgstr "Uɣal s uskan ummid"

#: src/pages/status.jsx:1363
msgid "Show all sensitive content"
msgstr "Sken akk agbur amḥulfu"

#: src/pages/status.jsx:1368
msgid "Experimental"
msgstr "Armitan"

#: src/pages/status.jsx:1377
msgid "Unable to switch"
msgstr "D awezɣi abeddel"

#. placeholder {0}: punycode.toUnicode( postInstance, )
#: src/pages/status.jsx:1384
msgid "Switch to post's instance ({0})"
msgstr ""

#: src/pages/status.jsx:1387
msgid "Switch to post's instance"
msgstr "Uɣal ɣer tummant n tsuffeɣt"

#: src/pages/status.jsx:1445
msgid "Unable to load post"
msgstr "D awezɣi alluy n tsuffeɣt"

#. placeholder {0}: replies.length
#. placeholder {1}: shortenNumber(replies.length)
#: src/pages/status.jsx:1581
msgid "{0, plural, one {# reply} other {<0>{1}</0> replies}}"
msgstr "{0, plural, one {# tiririt} other {<0>{1}</0> tiririyin}}"

#. placeholder {0}: shortenNumber(totalComments)
#: src/pages/status.jsx:1599
msgid "{totalComments, plural, one {# comment} other {<0>{0}</0> comments}}"
msgstr "{totalComments, plural, one {# awennit} other {<0>{0}</0> iwenniten}}"

#: src/pages/status.jsx:1621
msgid "View post with its replies"
msgstr "Wali tasuffeɣt s tririyin-is"

#: src/pages/trending.jsx:77
msgid "Trending ({instance})"
msgstr "Amuceε ({instance})"

#: src/pages/trending.jsx:235
msgid "Trending News"
msgstr "Isalan mucaɛen"

#. By [Author]
#. placeholder {0}: author ? ( <NameText account={author} showAvatar /> ) : authorUrl ? ( <a href={authorUrl} target="_blank" rel="noopener" > {authorName} </a> ) : ( authorName )
#: src/pages/trending.jsx:350
msgid "By {0}"
msgstr "Sɣur {0}"

#: src/pages/trending.jsx:411
msgid "Back to showing trending posts"
msgstr "Uɣal ɣer uskan n tsuffaɣ mucaεen"

#. placeholder {0}: currentLink .replace(/^https?:\/\/(www\.)?/i, '') .replace(/\/$/, '')
#: src/pages/trending.jsx:416
msgid "Showing posts mentioning <0>{0}</0>"
msgstr "Askan n tsuffaɣ i d-ibedren <0>{0}</0>"

#: src/pages/trending.jsx:428
msgid "Trending posts"
msgstr "Tisuffaɣ mucaɛen"

#: src/pages/trending.jsx:451
msgid "No trending posts."
msgstr "Ulac tisuffaɣ mucaɛen."

#: src/pages/welcome.jsx:54
msgid "A minimalistic opinionated Mastodon web client."
msgstr "Amsaɣ n Mastodon yella yettwali-t s tmuɣli taḥeqqart."

#: src/pages/welcome.jsx:65
msgid "Log in with Mastodon"
msgstr "Qqen s Mastodon"

#: src/pages/welcome.jsx:71
msgid "Sign up"
msgstr "Jerred"

#: src/pages/welcome.jsx:78
msgid "Connect your existing Mastodon/Fediverse account.<0/>Your credentials are not stored on this server."
msgstr "Qqen amiḍan-ik Mastodon/Fedivers i yellan.<0/>Inekcam-ik ur ttwaskelsen ara ɣef uqeddac-a."

#: src/pages/welcome.jsx:95
msgid "<0>Built</0> by <1>@cheeaun</1>. <2>Privacy Policy</2>."
msgstr "<0>Yettwabna</0> sɣur <1>@cheeaun</1>. <2>Tasertit n tbaḍnit</2>."

#: src/pages/welcome.jsx:126
msgid "Screenshot of Boosts Carousel"
msgstr "Tuṭṭfa n ugdil n kaṛusel n yizuzar"

#: src/pages/welcome.jsx:130
msgid "Boosts Carousel"
msgstr "Kaṛusel n yizuzar"

#: src/pages/welcome.jsx:133
msgid "Visually separate original posts and re-shared posts (boosted posts)."
msgstr "Freq s tmuɣli tisuffaɣ tiɣbula akked tsufaɣ yettwalsen beṭṭu (tisuffaɣ yettwazuzren)."

#: src/pages/welcome.jsx:142
msgid "Screenshot of nested comments thread"
msgstr "Tuṭṭfa n ugdil n yiwenniten yimyikcamen"

#: src/pages/welcome.jsx:146
msgid "Nested comments thread"
msgstr "Azrar n yiwenniten yemyikcamen"

#: src/pages/welcome.jsx:149
msgid "Effortlessly follow conversations. Semi-collapsible replies."
msgstr "Ḍfer idiwenniyen war ussis. Tiririt s uzgen-afnaẓ."

#: src/pages/welcome.jsx:157
msgid "Screenshot of grouped notifications"
msgstr "Tuṭṭfa n ugdil n yilɣa yettusgerwen"

#: src/pages/welcome.jsx:161
msgid "Grouped notifications"
msgstr "Ilɣa ttusgarwen"

#: src/pages/welcome.jsx:164
msgid "Similar notifications are grouped and collapsed to reduce clutter."
msgstr "Ilɣa uluten ttusgarwen syen ttufenẓen i usenqes seg temterwit."

#: src/pages/welcome.jsx:173
msgid "Screenshot of multi-column UI"
msgstr "Tuṭṭfa n ugdil n ugrudem n waṭas n tgejda"

#: src/pages/welcome.jsx:177
msgid "Single or multi-column"
msgstr "Yiwet neɣ aṭas n tgejdiyin"

#: src/pages/welcome.jsx:180
msgid "By default, single column for zen-mode seekers. Configurable multi-column for power users."
msgstr "S wudem amezwer, tigejdit tasuft i i umnadi n uskar Zen. Aṭas n tgejda ttwasestabent i yiseqdacen n tsaraɣt."

#: src/pages/welcome.jsx:189
msgid "Screenshot of multi-hashtag timeline with a form to add more hashtags"
msgstr "Tuṭṭfa n ugdil n tesnimirt n yihacṭagen s tferkit i tmerna n wugar ihacṭagen"

#: src/pages/welcome.jsx:193
msgid "Multi-hashtag timeline"
msgstr "Tasnimirt n waṭas yihacṭagen"

#: src/pages/welcome.jsx:196
msgid "Up to 5 hashtags combined into a single timeline."
msgstr "Ugar n 5 yihacṭagen ttwasdukklen deg yiwet n tesnimirt."

#: src/utils/open-compose.js:24
msgid "Looks like your browser is blocking popups."
msgstr "Yettban-d iminig-ik yessewḥal isfuyla udhimen."

#: src/utils/show-compose.js:16
msgid "A draft post is currently minimized. Post or discard it before creating a new one."
msgstr "Tasuffaɣt tarewwayt tettwasemẓẓi akka tura. Suffeɣ neɣ sefsex-itt send timerna n yiwet n tmaynut."

#: src/utils/show-compose.js:21
msgid "A post is currently open. Post or discard it before creating a new one."
msgstr "Tasuffaɣt teldi akka tura. Suffeɣ neɣ sefsex-itt send timerna n yiwet n tmaynut."

