msgid ""
msgstr ""
"POT-Creation-Date: 2025-05-03 21:08+0800\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: @lingui/cli\n"
"Language: pt\n"
"Project-Id-Version: phanpy\n"
"Report-Msgid-Bugs-To: \n"
"PO-Revision-Date: 2025-07-15 13:17\n"
"Last-Translator: \n"
"Language-Team: Portuguese, Brazilian\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Crowdin-Project: phanpy\n"
"X-Crowdin-Project-ID: 703337\n"
"X-Crowdin-Language: pt-BR\n"
"X-Crowdin-File: /main/src/locales/en.po\n"
"X-Crowdin-File-ID: 18\n"

#: src/components/account-block.jsx:137
msgid "Locked"
msgstr "Bloqueado"

#. placeholder {0}: shortenNumber(statusesCount)
#: src/components/account-block.jsx:143
msgid "Posts: {0}"
msgstr "Publicações: {0}"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-block.jsx:148
msgid "Last posted: {0}"
msgstr "Última publicação: {0}"

#: src/components/account-block.jsx:163
#: src/components/account-info.jsx:710
msgid "Automated"
msgstr "Automático"

#: src/components/account-block.jsx:170
#: src/components/account-info.jsx:715
#: src/components/status.jsx:665
msgid "Group"
msgstr "Grupo"

#: src/components/account-block.jsx:180
msgid "Mutual"
msgstr "Mútuo"

#: src/components/account-block.jsx:184
#: src/components/account-info.jsx:1875
msgid "Requested"
msgstr "Solicitado"

#: src/components/account-block.jsx:188
#: src/components/account-info.jsx:1866
msgid "Following"
msgstr "Seguindo"

#: src/components/account-block.jsx:192
#: src/components/account-info.jsx:1180
msgid "Follows you"
msgstr "Segue você"

#: src/components/account-block.jsx:200
msgid "{followersCount, plural, one {# follower} other {# followers}}"
msgstr "{followersCount, plural, one {# seguidor} other {# seguidores}}"

#: src/components/account-block.jsx:209
#: src/components/account-info.jsx:758
msgid "Verified"
msgstr "Verificado"

#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#. placeholder {0}: niceDateTime(createdAt, { hideTime: true, })
#: src/components/account-block.jsx:224
#: src/components/account-info.jsx:860
msgid "Joined <0>{0}</0>"
msgstr "Juntou-se em <0>{0}</0>"

#: src/components/account-info.jsx:63
msgid "Forever"
msgstr "Eu alterar"

#: src/components/account-info.jsx:403
msgid "Unable to load account."
msgstr "Impossível carregar a conta."

#: src/components/account-info.jsx:418
msgid "Go to account page"
msgstr "Ir à página da conta"

#: src/components/account-info.jsx:447
#: src/components/account-info.jsx:780
#: src/components/account-info.jsx:810
msgid "Followers"
msgstr "Seguidores"

#. js-lingui-explicit-id
#: src/components/account-info.jsx:451
#: src/components/account-info.jsx:820
#: src/components/account-info.jsx:839
msgid "following.stats"
msgstr "Seguindo"

#: src/components/account-info.jsx:454
#: src/components/account-info.jsx:856
#: src/pages/account-statuses.jsx:487
#: src/pages/search.jsx:344
#: src/pages/search.jsx:491
msgid "Posts"
msgstr "Publicações"

#: src/components/account-info.jsx:462
#: src/components/account-info.jsx:1236
#: src/components/compose.jsx:2792
#: src/components/media-alt-modal.jsx:55
#: src/components/media-modal.jsx:363
#: src/components/status.jsx:1915
#: src/components/status.jsx:1932
#: src/components/status.jsx:2057
#: src/components/status.jsx:2686
#: src/components/status.jsx:2689
#: src/pages/account-statuses.jsx:531
#: src/pages/accounts.jsx:118
#: src/pages/hashtag.jsx:203
#: src/pages/list.jsx:171
#: src/pages/public.jsx:116
#: src/pages/scheduled-posts.jsx:89
#: src/pages/status.jsx:1305
#: src/pages/trending.jsx:474
msgid "More"
msgstr "Mais"

#: src/components/account-info.jsx:474
msgid "<0>{displayName}</0> has indicated that their new account is now:"
msgstr "<0>{displayName}</0> disse que a nova conta é:"

#: src/components/account-info.jsx:619
#: src/components/account-info.jsx:1464
msgid "Handle copied"
msgstr "Usuário copiado"

#: src/components/account-info.jsx:622
#: src/components/account-info.jsx:1467
msgid "Unable to copy handle"
msgstr "Impossível copiar o usuário"

#: src/components/account-info.jsx:628
#: src/components/account-info.jsx:1473
msgid "Copy handle"
msgstr "Copiar usuário"

#: src/components/account-info.jsx:634
msgid "Go to original profile page"
msgstr "Ir à página original do perfil"

#: src/components/account-info.jsx:652
msgid "View profile image"
msgstr "Ver foto de perfil"

#: src/components/account-info.jsx:670
msgid "View profile header"
msgstr "Ver banner de perfil"

#: src/components/account-info.jsx:686
#: src/components/account-info.jsx:1774
#: src/components/account-info.jsx:2300
msgid "Edit profile"
msgstr "Editar perfil"

#: src/components/account-info.jsx:705
msgid "In Memoriam"
msgstr "Em memória"

#: src/components/account-info.jsx:787
#: src/components/account-info.jsx:830
msgid "This user has chosen to not make this information available."
msgstr "O usuário privou esta informação."

#. placeholder {0}: ( postingStats.originals / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {1}: ( postingStats.replies / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#. placeholder {2}: ( postingStats.boosts / postingStats.total ).toLocaleString(i18n.locale || undefined, { style: 'percent', })
#: src/components/account-info.jsx:885
msgid "{0} original posts, {1} replies, {2} boosts"
msgstr "{0} publicações originais, {1} respostas, {2} impulsos"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.daysSinceLastPost
#. placeholder {2}: postingStats.daysSinceLastPost
#. placeholder {3}: postingStats.daysSinceLastPost
#. placeholder {4}: postingStats.total
#. placeholder {5}: postingStats.total
#. placeholder {6}: postingStats.daysSinceLastPost
#: src/components/account-info.jsx:901
msgid "{0, plural, one {{1, plural, one {Last 1 post in the past 1 day} other {Last 1 post in the past {2} days}}} other {{3, plural, one {Last {4} posts in the past 1 day} other {Last {5} posts in the past {6} days}}}}"
msgstr "{0, plural, one {{1, plural, one {Última publicação no último dia} other {Última publicação nos últimos {2} dias}}} other {{3, plural, one {Últimas {4} publicações no último dia} other {Últimas {5} publicações nos últimos {6} dias}}}}"

#. placeholder {0}: postingStats.total
#. placeholder {1}: postingStats.total
#: src/components/account-info.jsx:917
msgid "{0, plural, one {Last 1 post in the past year(s)} other {Last {1} posts in the past year(s)}}"
msgstr "{0, plural, one {Última publicação no(s) ano(s) passado(s)} other {Últimas {1} publicações no(s) ano(s) passado(s)}}"

#: src/components/account-info.jsx:942
#: src/pages/catchup.jsx:70
msgid "Original"
msgstr "Original"

#: src/components/account-info.jsx:946
#: src/components/status.jsx:2470
#: src/pages/catchup.jsx:71
#: src/pages/catchup.jsx:1448
#: src/pages/catchup.jsx:2061
#: src/pages/status.jsx:1024
#: src/pages/status.jsx:1651
msgid "Replies"
msgstr "Respostas"

#: src/components/account-info.jsx:950
#: src/pages/catchup.jsx:72
#: src/pages/catchup.jsx:1450
#: src/pages/catchup.jsx:2073
#: src/pages/settings.jsx:1198
msgid "Boosts"
msgstr "Impulsos"

#: src/components/account-info.jsx:956
msgid "Post stats unavailable."
msgstr "Estatísticas indisponíveis."

#: src/components/account-info.jsx:987
msgid "View post stats"
msgstr "Ver estatísticas"

#. placeholder {0}: niceDateTime(lastStatusAt, { hideTime: true, })
#: src/components/account-info.jsx:1184
msgid "Last post: <0>{0}</0>"
msgstr "Última publicação: <0>{0}</0>"

#: src/components/account-info.jsx:1198
msgid "Muted"
msgstr "Silenciado"

#: src/components/account-info.jsx:1203
msgid "Blocked"
msgstr "Bloqueado"

#: src/components/account-info.jsx:1212
msgid "Private note"
msgstr "Nota privada"

#: src/components/account-info.jsx:1269
msgid "Mention <0>@{username}</0>"
msgstr "Mencionar <0>@{username}</0>"

#: src/components/account-info.jsx:1281
msgid "Translate bio"
msgstr "Traduzir bio"

#: src/components/account-info.jsx:1292
msgid "Edit private note"
msgstr "Editar nota privada"

#: src/components/account-info.jsx:1292
msgid "Add private note"
msgstr "Adicionar nota privada"

#: src/components/account-info.jsx:1312
msgid "Notifications enabled for @{username}'s posts."
msgstr "Notificações ativadas para publicações de @{username}."

#: src/components/account-info.jsx:1313
msgid " Notifications disabled for @{username}'s posts."
msgstr " Notificações desativadas para publicações de @{username}."

#: src/components/account-info.jsx:1325
msgid "Disable notifications"
msgstr "Desativar notificações"

#: src/components/account-info.jsx:1326
msgid "Enable notifications"
msgstr "Ativar notificações"

#: src/components/account-info.jsx:1343
msgid "Boosts from @{username} enabled."
msgstr "Impulsos de @{username} ativados."

#: src/components/account-info.jsx:1344
msgid "Boosts from @{username} disabled."
msgstr "Impulsos de @{username} desativados."

#: src/components/account-info.jsx:1355
msgid "Disable boosts"
msgstr "Desativar impulsos"

#: src/components/account-info.jsx:1355
msgid "Enable boosts"
msgstr "Ativar impulsos"

#: src/components/account-info.jsx:1374
msgid "@{username} is no longer featured on your profile."
msgstr "@{username} não será mais destacado em seu perfil."

#: src/components/account-info.jsx:1384
msgid "@{username} is now featured on your profile."
msgstr "@{username} agora será destacado em seu perfil."

#: src/components/account-info.jsx:1392
msgid "Unable to unfeature @{username} on your profile."
msgstr "Incapaz de remover destaque de @{username}"

#: src/components/account-info.jsx:1396
msgid "Unable to feature @{username} on your profile."
msgstr "Incapaz de destacar @{username} em seu perfil."

#: src/components/account-info.jsx:1405
msgid "Don't feature on profile"
msgstr "Não destacar no perfil"

#: src/components/account-info.jsx:1406
#: src/pages/hashtag.jsx:333
msgid "Feature on profile"
msgstr "Destacar no perfil"

#: src/components/account-info.jsx:1415
msgid "Show featured profiles"
msgstr "Exibir destaques"

#: src/components/account-info.jsx:1430
#: src/components/account-info.jsx:1440
#: src/components/account-info.jsx:2046
msgid "Add/Remove from Lists"
msgstr "Adicionar/remover das listas"

#: src/components/account-info.jsx:1490
#: src/components/status.jsx:1337
msgid "Link copied"
msgstr "Link copiado"

#: src/components/account-info.jsx:1493
#: src/components/status.jsx:1340
msgid "Unable to copy link"
msgstr "Impossível copiar o link"

#: src/components/account-info.jsx:1499
#: src/components/shortcuts-settings.jsx:1059
#: src/components/status.jsx:1346
#: src/components/status.jsx:3464
msgid "Copy"
msgstr "Copiar"

#: src/components/account-info.jsx:1514
#: src/components/shortcuts-settings.jsx:1077
#: src/components/status.jsx:1362
msgid "Sharing doesn't seem to work."
msgstr "Este recurso não está funcionando."

#: src/components/account-info.jsx:1520
#: src/components/status.jsx:1368
msgid "Share…"
msgstr "Compartilhar…"

#: src/components/account-info.jsx:1540
msgid "Unmuted @{username}"
msgstr "@{username} dessilenciado"

#: src/components/account-info.jsx:1552
msgid "Unmute <0>@{username}</0>"
msgstr "Dessilenciar <0>@{username}</0>"

#: src/components/account-info.jsx:1568
msgid "Mute <0>@{username}</0>…"
msgstr "Silenciar <0>@{username}</0>…"

#. placeholder {0}: typeof MUTE_DURATIONS_LABELS[duration] === 'function' ? MUTE_DURATIONS_LABELS[duration]() : _(MUTE_DURATIONS_LABELS[duration])
#: src/components/account-info.jsx:1600
msgid "Muted @{username} for {0}"
msgstr "@{username} silenciado até {0}"

#: src/components/account-info.jsx:1612
msgid "Unable to mute @{username}"
msgstr "Impossível silenciar @{username}"

#: src/components/account-info.jsx:1633
msgid "Remove <0>@{username}</0> from followers?"
msgstr "Excluir <0>@{username}</0> dos seguidores?"

#: src/components/account-info.jsx:1653
msgid "@{username} removed from followers"
msgstr "@{username} excluído dos seguidores"

#: src/components/account-info.jsx:1665
msgid "Remove follower…"
msgstr "Excluir seguidor…"

#: src/components/account-info.jsx:1676
msgid "Block <0>@{username}</0>?"
msgstr "Bloquear <0>@{username}</0>?"

#: src/components/account-info.jsx:1700
msgid "Unblocked @{username}"
msgstr "@{username} desbloqueado"

#: src/components/account-info.jsx:1708
msgid "Blocked @{username}"
msgstr "@{username} bloqueado"

#: src/components/account-info.jsx:1716
msgid "Unable to unblock @{username}"
msgstr "Impossível desbloquear @{username}"

#: src/components/account-info.jsx:1718
msgid "Unable to block @{username}"
msgstr "Impossível bloquear @{username}"

#: src/components/account-info.jsx:1728
msgid "Unblock <0>@{username}</0>"
msgstr "Desbloquear <0>@{username}</0>"

#: src/components/account-info.jsx:1737
msgid "Block <0>@{username}</0>…"
msgstr "Bloquear <0>@{username}</0>…"

#: src/components/account-info.jsx:1754
msgid "Report <0>@{username}</0>…"
msgstr "Denunciar <0>@{username}</0>…"

#: src/components/account-info.jsx:1810
msgid "Withdraw follow request?"
msgstr "Excluir pedido de seguir?"

#. placeholder {0}: info.acct || info.username
#: src/components/account-info.jsx:1811
msgid "Unfollow @{0}?"
msgstr "Parar de seguir @{0}?"

#: src/components/account-info.jsx:1869
msgid "Unfollow…"
msgstr "Parar de seguir…"

#: src/components/account-info.jsx:1878
msgid "Withdraw…"
msgstr "Excluir…"

#: src/components/account-info.jsx:1885
#: src/components/account-info.jsx:1889
#: src/pages/hashtag.jsx:265
msgid "Follow"
msgstr "Seguir"

#: src/components/account-info.jsx:1986
#: src/components/account-info.jsx:2041
#: src/components/account-info.jsx:2175
#: src/components/account-info.jsx:2295
#: src/components/account-sheet.jsx:38
#: src/components/compose.jsx:891
#: src/components/compose.jsx:2748
#: src/components/compose.jsx:3228
#: src/components/compose.jsx:3437
#: src/components/compose.jsx:3667
#: src/components/drafts.jsx:59
#: src/components/embed-modal.jsx:13
#: src/components/generic-accounts.jsx:151
#: src/components/keyboard-shortcuts-help.jsx:43
#: src/components/list-add-edit.jsx:37
#: src/components/media-alt-modal.jsx:43
#: src/components/media-modal.jsx:327
#: src/components/notification-service.jsx:157
#: src/components/report-modal.jsx:118
#: src/components/shortcuts-settings.jsx:230
#: src/components/shortcuts-settings.jsx:583
#: src/components/shortcuts-settings.jsx:783
#: src/components/status.jsx:3188
#: src/components/status.jsx:3428
#: src/components/status.jsx:3937
#: src/pages/accounts.jsx:45
#: src/pages/catchup.jsx:1584
#: src/pages/filters.jsx:225
#: src/pages/list.jsx:302
#: src/pages/notifications.jsx:942
#: src/pages/scheduled-posts.jsx:259
#: src/pages/settings.jsx:90
#: src/pages/status.jsx:1392
msgid "Close"
msgstr "Fechar"

#: src/components/account-info.jsx:1991
msgid "Translated Bio"
msgstr "Bio traduzida"

#: src/components/account-info.jsx:2086
msgid "Unable to remove from list."
msgstr "Impossível remover da lista."

#: src/components/account-info.jsx:2087
msgid "Unable to add to list."
msgstr "Impossível adicionar à lista."

#: src/components/account-info.jsx:2106
#: src/pages/lists.jsx:131
msgid "Unable to load lists."
msgstr "Impossível carregar listas."

#: src/components/account-info.jsx:2110
msgid "No lists."
msgstr "Não há listas."

#: src/components/account-info.jsx:2121
#: src/components/list-add-edit.jsx:41
#: src/pages/lists.jsx:62
msgid "New list"
msgstr "Nova lista"

#. placeholder {0}: account?.username || account?.acct
#: src/components/account-info.jsx:2180
msgid "Private note about <0>@{0}</0>"
msgstr "Nota privada sobre <0>@{0}</0>"

#: src/components/account-info.jsx:2210
msgid "Unable to update private note."
msgstr "Impossível atualizar nota privada."

#: src/components/account-info.jsx:2233
#: src/components/account-info.jsx:2531
msgid "Cancel"
msgstr "Cancelar"

#: src/components/account-info.jsx:2238
msgid "Save & close"
msgstr "Salvar e fechar"

#: src/components/account-info.jsx:2355
msgid "Unable to update profile."
msgstr "Impossível atualizar perfil."

#: src/components/account-info.jsx:2362
msgid "Header picture"
msgstr "Foto do banner"

#: src/components/account-info.jsx:2414
msgid "Profile picture"
msgstr "Foto de perfil"

#: src/components/account-info.jsx:2466
#: src/components/list-add-edit.jsx:106
msgid "Name"
msgstr "Nome"

#: src/components/account-info.jsx:2479
msgid "Bio"
msgstr "Bio"

#: src/components/account-info.jsx:2492
msgid "Extra fields"
msgstr "Espaços adicionais"

#: src/components/account-info.jsx:2498
msgid "Label"
msgstr "Etiqueta"

#: src/components/account-info.jsx:2501
msgid "Content"
msgstr "Conteúdo"

#: src/components/account-info.jsx:2534
#: src/components/list-add-edit.jsx:152
#: src/components/shortcuts-settings.jsx:715
#: src/pages/filters.jsx:570
#: src/pages/notifications.jsx:1008
msgid "Save"
msgstr "Salvar"

#: src/components/account-info.jsx:2588
msgid "username"
msgstr "nome de usuário"

#: src/components/account-info.jsx:2592
msgid "server domain name"
msgstr "domínio do servidor"

#. placeholder {0}: info.username
#: src/components/account-info.jsx:2658
msgid "Profiles featured by @{0}"
msgstr "Perfis destacados por @{0}"

#: src/components/account-info.jsx:2684
msgid "No featured profiles."
msgstr "Nenhum destaque."

#: src/components/background-service.jsx:158
msgid "Cloak mode disabled"
msgstr "Modo oculto desativado"

#: src/components/background-service.jsx:158
msgid "Cloak mode enabled"
msgstr "Modo oculto ativado"

#: src/components/columns.jsx:27
#: src/components/nav-menu.jsx:181
#: src/components/shortcuts-settings.jsx:139
#: src/components/timeline.jsx:469
#: src/pages/catchup.jsx:883
#: src/pages/filters.jsx:90
#: src/pages/followed-hashtags.jsx:41
#: src/pages/home.jsx:54
#: src/pages/notifications.jsx:587
#: src/pages/scheduled-posts.jsx:74
msgid "Home"
msgstr "Início"

#: src/components/compose-button.jsx:150
#: src/compose.jsx:38
msgid "Compose"
msgstr "Escrever"

#: src/components/compose-button.jsx:177
#: src/components/nav-menu.jsx:265
#: src/pages/scheduled-posts.jsx:31
#: src/pages/scheduled-posts.jsx:78
msgid "Scheduled Posts"
msgstr "Publicações agendadas"

#: src/components/compose-button.jsx:190
msgid "Add to thread"
msgstr "Adicionar ao tópico"

#: src/components/compose.jsx:211
msgid "Take photo or video"
msgstr "Captar foto ou vídeo"

#: src/components/compose.jsx:212
msgid "Add media"
msgstr "Adicionar mídia"

#: src/components/compose.jsx:213
msgid "Add custom emoji"
msgstr "Adicionar emoji personalizado"

#: src/components/compose.jsx:214
msgid "Add GIF"
msgstr "Adicionar GIF"

#: src/components/compose.jsx:215
msgid "Add poll"
msgstr "Adicionar enquete"

#: src/components/compose.jsx:216
msgid "Schedule post"
msgstr "Agendar publicação"

#: src/components/compose.jsx:416
msgid "You have unsaved changes. Discard this post?"
msgstr "Você tem mudanças não salvas. Descartar publicação?"

#. placeholder {0}: unsupportedFiles.length
#. placeholder {1}: unsupportedFiles[0].name
#. placeholder {2}: lf.format( unsupportedFiles.map((f) => f.name), )
#: src/components/compose.jsx:654
msgid "{0, plural, one {File {1} is not supported.} other {Files {2} are not supported.}}"
msgstr "{0, plural, one {O arquivo {1} não é suportado.} other {Os arquivos {2} não são suportados.}}"

#: src/components/compose.jsx:664
#: src/components/compose.jsx:682
#: src/components/compose.jsx:1794
#: src/components/compose.jsx:1919
msgid "{maxMediaAttachments, plural, one {You can only attach up to 1 file.} other {You can only attach up to # files.}}"
msgstr "{maxMediaAttachments, plural,one {Você só pode anexar até 1 arquivo.} other {Você só pode anexar até # arquivos.}}"

#: src/components/compose.jsx:872
msgid "Pop out"
msgstr "Fechar"

#: src/components/compose.jsx:879
msgid "Minimize"
msgstr "Minimizar"

#: src/components/compose.jsx:915
msgid "Looks like you closed the parent window."
msgstr "Parece que você fechou a janela principal."

#: src/components/compose.jsx:922
msgid "Looks like you already have a compose field open in the parent window and currently publishing. Please wait for it to be done and try again later."
msgstr "Parece que você já tem um campo de edição aberto na janela principal e está atualmente publicando. Espere até terminar e tente novamente mais tarde."

#: src/components/compose.jsx:927
msgid "Looks like you already have a compose field open in the parent window. Popping in this window will discard the changes you made in the parent window. Continue?"
msgstr "Parece que você já tem um campo de edição aberto na janela principal. Abrir esta janela irá desfazer as mudanças que você fez na janela principal. Continuar?"

#: src/components/compose.jsx:970
msgid "Pop in"
msgstr "Abrir"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#. placeholder {1}: rtf.format(-replyToStatusMonthsAgo, 'month')
#: src/components/compose.jsx:980
msgid "Replying to @{0}’s post (<0>{1}</0>)"
msgstr "Respondendo à publicação de @{0} (<0>{1}</0>)"

#. placeholder {0}: replyToStatus.account.acct || replyToStatus.account.username
#: src/components/compose.jsx:990
msgid "Replying to @{0}’s post"
msgstr "Respondendo à publicação de @{0}"

#: src/components/compose.jsx:1003
msgid "Editing source post"
msgstr "Editando publicação original"

#: src/components/compose.jsx:1056
msgid "Poll must have at least 2 options"
msgstr "A enquete precisa ter ao menos 2 opções"

#: src/components/compose.jsx:1060
msgid "Some poll choices are empty"
msgstr "Certas opções estão vazias"

#: src/components/compose.jsx:1073
msgid "Some media have no descriptions. Continue?"
msgstr "Certas mídias não têm descrição. Continuar mesmo assim?"

#: src/components/compose.jsx:1125
msgid "Attachment #{i} failed"
msgstr "Anexo #{i} falhou"

#: src/components/compose.jsx:1221
#: src/components/status.jsx:2245
#: src/components/timeline.jsx:1023
msgid "Content warning"
msgstr "Aviso de conteúdo"

#: src/components/compose.jsx:1237
msgid "Content warning or sensitive media"
msgstr "Aviso de conteúdo ou mídia sensível"

#: src/components/compose.jsx:1273
#: src/components/status.jsx:99
#: src/pages/settings.jsx:318
msgid "Public"
msgstr "Público"

#: src/components/compose.jsx:1278
#: src/components/nav-menu.jsx:349
#: src/components/shortcuts-settings.jsx:165
#: src/components/status.jsx:100
msgid "Local"
msgstr "Local"

#: src/components/compose.jsx:1282
#: src/components/status.jsx:101
#: src/pages/settings.jsx:321
msgid "Unlisted"
msgstr "Não listado"

#: src/components/compose.jsx:1285
#: src/components/status.jsx:102
#: src/pages/settings.jsx:324
msgid "Followers only"
msgstr "Somente seguidores"

#: src/components/compose.jsx:1288
#: src/components/status.jsx:103
#: src/components/status.jsx:2121
msgid "Private mention"
msgstr "Menção privada"

#: src/components/compose.jsx:1297
msgid "Post your reply"
msgstr "Publicar resposta"

#: src/components/compose.jsx:1299
msgid "Edit your post"
msgstr "Editar publicação"

#: src/components/compose.jsx:1300
msgid "What are you doing?"
msgstr "O que você está fazendo?"

#: src/components/compose.jsx:1379
msgid "Mark media as sensitive"
msgstr "Marcar mídia como sensível"

#: src/components/compose.jsx:1416
msgid "Posting on <0/>"
msgstr "Publicando em <0/>"

#: src/components/compose.jsx:1447
#: src/components/compose.jsx:3286
#: src/components/shortcuts-settings.jsx:715
#: src/pages/list.jsx:388
msgid "Add"
msgstr "Adicionar"

#: src/components/compose.jsx:1675
msgid "Schedule"
msgstr "Agendar"

#: src/components/compose.jsx:1677
#: src/components/keyboard-shortcuts-help.jsx:155
#: src/components/status.jsx:1109
#: src/components/status.jsx:1895
#: src/components/status.jsx:1896
#: src/components/status.jsx:2590
msgid "Reply"
msgstr "Responder"

#: src/components/compose.jsx:1679
msgid "Update"
msgstr "Atualizar"

#: src/components/compose.jsx:1680
msgctxt "Submit button in composer"
msgid "Post"
msgstr "Publicar"

#: src/components/compose.jsx:1806
msgid "Downloading GIF…"
msgstr "Baixando GIF…"

#: src/components/compose.jsx:1834
msgid "Failed to download GIF"
msgstr "Houve um erro ao baixar GIF"

#: src/components/compose.jsx:2049
#: src/components/compose.jsx:2126
#: src/components/nav-menu.jsx:244
msgid "More…"
msgstr "Mais…"

#: src/components/compose.jsx:2562
msgid "Uploaded"
msgstr "Enviado"

#: src/components/compose.jsx:2575
msgid "Image description"
msgstr "Descrição da imagem"

#: src/components/compose.jsx:2576
msgid "Video description"
msgstr "Descrição do vídeo"

#: src/components/compose.jsx:2577
msgid "Audio description"
msgstr "Descrição do áudio"

#. placeholder {0}: prettyBytes( imageSize, )
#. placeholder {0}: prettyBytes( videoSize, )
#. placeholder {1}: prettyBytes(imageSizeLimit)
#. placeholder {1}: prettyBytes(videoSizeLimit)
#: src/components/compose.jsx:2612
#: src/components/compose.jsx:2632
msgid "File size too large. Uploading might encounter issues. Try reduce the file size from {0} to {1} or lower."
msgstr "O arquivo é muito grande. Enviá-lo pode enfrentar problemas. Reduza o tamanho do arquivo de {0} a {1} ou menor."

#. placeholder {0}: i18n.number( width, )
#. placeholder {1}: i18n.number(height)
#. placeholder {2}: i18n.number(newWidth)
#. placeholder {3}: i18n.number( newHeight, )
#: src/components/compose.jsx:2624
#: src/components/compose.jsx:2644
msgid "Dimension too large. Uploading might encounter issues. Try reduce dimension from {0}×{1}px to {2}×{3}px."
msgstr "A dimensão é muito grande. Enviá-lo pode enfrentar problemas. Reduza a dimensão de {0}×{1}px a {2}×{3}px."

#: src/components/compose.jsx:2652
msgid "Frame rate too high. Uploading might encounter issues."
msgstr "A taxa de quadros está muito alta. Enviá-lo pode enfrentar problemas."

#: src/components/compose.jsx:2712
#: src/components/compose.jsx:2962
#: src/components/shortcuts-settings.jsx:726
#: src/pages/catchup.jsx:1081
#: src/pages/filters.jsx:413
msgid "Remove"
msgstr "Excluir"

#: src/components/compose.jsx:2729
#: src/compose.jsx:84
msgid "Error"
msgstr "Erro"

#: src/components/compose.jsx:2754
msgid "Edit image description"
msgstr "Editar descrição da imagem"

#: src/components/compose.jsx:2755
msgid "Edit video description"
msgstr "Editar descrição do vídeo"

#: src/components/compose.jsx:2756
msgid "Edit audio description"
msgstr "Editar descrição do áudio"

#: src/components/compose.jsx:2801
#: src/components/compose.jsx:2850
msgid "Generating description. Please wait…"
msgstr "Gerando descrição. Por favor, espere…"

#. placeholder {0}: e.message
#: src/components/compose.jsx:2821
msgid "Failed to generate description: {0}"
msgstr "Houve um erro ao gerar descrição: {0}"

#: src/components/compose.jsx:2822
msgid "Failed to generate description"
msgstr "Houve um erro ao gerar descrição"

#: src/components/compose.jsx:2834
#: src/components/compose.jsx:2840
#: src/components/compose.jsx:2886
msgid "Generate description…"
msgstr "Gerar descrição…"

#. placeholder {0}: e?.message ? `: ${e.message}` : ''
#: src/components/compose.jsx:2873
msgid "Failed to generate description{0}"
msgstr "Houve um erro ao gerar descrição{0}"

#. placeholder {0}: localeCode2Text(lang)
#: src/components/compose.jsx:2888
msgid "({0}) <0>— experimental</0>"
msgstr "({0}) <0>— experimental</0>"

#: src/components/compose.jsx:2907
msgid "Done"
msgstr "Concluído"

#. placeholder {0}: i + 1
#: src/components/compose.jsx:2943
msgid "Choice {0}"
msgstr "Opção {0}"

#: src/components/compose.jsx:2990
msgid "Multiple choices"
msgstr "Várias opções"

#: src/components/compose.jsx:2993
msgid "Duration"
msgstr "Duração"

#: src/components/compose.jsx:3024
msgid "Remove poll"
msgstr "Excluir enquete"

#: src/components/compose.jsx:3245
msgid "Search accounts"
msgstr "Procurar contas"

#: src/components/compose.jsx:3299
#: src/components/generic-accounts.jsx:236
msgid "Error loading accounts"
msgstr "Erro ao carregar contas"

#: src/components/compose.jsx:3443
msgid "Custom emojis"
msgstr "Emojis personalizados"

#: src/components/compose.jsx:3463
msgid "Search emoji"
msgstr "Buscar emoji"

#: src/components/compose.jsx:3494
msgid "Error loading custom emojis"
msgstr "Erro ao carregar emojis personalizados"

#: src/components/compose.jsx:3505
msgid "Recently used"
msgstr "Usado recentemente"

#: src/components/compose.jsx:3506
msgid "Others"
msgstr "Outros"

#. placeholder {0}: i18n.number(emojis.length - max)
#: src/components/compose.jsx:3544
msgid "{0} more…"
msgstr "{0} mais…"

#: src/components/compose.jsx:3682
msgid "Search GIFs"
msgstr "Procurar GIFs"

#: src/components/compose.jsx:3697
msgid "Powered by GIPHY"
msgstr "Desenvolvido por GIPHY"

#: src/components/compose.jsx:3705
msgid "Type to search GIFs"
msgstr "Escreva para pesquisar GIFs"

#: src/components/compose.jsx:3803
#: src/components/media-modal.jsx:469
#: src/components/timeline.jsx:927
msgid "Previous"
msgstr "Anterior"

#: src/components/compose.jsx:3821
#: src/components/media-modal.jsx:488
#: src/components/timeline.jsx:944
msgid "Next"
msgstr "Seguinte"

#: src/components/compose.jsx:3838
msgid "Error loading GIFs"
msgstr "Erro ao carregar GIFs"

#: src/components/drafts.jsx:64
#: src/pages/settings.jsx:702
msgid "Unsent drafts"
msgstr "Rascunhos não enviados"

#: src/components/drafts.jsx:69
msgid "Looks like you have unsent drafts. Let's continue where you left off."
msgstr "Parece que você tem alguns rascunhos não enviados. Vamos continuar de onde você parou."

#: src/components/drafts.jsx:103
msgid "Delete this draft?"
msgstr "Excluir rascunho?"

#: src/components/drafts.jsx:118
msgid "Error deleting draft! Please try again."
msgstr "Erro ao excluir rascunho. Tente novamente."

#: src/components/drafts.jsx:128
#: src/components/list-add-edit.jsx:188
#: src/components/status.jsx:1512
#: src/pages/filters.jsx:603
#: src/pages/scheduled-posts.jsx:369
msgid "Delete…"
msgstr "Excluir…"

#: src/components/drafts.jsx:147
msgid "Error fetching reply-to status!"
msgstr "Erro ao obter estado de resposta!"

#: src/components/drafts.jsx:172
msgid "Delete all drafts?"
msgstr "Excluir rascunhos?"

#: src/components/drafts.jsx:190
msgid "Error deleting drafts! Please try again."
msgstr "Erro ao excluir rascunhos. Tente novamente."

#: src/components/drafts.jsx:202
msgid "Delete all…"
msgstr "Excluir tudo…"

#: src/components/drafts.jsx:210
msgid "No drafts found."
msgstr "Nenhum rascunho encontrado."

#: src/components/drafts.jsx:247
#: src/pages/catchup.jsx:1932
msgid "Poll"
msgstr "Enquete"

#: src/components/drafts.jsx:250
#: src/pages/account-statuses.jsx:367
msgid "Media"
msgstr "Mídia"

#: src/components/embed-modal.jsx:18
msgid "Open in new window"
msgstr "Abrir em nova janela"

#: src/components/follow-request-buttons.jsx:43
#: src/pages/notifications.jsx:992
msgid "Accept"
msgstr "Aceitar"

#: src/components/follow-request-buttons.jsx:69
msgid "Reject"
msgstr "Rejeitar"

#: src/components/follow-request-buttons.jsx:76
#: src/pages/notifications.jsx:1276
msgid "Accepted"
msgstr "Aceitado"

#: src/components/follow-request-buttons.jsx:80
msgid "Rejected"
msgstr "Rejeitado"

#: src/components/generic-accounts.jsx:154
#: src/components/notification.jsx:455
#: src/pages/accounts.jsx:50
#: src/pages/search.jsx:334
#: src/pages/search.jsx:367
msgid "Accounts"
msgstr "Contas"

#: src/components/generic-accounts.jsx:214
#: src/components/timeline.jsx:551
#: src/pages/list.jsx:321
#: src/pages/notifications.jsx:922
#: src/pages/search.jsx:561
#: src/pages/status.jsx:1425
msgid "Show more…"
msgstr "Mostrar mais…"

#: src/components/generic-accounts.jsx:219
#: src/components/timeline.jsx:556
#: src/pages/search.jsx:566
msgid "The end."
msgstr "Fim."

#: src/components/generic-accounts.jsx:240
msgid "Nothing to show"
msgstr "Nada para mostrar"

#: src/components/keyboard-shortcuts-help.jsx:47
#: src/components/nav-menu.jsx:368
#: src/pages/catchup.jsx:1622
msgid "Keyboard shortcuts"
msgstr "Atalhos do teclado"

#: src/components/keyboard-shortcuts-help.jsx:55
msgid "Keyboard shortcuts help"
msgstr "Ajuda com atalhos do teclado"

#: src/components/keyboard-shortcuts-help.jsx:59
#: src/pages/catchup.jsx:1647
msgid "Next post"
msgstr "Próxima publicação"

#: src/components/keyboard-shortcuts-help.jsx:63
#: src/pages/catchup.jsx:1655
msgid "Previous post"
msgstr "Publicação anterior"

#: src/components/keyboard-shortcuts-help.jsx:67
msgid "Skip carousel to next post"
msgstr "Pular carrossel para a próxima publicação"

#: src/components/keyboard-shortcuts-help.jsx:69
msgid "<0>Shift</0> + <1>j</1>"
msgstr "<0>Shift</0> + <1>j</1>"

#: src/components/keyboard-shortcuts-help.jsx:75
msgid "Skip carousel to previous post"
msgstr "Pular carrossel para a publicação anterior"

#: src/components/keyboard-shortcuts-help.jsx:77
msgid "<0>Shift</0> + <1>k</1>"
msgstr "<0>Shift</0> + <1>k</1>"

#: src/components/keyboard-shortcuts-help.jsx:83
msgid "Load new posts"
msgstr "Carregar novidades"

#: src/components/keyboard-shortcuts-help.jsx:87
#: src/pages/catchup.jsx:1679
msgid "Open post details"
msgstr "Detalhes da publicação"

#: src/components/keyboard-shortcuts-help.jsx:89
msgid "<0>Enter</0> or <1>o</1>"
msgstr "<0>Enter</0> ou <1>o</1>"

#: src/components/keyboard-shortcuts-help.jsx:96
msgid "Expand content warning or<0/>toggle expanded/collapsed thread"
msgstr "Expandir aviso de conteúdo ou<0/>alternar tópico expandido/colapsado"

#: src/components/keyboard-shortcuts-help.jsx:105
msgid "Close post or dialogs"
msgstr "Fechar publicação ou diálogos"

#: src/components/keyboard-shortcuts-help.jsx:107
msgid "<0>Esc</0> or <1>Backspace</1>"
msgstr "<0>Esc</0> ou <1>Backspace</1>"

#: src/components/keyboard-shortcuts-help.jsx:113
msgid "Focus column in multi-column mode"
msgstr "Focar coluna no modo multi-coluna"

#: src/components/keyboard-shortcuts-help.jsx:115
msgid "<0>1</0> to <1>9</1>"
msgstr "<0>1</0> a <1>9</1>"

#: src/components/keyboard-shortcuts-help.jsx:121
msgid "Focus next column in multi-column mode"
msgstr "Focar na próxima coluna no modo multi-coluna"

#: src/components/keyboard-shortcuts-help.jsx:125
msgid "Focus previous column in multi-column mode"
msgstr "Focar na coluna anterior no modo multi-coluna"

#: src/components/keyboard-shortcuts-help.jsx:129
msgid "Compose new post"
msgstr "Escrever nova publicação"

#: src/components/keyboard-shortcuts-help.jsx:133
msgid "Compose new post (new window)"
msgstr "Escrever nova publicação (nova janela)"

#: src/components/keyboard-shortcuts-help.jsx:136
msgid "<0>Shift</0> + <1>c</1>"
msgstr "<0>Shift</0> + <1>c</1>"

#: src/components/keyboard-shortcuts-help.jsx:142
msgid "Send post"
msgstr "Enviar publicação"

#: src/components/keyboard-shortcuts-help.jsx:144
msgid "<0>Ctrl</0> + <1>Enter</1> or <2>⌘</2> + <3>Enter</3>"
msgstr "<0>Ctrl</0> + <1>Enter</1> ou <2>⌘</2> + <3>Enter</3>"

#: src/components/keyboard-shortcuts-help.jsx:151
#: src/components/nav-menu.jsx:337
#: src/components/search-form.jsx:73
#: src/components/shortcuts-settings.jsx:52
#: src/components/shortcuts-settings.jsx:179
#: src/pages/search.jsx:46
#: src/pages/search.jsx:316
msgid "Search"
msgstr "Buscar"

#: src/components/keyboard-shortcuts-help.jsx:159
msgid "Reply (new window)"
msgstr "Responder (nova janela)"

#: src/components/keyboard-shortcuts-help.jsx:162
msgid "<0>Shift</0> + <1>r</1>"
msgstr "<0>Shift</0> + <1>r</1>"

#: src/components/keyboard-shortcuts-help.jsx:168
msgid "Like (favourite)"
msgstr "Curtida (favorito)"

#: src/components/keyboard-shortcuts-help.jsx:170
msgid "<0>l</0> or <1>f</1>"
msgstr "<0>l</0> ou <1>f</1>"

#: src/components/keyboard-shortcuts-help.jsx:176
#: src/components/status.jsx:1117
#: src/components/status.jsx:2617
#: src/components/status.jsx:2640
#: src/components/status.jsx:2641
msgid "Boost"
msgstr "Impulsionar"

#: src/components/keyboard-shortcuts-help.jsx:178
msgid "<0>Shift</0> + <1>b</1>"
msgstr "<0>Shift</0> + <1>b</1>"

#: src/components/keyboard-shortcuts-help.jsx:184
#: src/components/status.jsx:1180
#: src/components/status.jsx:2665
#: src/components/status.jsx:2666
msgid "Bookmark"
msgstr "Favoritar"

#: src/components/keyboard-shortcuts-help.jsx:188
msgid "Toggle Cloak mode"
msgstr "Alternar modo oculto"

#: src/components/keyboard-shortcuts-help.jsx:190
msgid "<0>Shift</0> + <1>Alt</1> + <2>k</2>"
msgstr "<0>Shift</0> + <1>Alt</1> + <2>k</2>"

#: src/components/list-add-edit.jsx:41
msgid "Edit list"
msgstr "Editar lista"

#: src/components/list-add-edit.jsx:97
msgid "Unable to edit list."
msgstr "Impossível editar lista."

#: src/components/list-add-edit.jsx:98
msgid "Unable to create list."
msgstr "Impossível criar lista."

#: src/components/list-add-edit.jsx:126
msgid "Show replies to list members"
msgstr "Mostrar respostas aos membros da lista"

#: src/components/list-add-edit.jsx:129
msgid "Show replies to people I follow"
msgstr "Mostrar respostas para quem sigo"

#: src/components/list-add-edit.jsx:132
msgid "Don't show replies"
msgstr "Não mostrar respostas"

#: src/components/list-add-edit.jsx:146
msgid "Hide posts on this list from Home/Following"
msgstr "Ocultar publicações desta lista do Início/Seguindo"

#: src/components/list-add-edit.jsx:152
#: src/pages/filters.jsx:570
msgid "Create"
msgstr "Criar"

#: src/components/list-add-edit.jsx:159
msgid "Delete this list?"
msgstr "Excluir lista?"

#: src/components/list-add-edit.jsx:178
msgid "Unable to delete list."
msgstr "Impossível excluir lista."

#: src/components/list-exclusive-badge.jsx:14
#: src/pages/list.jsx:179
#: src/pages/lists.jsx:107
msgid "Posts on this list are hidden from Home/Following"
msgstr "Publicações desta lista estão ocultas do Início/Seguindo"

#: src/components/media-alt-modal.jsx:48
#: src/components/media.jsx:51
msgid "Media description"
msgstr "Descrição da mídia"

#: src/components/media-alt-modal.jsx:67
#: src/components/status.jsx:1223
#: src/components/status.jsx:1232
#: src/components/translation-block.jsx:239
msgid "Translate"
msgstr "Traduzir"

#: src/components/media-alt-modal.jsx:78
#: src/components/status.jsx:1251
msgid "Speak"
msgstr "Falar"

#: src/components/media-modal.jsx:374
msgid "Open original media in new window"
msgstr "Abrir mídia original em nova janela"

#: src/components/media-modal.jsx:378
msgid "Open original media"
msgstr "Abrir mídia original"

#: src/components/media-modal.jsx:394
msgid "Attempting to describe image. Please wait…"
msgstr "Tentando descrever imagem. Por favor, espere…"

#: src/components/media-modal.jsx:409
msgid "Failed to describe image"
msgstr "Houve um erro ao descrever imagem"

#: src/components/media-modal.jsx:419
msgid "Describe image…"
msgstr "Descrever imagem…"

#: src/components/media-modal.jsx:443
msgid "View post"
msgstr "Ver publicação"

#: src/components/media-post.jsx:127
msgid "Sensitive media"
msgstr "Mídia sensível"

#: src/components/media-post.jsx:132
msgid "Filtered: {filterTitleStr}"
msgstr "Filtrado: {filterTitleStr}"

#: src/components/media-post.jsx:133
#: src/components/status.jsx:3767
#: src/components/status.jsx:3863
#: src/components/status.jsx:3941
#: src/components/timeline.jsx:1012
#: src/pages/catchup.jsx:75
#: src/pages/catchup.jsx:1880
msgid "Filtered"
msgstr "Filtrado"

#: src/components/media.jsx:477
msgid "Open file"
msgstr "Abrir arquivo"

#: src/components/modals.jsx:75
msgid "Post scheduled"
msgstr "Publicação agendada"

#: src/components/modals.jsx:76
msgid "Post published. Check it out."
msgstr "Publicado. Dê uma conferida."

#: src/components/modals.jsx:78
msgid "Reply scheduled"
msgstr "Resposta agendada"

#: src/components/modals.jsx:79
msgid "Reply posted. Check it out."
msgstr "Resposta publicada. Dê uma conferida."

#: src/components/modals.jsx:80
msgid "Post updated. Check it out."
msgstr "Publicação atualizada. Dê uma conferida."

#: src/components/nav-menu.jsx:123
msgid "Menu"
msgstr "Menu"

#: src/components/nav-menu.jsx:159
msgid "Reload page now to update?"
msgstr "Recarregar página para atualizar?"

#: src/components/nav-menu.jsx:171
msgid "New update available…"
msgstr "Nova atualização disponível…"

#. js-lingui-explicit-id
#: src/components/nav-menu.jsx:190
#: src/components/shortcuts-settings.jsx:140
#: src/pages/following.jsx:23
#: src/pages/following.jsx:145
msgid "following.title"
msgstr "Seguindo"

#: src/components/nav-menu.jsx:197
#: src/pages/catchup.jsx:878
msgid "Catch-up"
msgstr "Reacompanhar"

#: src/components/nav-menu.jsx:204
#: src/components/shortcuts-settings.jsx:58
#: src/components/shortcuts-settings.jsx:146
#: src/pages/home.jsx:240
#: src/pages/mentions.jsx:21
#: src/pages/mentions.jsx:174
#: src/pages/settings.jsx:1190
#: src/pages/trending.jsx:384
msgid "Mentions"
msgstr "Menções"

#: src/components/nav-menu.jsx:211
#: src/components/shortcuts-settings.jsx:49
#: src/components/shortcuts-settings.jsx:152
#: src/pages/filters.jsx:24
#: src/pages/home.jsx:86
#: src/pages/home.jsx:198
#: src/pages/notifications.jsx:117
#: src/pages/notifications.jsx:591
msgid "Notifications"
msgstr "Notificações"

#: src/components/nav-menu.jsx:214
msgid "New"
msgstr "Novo"

#: src/components/nav-menu.jsx:225
msgid "Profile"
msgstr "Perfil"

#: src/components/nav-menu.jsx:233
#: src/components/shortcuts-settings.jsx:54
#: src/components/shortcuts-settings.jsx:195
#: src/pages/bookmarks.jsx:12
#: src/pages/bookmarks.jsx:26
msgid "Bookmarks"
msgstr "Favoritos"

#: src/components/nav-menu.jsx:253
#: src/components/shortcuts-settings.jsx:55
#: src/components/shortcuts-settings.jsx:201
#: src/pages/catchup.jsx:1449
#: src/pages/catchup.jsx:2067
#: src/pages/favourites.jsx:12
#: src/pages/favourites.jsx:26
#: src/pages/settings.jsx:1194
msgid "Likes"
msgstr "Curtidas"

#: src/components/nav-menu.jsx:259
#: src/pages/followed-hashtags.jsx:15
#: src/pages/followed-hashtags.jsx:45
msgid "Followed Hashtags"
msgstr "Hashtags seguidas"

#: src/components/nav-menu.jsx:273
#: src/pages/account-statuses.jsx:333
#: src/pages/filters.jsx:55
#: src/pages/filters.jsx:94
#: src/pages/hashtag.jsx:344
msgid "Filters"
msgstr "Filtros"

#: src/components/nav-menu.jsx:281
msgid "Muted users"
msgstr "Usuários silenciados"

#: src/components/nav-menu.jsx:289
msgid "Muted users…"
msgstr "Usuários silenciados…"

#: src/components/nav-menu.jsx:296
msgid "Blocked users"
msgstr "Usuários bloqueados"

#: src/components/nav-menu.jsx:304
msgid "Blocked users…"
msgstr "Usuários bloqueados…"

#: src/components/nav-menu.jsx:316
msgid "Accounts…"
msgstr "Contas…"

#: src/components/nav-menu.jsx:326
#: src/pages/login.jsx:32
#: src/pages/login.jsx:199
#: src/pages/status.jsx:924
#: src/pages/welcome.jsx:65
msgid "Log in"
msgstr "Registrar"

#: src/components/nav-menu.jsx:343
#: src/components/shortcuts-settings.jsx:57
#: src/components/shortcuts-settings.jsx:172
#: src/pages/trending.jsx:444
msgid "Trending"
msgstr "Em Alta"

#: src/components/nav-menu.jsx:355
#: src/components/shortcuts-settings.jsx:165
msgid "Federated"
msgstr "Federado"

#: src/components/nav-menu.jsx:378
msgid "Shortcuts / Columns…"
msgstr "Atalhos / Colunas…"

#: src/components/nav-menu.jsx:388
#: src/components/nav-menu.jsx:402
msgid "Settings…"
msgstr "Opções…"

#: src/components/nav-menu.jsx:432
#: src/components/nav-menu.jsx:467
#: src/components/shortcuts-settings.jsx:50
#: src/components/shortcuts-settings.jsx:158
#: src/pages/list.jsx:127
#: src/pages/lists.jsx:18
#: src/pages/lists.jsx:54
msgid "Lists"
msgstr "Listas"

#: src/components/nav-menu.jsx:440
#: src/components/shortcuts.jsx:241
#: src/pages/list.jsx:139
msgid "All Lists"
msgstr "Todas as listas"

#: src/components/notification-service.jsx:161
msgid "Notification"
msgstr "Notificação"

#: src/components/notification-service.jsx:167
msgid "This notification is from your other account."
msgstr "Esta notificação é da sua outra conta."

#: src/components/notification-service.jsx:196
msgid "View all notifications"
msgstr "Ver todas as notificações"

#: src/components/notification.jsx:71
msgid "{account} reacted to your post with {emojiObject}"
msgstr "{account} reagiu a sua publicação com {emojiObject}"

#: src/components/notification.jsx:78
msgid "{account} published a post."
msgstr "{account} publicou algo."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:86
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted your reply.} other {{account} boosted your post.}}} other {{account} boosted {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted your reply.} other {<2><3>{1}</3> people</2> boosted your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} impulsionou sua resposta.} other {{account} impulsionou sua publicação.}}} other {{account} impulsionou {postsCount} publicações suas.}}} other {{postType, select, reply {<0><1>{0}</1> pessoa(s)</0> impulsionou(aram) sua resposta.} other {<2><3>{1}</3> pessoa(s)</2> impulsionou(aram) sua publicação.}}}}"

#. placeholder {0}: shortenNumber(count)
#: src/components/notification.jsx:129
msgid "{count, plural, =1 {{account} followed you.} other {<0><1>{0}</1> people</0> followed you.}}"
msgstr "{count, plural, =1 {{account} seguiu você.} other {<0><1>{0}</1> pessoa(s)</0> seguiu você.}}"

#: src/components/notification.jsx:143
msgid "{account} requested to follow you."
msgstr "{account} pediu para seguir você."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:152
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} liked your reply.} other {{account} liked your post.}}} other {{account} liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> liked your reply.} other {<2><3>{1}</3> people</2> liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} curtiu sua resposta.} other {{account} curtiu sua publicação.}}} other {{account} curtiu {postsCount} publicações suas.}}} other {{postType, select, reply {<0><1>{0}</1> pessoa(s)</0> curtiu(ram) sua resposta.} other {<2><3>{1}</3> pessoa(s)</2> curtiu(ram) sua publicação.}}}}"

#: src/components/notification.jsx:194
msgid "A poll you have voted in or created has ended."
msgstr "A enquete que você votou ou criou já acabou."

#: src/components/notification.jsx:195
msgid "A poll you have created has ended."
msgstr "A enquete que você criou já acabou."

#: src/components/notification.jsx:196
msgid "A poll you have voted in has ended."
msgstr "A enquete que você votou já acabou."

#: src/components/notification.jsx:197
msgid "A post you interacted with has been edited."
msgstr "Uma publicação que você interagiu foi editada."

#. placeholder {0}: shortenNumber(count)
#. placeholder {1}: shortenNumber(count)
#: src/components/notification.jsx:205
msgid "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} boosted & liked your reply.} other {{account} boosted & liked your post.}}} other {{account} boosted & liked {postsCount} of your posts.}}} other {{postType, select, reply {<0><1>{0}</1> people</0> boosted & liked your reply.} other {<2><3>{1}</3> people</2> boosted & liked your post.}}}}"
msgstr "{count, plural, =1 {{postsCount, plural, =1 {{postType, select, reply {{account} impulsionou e curtiu sua resposta.} other {{account} impulsionou e curtiu sua publicação.}}} other {{account} impulsionou e curtiu {postsCount} publicações suas.}}} other {{postType, select, reply {<0><1>{0}</1> pessoa(s)</0> impulsionou(aram) e curtiu(ram) sua resposta.} other {<2><3>{1}</3> pessoa(s)</2> impulsionou(aram) e curtiu(ram) sua publicação.}}}}"

#: src/components/notification.jsx:247
msgid "{account} signed up."
msgstr "{account} registrada."

#: src/components/notification.jsx:249
msgid "{account} reported {targetAccount}"
msgstr "{account} denunciou {targetAccount}"

#: src/components/notification.jsx:254
msgid "Lost connections with <0>{name}</0>."
msgstr "Perdeu conexões com <0>{name}</0>."

#: src/components/notification.jsx:260
msgid "Moderation warning"
msgstr "Aviso de moderação"

#: src/components/notification.jsx:265
msgid "Your {year} #Wrapstodon is here!"
msgstr "Seu #Wrapstodon {year} acaba de chegar!"

#: src/components/notification.jsx:271
msgid "An admin from <0>{from}</0> has suspended <1>{targetName}</1>, which means you can no longer receive updates from them or interact with them."
msgstr "Um administrador de <0>{from}</0> suspendeu <1>{targetName}</1></0>, O que significa que você não receberá atualizações ou interações deles."

#: src/components/notification.jsx:277
msgid "An admin from <0>{from}</0> has blocked <1>{targetName}</1>. Affected followers: {followersCount}, followings: {followingCount}."
msgstr "Um administrador de <0>{from}</0> bloqueou <1>{targetName}</1>. Seguidores afetados: {followersCount}, seguindo: {followingCount}."

#: src/components/notification.jsx:283
msgid "You have blocked <0>{targetName}</0>. Removed followers: {followersCount}, followings: {followingCount}."
msgstr "Você bloqueou <0>{targetName}</0>.\n"
"Seguidores removidos: {followersCount}, seguindo: {followingCount}."

#: src/components/notification.jsx:291
msgid "Your account has received a moderation warning."
msgstr "Sua conta recebeu um aviso de moderação."

#: src/components/notification.jsx:292
msgid "Your account has been disabled."
msgstr "Sua conta foi desativada."

#: src/components/notification.jsx:293
msgid "Some of your posts have been marked as sensitive."
msgstr "Algumas publicações suas foram marcadas como sensíveis."

#: src/components/notification.jsx:294
msgid "Some of your posts have been deleted."
msgstr "Algumas publicações suas foram excluídas."

#: src/components/notification.jsx:295
msgid "Your posts will be marked as sensitive from now on."
msgstr "Suas publicações foram marcadas como sensível de agora em diante."

#: src/components/notification.jsx:296
msgid "Your account has been limited."
msgstr "Sua conta foi limitada."

#: src/components/notification.jsx:297
msgid "Your account has been suspended."
msgstr "Sua conta foi suspensa."

#: src/components/notification.jsx:374
msgid "[Unknown notification type: {type}]"
msgstr "[Tipo de notificação desconhecida: {type}]"

#: src/components/notification.jsx:451
#: src/components/status.jsx:1194
#: src/components/status.jsx:1204
msgid "Boosted/Liked by…"
msgstr "Impulsionado/Curtido por…"

#: src/components/notification.jsx:452
msgid "Liked by…"
msgstr "Curtido por…"

#: src/components/notification.jsx:453
msgid "Boosted by…"
msgstr "Impulsionado por…"

#: src/components/notification.jsx:454
msgid "Followed by…"
msgstr "Seguido por…"

#: src/components/notification.jsx:535
#: src/components/notification.jsx:551
msgid "Learn more <0/>"
msgstr "Saiba mais <0/>"

#: src/components/notification.jsx:560
msgid "View #Wrapstodon"
msgstr "Ver #Wrapstodon"

#: src/components/notification.jsx:801
#: src/components/status.jsx:403
msgid "Read more →"
msgstr "Ler mais →"

#: src/components/poll.jsx:113
msgid "Voted"
msgstr "Votado"

#: src/components/poll.jsx:119
msgid "{optionVotesCount, plural, one {# vote} other {# votes}}"
msgstr "{optionVotesCount, plural, one {# voto} other {# votos}}"

#: src/components/poll.jsx:139
#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Hide results"
msgstr "Ocultar resultado"

#: src/components/poll.jsx:188
msgid "Vote"
msgstr "Votar"

#: src/components/poll.jsx:208
#: src/components/poll.jsx:210
#: src/pages/scheduled-posts.jsx:100
#: src/pages/status.jsx:1294
#: src/pages/status.jsx:1317
msgid "Refresh"
msgstr "Atualizar"

#: src/components/poll.jsx:222
#: src/components/poll.jsx:226
msgid "Show results"
msgstr "Exibir resultado"

#. placeholder {0}: shortenNumber(votesCount)
#. placeholder {1}: shortenNumber(votesCount)
#: src/components/poll.jsx:231
msgid "{votesCount, plural, one {<0>{0}</0> vote} other {<1>{1}</1> votes}}"
msgstr "{votesCount, plural, one {<0>{0}</0> voto} other {<1>{1}</1> votos}}"

#. placeholder {0}: shortenNumber(votersCount)
#. placeholder {1}: shortenNumber(votersCount)
#: src/components/poll.jsx:248
msgid "{votersCount, plural, one {<0>{0}</0> voter} other {<1>{1}</1> voters}}"
msgstr "{votersCount, plural, one {<0>{0}</0> votante} other {<1>{1}</1> votantes}}"

#: src/components/poll.jsx:268
msgid "Ended <0/>"
msgstr "Encerrou em <0/>"

#: src/components/poll.jsx:272
msgid "Ended"
msgstr "Encerrado"

#: src/components/poll.jsx:275
msgid "Ending <0/>"
msgstr "Encerrando em <0/>"

#: src/components/poll.jsx:279
msgid "Ending"
msgstr "Encerrando"

#. Relative time in seconds, as short as possible
#. placeholder {0}: seconds < 1 ? 1 : Math.floor(seconds)
#: src/components/relative-time.jsx:61
msgid "{0}s"
msgstr "{0}s"

#. Relative time in minutes, as short as possible
#. placeholder {0}: Math.floor(seconds / minute)
#: src/components/relative-time.jsx:66
msgid "{0}m"
msgstr "{0}m"

#. Relative time in hours, as short as possible
#. placeholder {0}: Math.floor(seconds / hour)
#: src/components/relative-time.jsx:71
msgid "{0}h"
msgstr "{0}h"

#: src/components/report-modal.jsx:30
msgid "Spam"
msgstr "Spam"

#: src/components/report-modal.jsx:31
msgid "Malicious links, fake engagement, or repetitive replies"
msgstr "Links falsos, interações falsas, ou respostas repetitivas"

#: src/components/report-modal.jsx:34
msgid "Illegal"
msgstr "Ilegal"

#: src/components/report-modal.jsx:35
msgid "Violates the law of your or the server's country"
msgstr "Viola a lei do servidor ou da sua região"

#: src/components/report-modal.jsx:38
msgid "Server rule violation"
msgstr "Regra de violação do servidor"

#: src/components/report-modal.jsx:39
msgid "Breaks specific server rules"
msgstr "Quebra regras específicas do servidor"

#: src/components/report-modal.jsx:40
msgid "Violation"
msgstr "Violação"

#: src/components/report-modal.jsx:43
msgid "Other"
msgstr "Outro"

#: src/components/report-modal.jsx:44
msgid "Issue doesn't fit other categories"
msgstr "Nenhuma das listadas"

#: src/components/report-modal.jsx:111
msgid "Report Post"
msgstr "Denunciar publicação"

#: src/components/report-modal.jsx:111
msgid "Report @{username}"
msgstr "Denunciar @{username}"

#: src/components/report-modal.jsx:147
msgid "Pending review"
msgstr "Revisão pendente"

#: src/components/report-modal.jsx:189
msgid "Post reported"
msgstr "Publicação denunciada"

#: src/components/report-modal.jsx:189
msgid "Profile reported"
msgstr "Perfil denunciado"

#: src/components/report-modal.jsx:197
msgid "Unable to report post"
msgstr "Impossível denunciar publicação"

#: src/components/report-modal.jsx:198
msgid "Unable to report profile"
msgstr "Impossível denunciar perfil"

#: src/components/report-modal.jsx:206
msgid "What's the issue with this post?"
msgstr "O que tem de errado com a publicação?"

#: src/components/report-modal.jsx:207
msgid "What's the issue with this profile?"
msgstr "O que tem de errado com o perfil?"

#: src/components/report-modal.jsx:276
msgid "Additional info"
msgstr "Informações adicionais"

#: src/components/report-modal.jsx:299
msgid "Forward to <0>{domain}</0>"
msgstr "Encaminhar para <0>{domain}</0>"

#: src/components/report-modal.jsx:309
msgid "Send Report"
msgstr "Enviar relatório"

#: src/components/report-modal.jsx:318
msgid "Muted {username}"
msgstr "Silenciou {username}"

#: src/components/report-modal.jsx:321
msgid "Unable to mute {username}"
msgstr "Impossível silenciar {username}"

#: src/components/report-modal.jsx:326
msgid "Send Report <0>+ Mute profile</0>"
msgstr "Enviar relatório <0>+ Silenciar perfil</0>"

#: src/components/report-modal.jsx:337
msgid "Blocked {username}"
msgstr "{username} bloqueado"

#: src/components/report-modal.jsx:340
msgid "Unable to block {username}"
msgstr "Impossível bloquear {username}"

#: src/components/report-modal.jsx:345
msgid "Send Report <0>+ Block profile</0>"
msgstr "Enviar relatório <0>+ Bloquear perfil</0>"

#: src/components/search-form.jsx:203
msgid "{query} <0>‒ accounts, hashtags & posts</0>"
msgstr "{query} <0>‒ contas, hashtags e publicações</0>"

#: src/components/search-form.jsx:216
msgid "Posts with <0>{query}</0>"
msgstr "Publicações com <0>{query}</0>"

#. placeholder {0}: query.replace(/^#/, '')
#: src/components/search-form.jsx:228
msgid "Posts tagged with <0>#{0}</0>"
msgstr "Publicações marcadas com <0>#{0}</0>"

#: src/components/search-form.jsx:242
msgid "Look up <0>{query}</0>"
msgstr "Procurar <0>{query}</0>"

#: src/components/search-form.jsx:253
msgid "Accounts with <0>{query}</0>"
msgstr "Contas com <0>{query}</0>"

#: src/components/shortcuts-settings.jsx:48
msgid "Home / Following"
msgstr "Inicio / Seguindo"

#: src/components/shortcuts-settings.jsx:51
msgid "Public (Local / Federated)"
msgstr "Público (Local / Federado)"

#: src/components/shortcuts-settings.jsx:53
msgid "Account"
msgstr "Conta"

#: src/components/shortcuts-settings.jsx:56
msgid "Hashtag"
msgstr "Hashtag"

#: src/components/shortcuts-settings.jsx:63
msgid "List ID"
msgstr "ID de lista"

#: src/components/shortcuts-settings.jsx:70
msgid "Local only"
msgstr "Apenas local"

#: src/components/shortcuts-settings.jsx:75
#: src/components/shortcuts-settings.jsx:84
#: src/components/shortcuts-settings.jsx:122
#: src/pages/login.jsx:203
msgid "Instance"
msgstr "Instância"

#: src/components/shortcuts-settings.jsx:78
#: src/components/shortcuts-settings.jsx:87
#: src/components/shortcuts-settings.jsx:125
msgid "Optional, e.g. mastodon.social"
msgstr "Opcional, ex.: mastodon.social"

#: src/components/shortcuts-settings.jsx:93
msgid "Search term"
msgstr "Termo de busca"

#: src/components/shortcuts-settings.jsx:96
msgid "Optional, unless for multi-column mode"
msgstr "Opcional, menos para o modo multi-coluna"

#: src/components/shortcuts-settings.jsx:113
msgid "e.g. PixelArt (Max 5, space-separated)"
msgstr "ex.: PixelArt (Máx. 5, separado por espaço)"

#: src/components/shortcuts-settings.jsx:117
#: src/pages/hashtag.jsx:360
msgid "Media only"
msgstr "Apenas mídia"

#: src/components/shortcuts-settings.jsx:235
#: src/components/shortcuts.jsx:218
msgid "Shortcuts"
msgstr "Atalhos"

#: src/components/shortcuts-settings.jsx:243
msgid "beta"
msgstr "beta"

#: src/components/shortcuts-settings.jsx:249
msgid "Specify a list of shortcuts that'll appear as:"
msgstr "Especifique uma lista de atalhos que aparecerá como:"

#: src/components/shortcuts-settings.jsx:255
msgid "Floating button"
msgstr "Botão flutuante"

#: src/components/shortcuts-settings.jsx:260
msgid "Tab/Menu bar"
msgstr "Aba/Barra do menu"

#: src/components/shortcuts-settings.jsx:265
msgid "Multi-column"
msgstr "Multi-coluna"

#: src/components/shortcuts-settings.jsx:332
msgid "Not available in current view mode"
msgstr "Indisponível no modo de vista atual"

#: src/components/shortcuts-settings.jsx:351
#: src/pages/accounts.jsx:188
msgid "Move up"
msgstr "Mover para cima"

#: src/components/shortcuts-settings.jsx:367
#: src/pages/accounts.jsx:203
msgid "Move down"
msgstr "Mover para baixo"

#: src/components/shortcuts-settings.jsx:379
#: src/components/status.jsx:1474
#: src/pages/list.jsx:195
msgid "Edit"
msgstr "Editar"

#: src/components/shortcuts-settings.jsx:400
msgid "Add more than one shortcut/column to make this work."
msgstr "Adicione mais de um atalho ou coluna para funcionar."

#: src/components/shortcuts-settings.jsx:411
msgid "No columns yet. Tap on the Add column button."
msgstr "Não há colunas ainda. Clique no botão de adicionar coluna."

#: src/components/shortcuts-settings.jsx:412
msgid "No shortcuts yet. Tap on the Add shortcut button."
msgstr "Não há atalhos ainda. Clique no botão de adicionar atalho."

#: src/components/shortcuts-settings.jsx:415
msgid "Not sure what to add?<0/>Try adding <1>Home / Following and Notifications</1> first."
msgstr "Sem certeza do que adicionar?<0/>Tente adicionando as <1>Notificações do Início / Seguindo </1> primeiramente."

#: src/components/shortcuts-settings.jsx:443
msgid "Max {SHORTCUTS_LIMIT} columns"
msgstr "Máximo {SHORTCUTS_LIMIT} colunas"

#: src/components/shortcuts-settings.jsx:444
msgid "Max {SHORTCUTS_LIMIT} shortcuts"
msgstr "Máximo {SHORTCUTS_LIMIT} atalhos"

#: src/components/shortcuts-settings.jsx:458
msgid "Import/export"
msgstr "Importar/Exportar"

#: src/components/shortcuts-settings.jsx:468
msgid "Add column…"
msgstr "Adicionar coluna…"

#: src/components/shortcuts-settings.jsx:469
msgid "Add shortcut…"
msgstr "Adicionar atalho…"

#: src/components/shortcuts-settings.jsx:516
msgid "Specific list is optional. For multi-column mode, list is required, else the column will not be shown."
msgstr "A lista específica é opcional. Para o modo multi-coluna a lista é necessária, ou a coluna não será exibida."

#: src/components/shortcuts-settings.jsx:517
msgid "For multi-column mode, search term is required, else the column will not be shown."
msgstr "Para o modo multi-coluna, procurar o termo é necessário ou a coluna não será exibida."

#: src/components/shortcuts-settings.jsx:518
msgid "Multiple hashtags are supported. Space-separated."
msgstr "Várias hashtags são suportadas. Separado por espaço."

#: src/components/shortcuts-settings.jsx:587
msgid "Edit shortcut"
msgstr "Editar atalho"

#: src/components/shortcuts-settings.jsx:587
msgid "Add shortcut"
msgstr "Adicionar atalho"

#: src/components/shortcuts-settings.jsx:623
msgid "Timeline"
msgstr "Timeline"

#: src/components/shortcuts-settings.jsx:649
msgid "List"
msgstr "Lista"

#: src/components/shortcuts-settings.jsx:788
msgid "Import/Export <0>Shortcuts</0>"
msgstr "Importar/Exportar <0>atalhos</0>"

#: src/components/shortcuts-settings.jsx:798
msgid "Import"
msgstr "Importar"

#: src/components/shortcuts-settings.jsx:806
msgid "Paste shortcuts here"
msgstr "Cole atalhos aqui"

#: src/components/shortcuts-settings.jsx:822
msgid "Downloading saved shortcuts from instance server…"
msgstr "Baixando atalhos salvos do servidor de instância…"

#: src/components/shortcuts-settings.jsx:851
msgid "Unable to download shortcuts"
msgstr "Impossível baixar atalhos"

#: src/components/shortcuts-settings.jsx:854
msgid "Download shortcuts from instance server"
msgstr "Baixar atalhos do servidor de instância"

#: src/components/shortcuts-settings.jsx:912
msgid "* Exists in current shortcuts"
msgstr "* Existem nos atuais atalhos"

#: src/components/shortcuts-settings.jsx:917
msgid "List may not work if it's from a different account."
msgstr "A lista pode não funcionar se for de uma conta diferente."

#: src/components/shortcuts-settings.jsx:927
msgid "Invalid settings format"
msgstr "Formato de opções inválido"

#: src/components/shortcuts-settings.jsx:935
msgid "Append to current shortcuts?"
msgstr "Anexar aos atalhos atuais?"

#: src/components/shortcuts-settings.jsx:938
msgid "Only shortcuts that don’t exist in current shortcuts will be appended."
msgstr "Apenas atalhos que não existem no atalho atual serão anexados."

#: src/components/shortcuts-settings.jsx:960
msgid "No new shortcuts to import"
msgstr "Não há atalhos novos para importar"

#: src/components/shortcuts-settings.jsx:975
msgid "Shortcuts imported. Exceeded max {SHORTCUTS_LIMIT}, so the rest are not imported."
msgstr "Atalhos importados. Máximo excedido {SHORTCUTS_LIMIT}, então o resto não será importado."

#: src/components/shortcuts-settings.jsx:976
#: src/components/shortcuts-settings.jsx:1000
msgid "Shortcuts imported"
msgstr "Atalhos importados"

#: src/components/shortcuts-settings.jsx:986
msgid "Import & append…"
msgstr "Importar e anexar…"

#: src/components/shortcuts-settings.jsx:994
msgid "Override current shortcuts?"
msgstr "Substituir atalhos atuais?"

#: src/components/shortcuts-settings.jsx:995
msgid "Import shortcuts?"
msgstr "Importar atalhos?"

#: src/components/shortcuts-settings.jsx:1009
msgid "or override…"
msgstr "ou substituir…"

#: src/components/shortcuts-settings.jsx:1009
msgid "Import…"
msgstr "Importar…"

#: src/components/shortcuts-settings.jsx:1018
msgid "Export"
msgstr "Exportar"

#: src/components/shortcuts-settings.jsx:1033
msgid "Shortcuts copied"
msgstr "Atalhos copiados"

#: src/components/shortcuts-settings.jsx:1036
msgid "Unable to copy shortcuts"
msgstr "Impossível copiar atalhos"

#: src/components/shortcuts-settings.jsx:1050
msgid "Shortcut settings copied"
msgstr "Opções de atalhos copiados"

#: src/components/shortcuts-settings.jsx:1053
msgid "Unable to copy shortcut settings"
msgstr "Impossível copiar opções de atalho"

#: src/components/shortcuts-settings.jsx:1083
msgid "Share"
msgstr "Compartilhar"

#: src/components/shortcuts-settings.jsx:1122
msgid "Saving shortcuts to instance server…"
msgstr "Salvando atalhos no servidor de instância…"

#: src/components/shortcuts-settings.jsx:1129
msgid "Shortcuts saved"
msgstr "Atalhos salvos"

#: src/components/shortcuts-settings.jsx:1134
msgid "Unable to save shortcuts"
msgstr "Impossível salvar atalhos"

#: src/components/shortcuts-settings.jsx:1137
msgid "Sync to instance server"
msgstr "Sincronizar ao servidor de instância"

#. placeholder {0}: shortcutsStr.length
#: src/components/shortcuts-settings.jsx:1145
msgid "{0, plural, one {# character} other {# characters}}"
msgstr "{0, plural, one {# carácter} other {# carácteres}}"

#: src/components/shortcuts-settings.jsx:1157
msgid "Raw Shortcuts JSON"
msgstr "Atalhos brutos JSON"

#: src/components/shortcuts-settings.jsx:1170
msgid "Import/export settings from/to instance server (Very experimental)"
msgstr "Importar/exportar opções do/para servidor de instância (Muito experimental)"

#: src/components/status.jsx:277
msgid "Unable to format math"
msgstr "Incapaz de formatar matemática"

#: src/components/status.jsx:292
msgid "Math expressions found."
msgstr "Encontrado expressões matemáticas."

#: src/components/status.jsx:294
msgid "Show markup"
msgstr "Exibir marcação"

#: src/components/status.jsx:294
msgid "Format math"
msgstr "Formatar matemática"

#: src/components/status.jsx:689
msgid "<0/> <1>boosted</1>"
msgstr "<0/> <1>impulsionou</1>"

#: src/components/status.jsx:792
msgid "Sorry, your current logged-in instance can't interact with this post from another instance."
msgstr "Desculpe, a atual instância registrada não pode interagir com esta publicação de outra instância."

#. placeholder {0}: username || acct
#: src/components/status.jsx:946
msgid "Unliked @{0}'s post"
msgstr "Não curtiu a publicação de @{0}"

#. placeholder {0}: username || acct
#: src/components/status.jsx:947
msgid "Liked @{0}'s post"
msgstr "Curtiu a publicação de @{0}"

#. placeholder {0}: username || acct
#: src/components/status.jsx:986
msgid "Unbookmarked @{0}'s post"
msgstr "Desfavoritou a publicação de @{0}"

#. placeholder {0}: username || acct
#: src/components/status.jsx:987
msgid "Bookmarked @{0}'s post"
msgstr "Favoritou a publicação de @{0}"

#: src/components/status.jsx:1086
msgid "Some media have no descriptions."
msgstr "Algumas das mídias não têm descrição."

#. placeholder {0}: rtf.format(-statusMonthsAgo, 'month')
#: src/components/status.jsx:1093
msgid "Old post (<0>{0}</0>)"
msgstr "Publicação antiga (<0>{0}</0>)"

#: src/components/status.jsx:1117
#: src/components/status.jsx:1157
#: src/components/status.jsx:2617
#: src/components/status.jsx:2640
msgid "Unboost"
msgstr "Desimpulsionar"

#: src/components/status.jsx:1133
#: src/components/status.jsx:2632
msgid "Quote"
msgstr "Citar"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1145
#: src/components/status.jsx:1611
msgid "Unboosted @{0}'s post"
msgstr "Desimpulsionou a publicação de @{0}"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1146
#: src/components/status.jsx:1612
msgid "Boosted @{0}'s post"
msgstr "Impulsionou a publicação de @{0}"

#: src/components/status.jsx:1158
msgid "Boost…"
msgstr "Impulsionar…"

#: src/components/status.jsx:1170
#: src/components/status.jsx:1905
#: src/components/status.jsx:2653
msgid "Unlike"
msgstr "Descurtir"

#: src/components/status.jsx:1171
#: src/components/status.jsx:1905
#: src/components/status.jsx:1906
#: src/components/status.jsx:2653
#: src/components/status.jsx:2654
msgid "Like"
msgstr "Curtir"

#: src/components/status.jsx:1180
#: src/components/status.jsx:2665
msgid "Unbookmark"
msgstr "Desfavoritar"

#: src/components/status.jsx:1263
msgid "Post text copied"
msgstr "Texto da publicação copiado"

#: src/components/status.jsx:1266
msgid "Unable to copy post text"
msgstr "Incapaz de copiar o texto da publicação"

#: src/components/status.jsx:1272
msgid "Copy post text"
msgstr "Copiar texto da publicação"

#. placeholder {0}: username || acct
#: src/components/status.jsx:1290
msgid "View post by <0>@{0}</0>"
msgstr "Ver publicação de <0>@{0}</0>"

#: src/components/status.jsx:1311
msgid "Show Edit History"
msgstr "Exibir histórico de edições"

#: src/components/status.jsx:1314
msgid "Edited: {editedDateText}"
msgstr "Editado: {editedDateText}"

#: src/components/status.jsx:1381
#: src/components/status.jsx:3433
msgid "Embed post"
msgstr "Incorporar publicação"

#: src/components/status.jsx:1395
msgid "Conversation unmuted"
msgstr "Conversa dessilenciada"

#: src/components/status.jsx:1395
msgid "Conversation muted"
msgstr "Conversa silenciada"

#: src/components/status.jsx:1401
msgid "Unable to unmute conversation"
msgstr "Impossível dessilenciar conversa"

#: src/components/status.jsx:1402
msgid "Unable to mute conversation"
msgstr "Impossível silenciar conversa"

#: src/components/status.jsx:1411
msgid "Unmute conversation"
msgstr "Dessilenciar conversa"

#: src/components/status.jsx:1418
msgid "Mute conversation"
msgstr "Silenciar conversa"

#: src/components/status.jsx:1434
msgid "Post unpinned from profile"
msgstr "Publicação desafixada do perfil"

#: src/components/status.jsx:1435
msgid "Post pinned to profile"
msgstr "Publicação fixada no perfil"

#: src/components/status.jsx:1440
msgid "Unable to unpin post"
msgstr "Impossível desafixar publicação"

#: src/components/status.jsx:1440
msgid "Unable to pin post"
msgstr "Impossível fixar publicação"

#: src/components/status.jsx:1449
msgid "Unpin from profile"
msgstr "Desafixar do perfil"

#: src/components/status.jsx:1456
msgid "Pin to profile"
msgstr "Fixar ao perfil"

#: src/components/status.jsx:1485
msgid "Delete this post?"
msgstr "Excluir publicação?"

#: src/components/status.jsx:1501
msgid "Post deleted"
msgstr "Publicação excluída"

#: src/components/status.jsx:1504
msgid "Unable to delete post"
msgstr "Impossível excluir publicação"

#: src/components/status.jsx:1532
msgid "Report post…"
msgstr "Denunciar publicação…"

#: src/components/status.jsx:1906
#: src/components/status.jsx:1942
#: src/components/status.jsx:2654
msgid "Liked"
msgstr "Curtido"

#: src/components/status.jsx:1939
#: src/components/status.jsx:2641
msgid "Boosted"
msgstr "Impulsionado"

#: src/components/status.jsx:1949
#: src/components/status.jsx:2666
msgid "Bookmarked"
msgstr "Favoritado"

#: src/components/status.jsx:1953
msgid "Pinned"
msgstr "Fixado"

#: src/components/status.jsx:1999
#: src/components/status.jsx:2478
msgid "Deleted"
msgstr "Excluído"

#: src/components/status.jsx:2040
msgid "{repliesCount, plural, one {# reply} other {# replies}}"
msgstr "{repliesCount, plural, one {# resposta} other {# respostas}}"

#. placeholder {0}: snapStates.statusThreadNumber[sKey] ? ` ${snapStates.statusThreadNumber[sKey]}/X` : ''
#: src/components/status.jsx:2130
msgid "Thread{0}"
msgstr "Tópico{0}"

#: src/components/status.jsx:2208
#: src/components/status.jsx:2270
#: src/components/status.jsx:2374
msgid "Show less"
msgstr "Mostrar menos"

#: src/components/status.jsx:2208
#: src/components/status.jsx:2270
msgid "Show content"
msgstr "Mostrar conteúdo"

#. placeholder {0}: filterInfo.titlesStr
#. placeholder {0}: filterInfo?.titlesStr
#: src/components/status.jsx:2370
#: src/pages/catchup.jsx:1879
msgid "Filtered: {0}"
msgstr "Filtrado: {0}"

#: src/components/status.jsx:2374
msgid "Show media"
msgstr "Mostrar mídia"

#: src/components/status.jsx:2514
msgid "Edited"
msgstr "Editado"

#: src/components/status.jsx:2591
msgid "Comments"
msgstr "Comentários"

#. More from [Author]
#: src/components/status.jsx:2891
msgid "More from <0/>"
msgstr "Mais de <0/>"

#: src/components/status.jsx:3193
msgid "Edit History"
msgstr "Histórico de edições"

#: src/components/status.jsx:3197
msgid "Failed to load history"
msgstr "Houve um erro ao carregar histórico"

#: src/components/status.jsx:3202
#: src/pages/annual-report.jsx:45
msgid "Loading…"
msgstr "Carregando…"

#: src/components/status.jsx:3438
msgid "HTML Code"
msgstr "Código HTML"

#: src/components/status.jsx:3455
msgid "HTML code copied"
msgstr "Código HTML copiado"

#: src/components/status.jsx:3458
msgid "Unable to copy HTML code"
msgstr "Impossível copiar código HTML"

#: src/components/status.jsx:3470
msgid "Media attachments:"
msgstr "Anexos de mídia:"

#: src/components/status.jsx:3492
msgid "Account Emojis:"
msgstr "Emojis da conta:"

#: src/components/status.jsx:3523
#: src/components/status.jsx:3568
msgid "static URL"
msgstr "URL estático"

#: src/components/status.jsx:3537
msgid "Emojis:"
msgstr "Emojis:"

#: src/components/status.jsx:3582
msgid "Notes:"
msgstr "Notas:"

#: src/components/status.jsx:3586
msgid "This is static, unstyled and scriptless. You may need to apply your own styles and edit as needed."
msgstr "Aqui é estático, sem estilo e programação. Talvez você precise aplicar seus estilos e editar como for necessário."

#: src/components/status.jsx:3592
msgid "Polls are not interactive, becomes a list with vote counts."
msgstr "Enquetes não são interativas, ela torna-se uma lista com contagem de votos."

#: src/components/status.jsx:3597
msgid "Media attachments can be images, videos, audios or any file types."
msgstr "Anexos de mídia pode ser imagens, vídeos, áudios ou qualquer arquivo."

#: src/components/status.jsx:3603
msgid "Post could be edited or deleted later."
msgstr "Publicações podem ser editadas ou excluídas depois."

#: src/components/status.jsx:3609
msgid "Preview"
msgstr "Prévia"

#: src/components/status.jsx:3618
msgid "Note: This preview is lightly styled."
msgstr "Nota: Esta prévia tem um estilo levemente padronizado."

#. [Name] [Visibility icon] boosted
#: src/components/status.jsx:3871
msgid "<0/> <1/> boosted"
msgstr "<0/> <1/> impulsionou"

#: src/components/status.jsx:3973
msgid "Post hidden by your filters"
msgstr "Publicação oculta por seus filtros"

#: src/components/status.jsx:3974
msgid "Post removed by author."
msgstr "Publicação removida pelo autor."

#: src/components/status.jsx:3975
msgid "You’re not authorized to view this post."
msgstr "Você não tem permissão para visualizar esta publicação."

#: src/components/status.jsx:3976
msgid "Post pending author approval."
msgstr "Publicação aguardando resposta do autor."

#: src/components/status.jsx:3977
#: src/components/status.jsx:3978
msgid "Quoting not allowed by the author."
msgstr "O autor não permite citações."

#: src/components/timeline.jsx:485
#: src/pages/settings.jsx:1218
msgid "New posts"
msgstr "Novas publicações"

#: src/components/timeline.jsx:586
#: src/pages/home.jsx:228
#: src/pages/notifications.jsx:898
#: src/pages/status.jsx:1077
#: src/pages/status.jsx:1454
msgid "Try again"
msgstr "Tentar novamente"

#. placeholder {0}: fItems.length
#: src/components/timeline.jsx:621
msgid "{0, plural, one {# Boost} other {# Boosts}}"
msgstr "{0, plural, one {# Impulso} other {# Impulsos}}"

#: src/components/timeline.jsx:626
msgid "Pinned posts"
msgstr "Publicações fixadas"

#: src/components/timeline.jsx:985
#: src/components/timeline.jsx:992
#: src/pages/catchup.jsx:1897
msgid "Thread"
msgstr "Tópico"

#. placeholder {0}: filterInfo.titlesStr
#: src/components/timeline.jsx:1007
msgid "<0>Filtered</0>: <1>{0}</1>"
msgstr "<0>Filtrado</0>: <1>{0}</1>"

#: src/components/translation-block.jsx:196
msgid "Auto-translated from {sourceLangText}"
msgstr "Traduzido automaticamente de {sourceLangText}"

#: src/components/translation-block.jsx:234
msgid "Translating…"
msgstr "Traduzindo…"

#: src/components/translation-block.jsx:237
msgid "Translate from {sourceLangText} (auto-detected)"
msgstr "Traduzir do {sourceLangText} (detectado automaticamente)"

#: src/components/translation-block.jsx:238
msgid "Translate from {sourceLangText}"
msgstr "Traduzir do {sourceLangText}"

#. placeholder {0}: detectedLang ?? '…'
#: src/components/translation-block.jsx:266
msgid "Auto ({0})"
msgstr "Auto ({0})"

#: src/components/translation-block.jsx:279
msgid "Failed to translate"
msgstr "Houve um erro ao traduzir"

#: src/compose.jsx:33
msgid "Editing source status"
msgstr "Editando a mensagem original"

#. placeholder {0}: replyToStatus.account?.acct || replyToStatus.account?.username
#: src/compose.jsx:35
msgid "Replying to @{0}"
msgstr "Respondendo à @{0}"

#: src/compose.jsx:63
msgid "You may close this page now."
msgstr "Você deve fechar esta página agora."

#: src/compose.jsx:71
msgid "Close window"
msgstr "Fechar janela"

#: src/compose.jsx:87
msgid "Login required."
msgstr "Requer registro."

#: src/compose.jsx:91
#: src/pages/annual-report.jsx:164
#: src/pages/http-route.jsx:91
#: src/pages/login.jsx:280
msgid "Go home"
msgstr "Voltar ao início"

#: src/pages/account-statuses.jsx:238
msgid "Account posts"
msgstr "Publicações da conta"

#: src/pages/account-statuses.jsx:245
msgid "{accountDisplay} (+ Replies)"
msgstr "{accountDisplay} (+ Respostas)"

#: src/pages/account-statuses.jsx:247
msgid "{accountDisplay} (- Boosts)"
msgstr "{accountDisplay} (- Impulsos)"

#: src/pages/account-statuses.jsx:249
msgid "{accountDisplay} (#{tagged})"
msgstr "{accountDisplay} (#{tagged})"

#: src/pages/account-statuses.jsx:251
msgid "{accountDisplay} (Media)"
msgstr "{accountDisplay} (Mídia)"

#: src/pages/account-statuses.jsx:257
msgid "{accountDisplay} ({monthYear})"
msgstr "{accountDisplay} ({monthYear})"

#: src/pages/account-statuses.jsx:323
msgid "Clear filters"
msgstr "Limpar filtro"

#: src/pages/account-statuses.jsx:326
msgid "Clear"
msgstr "Limpar"

#: src/pages/account-statuses.jsx:340
msgid "Showing post with replies"
msgstr "Exibindo publicação com respostas"

#: src/pages/account-statuses.jsx:345
msgid "+ Replies"
msgstr "+ Respostas"

#: src/pages/account-statuses.jsx:351
msgid "Showing posts without boosts"
msgstr "Exibindo publicações sem impulsos"

#: src/pages/account-statuses.jsx:356
msgid "- Boosts"
msgstr "- Impulsos"

#: src/pages/account-statuses.jsx:362
msgid "Showing posts with media"
msgstr "Exibindo publicações com mídia"

#. placeholder {0}: tag.name
#: src/pages/account-statuses.jsx:379
msgid "Showing posts tagged with #{0}"
msgstr "Exibindo publicações marcadas com #{0}"

#. placeholder {0}: date.toLocaleString(i18n.locale, { month: 'long', year: 'numeric', })
#: src/pages/account-statuses.jsx:418
msgid "Showing posts in {0}"
msgstr "Exibindo publicações em {0}"

#: src/pages/account-statuses.jsx:508
msgid "Nothing to see here yet."
msgstr "Não há nada para ver aqui."

#: src/pages/account-statuses.jsx:509
#: src/pages/public.jsx:99
#: src/pages/trending.jsx:452
msgid "Unable to load posts"
msgstr "Impossível carregar publicações"

#: src/pages/account-statuses.jsx:550
#: src/pages/account-statuses.jsx:580
msgid "Unable to fetch account info"
msgstr "Impossível obter informações da conta"

#. placeholder {0}: accountInstance ? ( <> {' '} (<b>{punycode.toUnicode(accountInstance)}</b>) </> ) : null
#: src/pages/account-statuses.jsx:557
msgid "Switch to account's instance {0}"
msgstr "Alterar a instância da conta {0}"

#: src/pages/account-statuses.jsx:587
msgid "Switch to my instance (<0>{currentInstance}</0>)"
msgstr "Alterar para a minha instância (<0>{currentInstance}</0>)"

#: src/pages/account-statuses.jsx:660
msgid "Month"
msgstr "Mês"

#: src/pages/accounts.jsx:64
msgid "Current"
msgstr "Atual"

#: src/pages/accounts.jsx:110
msgid "Default"
msgstr "Padrão"

#: src/pages/accounts.jsx:132
msgid "Switch to this account"
msgstr "Alterar para esta conta"

#: src/pages/accounts.jsx:141
msgid "Switch in new tab/window"
msgstr "Alterar para nova janela/aba"

#: src/pages/accounts.jsx:155
msgid "View profile…"
msgstr "Ver perfil…"

#: src/pages/accounts.jsx:173
msgid "Set as default"
msgstr "Definir como padrão"

#. placeholder {0}: account.info.acct
#: src/pages/accounts.jsx:215
msgid "Log out <0>@{0}</0>?"
msgstr "Encerrar sessão <0>@{0}</0>?"

#: src/pages/accounts.jsx:244
msgid "Log out…"
msgstr "Encerrar sessão…"

#. placeholder {0}: niceDateTime(account.createdAt)
#: src/pages/accounts.jsx:251
msgid "Connected on {0} (<0/>)"
msgstr "Conectado em {0} (<0/>)"

#: src/pages/accounts.jsx:268
msgid "Add an existing account"
msgstr "Adicionar conta existente"

#: src/pages/accounts.jsx:275
msgid "Note: <0>Default</0> account will always be used for first load. Switched accounts will persist during the session."
msgstr "Nota: as contas <0>padrões</0> sempre serão as primeiras a carregar. Contas alteradas permaneceram durante a sessão."

#: src/pages/bookmarks.jsx:28
msgid "No bookmarks yet. Go bookmark something!"
msgstr "Não há favoritos ainda. Favorite algo primeiramente!"

#: src/pages/bookmarks.jsx:29
msgid "Unable to load bookmarks."
msgstr "Impossível carregar favoritos."

#: src/pages/catchup.jsx:54
msgid "last 1 hour"
msgstr "da última hora"

#: src/pages/catchup.jsx:55
msgid "last 2 hours"
msgstr "das últimas 2 horas"

#: src/pages/catchup.jsx:56
msgid "last 3 hours"
msgstr "das últimas 3 horas"

#: src/pages/catchup.jsx:57
msgid "last 4 hours"
msgstr "das últimas 4 horas"

#: src/pages/catchup.jsx:58
msgid "last 5 hours"
msgstr "das últimas 5 horas"

#: src/pages/catchup.jsx:59
msgid "last 6 hours"
msgstr "das últimas 6 horas"

#: src/pages/catchup.jsx:60
msgid "last 7 hours"
msgstr "das últimas 7 horas"

#: src/pages/catchup.jsx:61
msgid "last 8 hours"
msgstr "das últimas 8 horas"

#: src/pages/catchup.jsx:62
msgid "last 9 hours"
msgstr "das últimas 9 horas"

#: src/pages/catchup.jsx:63
msgid "last 10 hours"
msgstr "das últimas 10 horas"

#: src/pages/catchup.jsx:64
msgid "last 11 hours"
msgstr "das últimas 11 horas"

#: src/pages/catchup.jsx:65
msgid "last 12 hours"
msgstr "das últimas 12 horas"

#: src/pages/catchup.jsx:66
msgid "beyond 12 hours"
msgstr "além das últimas 12 horas"

#: src/pages/catchup.jsx:73
msgid "Followed tags"
msgstr "Tags seguidas"

#: src/pages/catchup.jsx:74
msgid "Groups"
msgstr "Grupos"

#: src/pages/catchup.jsx:597
msgid "Showing {selectedFilterCategory, select, all {all posts} original {original posts} replies {replies} boosts {boosts} followedTags {followed tags} groups {groups} filtered {filtered posts}}, {sortBy, select, createdAt {{sortOrder, select, asc {oldest} desc {latest}}} reblogsCount {{sortOrder, select, asc {fewest boosts} desc {most boosts}}} favouritesCount {{sortOrder, select, asc {fewest likes} desc {most likes}}} repliesCount {{sortOrder, select, asc {fewest replies} desc {most replies}}} density {{sortOrder, select, asc {least dense} desc {most dense}}}} first{groupBy, select, account {, grouped by authors} other {}}"
msgstr "Exibindo {selectedFilterCategory, select, all {todas as publicações} original {publicações originais} replies {respostas} boosts {impulsos} followedTags {tags seguidas} groups {grupos} filtered {publicações filtradas}}, {sortBy, select, createdAt {{sortOrder, select, asc {mais antigo} desc {mais recente}}} reblogsCount {{sortOrder, select, asc {menos impulsos} desc {mais impulsos}}} favouritesCount {{sortOrder, select, asc {menos curtidas} desc {mais curtidas}}} repliesCount {{sortOrder, select, asc {menos respostas} desc {mais respostas}}} density {{sortOrder, select, asc {menos denso} desc {mais denso}}}} primeiro{groupBy, select, account {, agrupado por autores} other {}}"

#: src/pages/catchup.jsx:889
#: src/pages/catchup.jsx:913
msgid "Catch-up <0>beta</0>"
msgstr "Reacompanhar conteúdo perdido <0>BETA</0>"

#: src/pages/catchup.jsx:903
#: src/pages/catchup.jsx:1588
msgid "Help"
msgstr "Ajuda"

#: src/pages/catchup.jsx:919
msgid "What is this?"
msgstr "O que é isso?"

#: src/pages/catchup.jsx:922
msgid "Catch-up is a separate timeline for your followings, offering a high-level view at a glance, with a simple, email-inspired interface to effortlessly sort and filter through posts."
msgstr "\"Reacompanhar\" é uma timeline diferente do seu seguindo, dando uma visão incrível num relance, com uma simples interface com ordenação e filtragem de publicações sem esforço."

#: src/pages/catchup.jsx:933
msgid "Preview of Catch-up UI"
msgstr "Prévia da interface de \"Reacompanho\""

#: src/pages/catchup.jsx:942
msgid "Let's catch up"
msgstr "Vamos reacompanhar"

#: src/pages/catchup.jsx:947
msgid "Let's catch up on the posts from your followings."
msgstr "Vamos reacompanhar as publicações perdidas."

#: src/pages/catchup.jsx:951
msgid "Show me all posts from…"
msgstr "Exiba as publicações que perdi de…"

#: src/pages/catchup.jsx:974
msgid "until the max"
msgstr "até o máximo"

#: src/pages/catchup.jsx:1004
msgid "Catch up"
msgstr "Reacompanhar"

#: src/pages/catchup.jsx:1010
msgid "Overlaps with your last catch-up"
msgstr "Sobrepõe com a sua última reacompanhada"

#. placeholder {0}: dtf.format(new Date(lastCatchupEndAt))
#: src/pages/catchup.jsx:1022
msgid "Until the last catch-up ({0})"
msgstr "Até a última reacompanhada ({0})"

#: src/pages/catchup.jsx:1031
msgid "Note: your instance might only show a maximum of 800 posts in the Home timeline regardless of the time range. Could be less or more."
msgstr "Aviso: Sua instância talvez possa só exibir até 800 publicações numa timeline inicial independente do tempo. Pode ser mais ou pode ser menos."

#: src/pages/catchup.jsx:1041
msgid "Previously…"
msgstr "Anteriormente…"

#. placeholder {0}: pc.count
#: src/pages/catchup.jsx:1059
msgid "{0, plural, one {# post} other {# posts}}"
msgstr "{0, plural, one {# publicação} other {# publicações}}"

#: src/pages/catchup.jsx:1069
msgid "Remove this catch-up?"
msgstr "Excluir reacompanhamento?"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1072
msgid "Removing Catch-up {0}"
msgstr "Excluindo reacompanhamento {0}"

#. placeholder {0}: pc.id
#: src/pages/catchup.jsx:1076
msgid "Catch-up {0} removed"
msgstr "Reacompanhamento {0} excluído"

#: src/pages/catchup.jsx:1090
msgid "Note: Only max 3 will be stored. The rest will be automatically removed."
msgstr "Aviso: este espaço só armazenará 3 reacompanhamentos. Mais que isso, os outros serão excluídos."

#: src/pages/catchup.jsx:1105
msgid "Fetching posts…"
msgstr "Obtendo publicações…"

#: src/pages/catchup.jsx:1108
msgid "This might take a while."
msgstr "Isso deve levar um tempo."

#: src/pages/catchup.jsx:1143
msgid "Reset filters"
msgstr "Redefinir filtros"

#: src/pages/catchup.jsx:1151
#: src/pages/catchup.jsx:1594
msgid "Top links"
msgstr "Links populares"

#. placeholder {0}: sharers.map((s) => { const { avatarStatic, displayName } = s; return ( <button type="button" class="plain" style={{ padding: 0, }} onClick={(e) => { e.preventDefault(); e.stopPropagation(); // Reset and filter to author const { id } = s; setSelectedAuthor(id); setSelectedFilterCategory('all'); }} > <Avatar url={avatarStatic} size="s" alt={displayName} /> </button> ); })
#: src/pages/catchup.jsx:1264
msgid "Shared by {0}"
msgstr "Compartilhado por {0}"

#: src/pages/catchup.jsx:1319
#: src/pages/mentions.jsx:154
#: src/pages/search.jsx:329
msgid "All"
msgstr "Tudo"

#. placeholder {0}: authorCountsList.length
#: src/pages/catchup.jsx:1404
msgid "{0, plural, one {# author} other {# authors}}"
msgstr "{0, plural, one {# autor} other {# autores}}"

#: src/pages/catchup.jsx:1416
msgid "Sort"
msgstr "Ordenar"

#: src/pages/catchup.jsx:1447
msgid "Date"
msgstr "Data"

#: src/pages/catchup.jsx:1451
msgid "Density"
msgstr "Densidade"

#. js-lingui-explicit-id
#: src/pages/catchup.jsx:1474
msgid "group.filter"
msgstr "Filtrar"

#: src/pages/catchup.jsx:1489
msgid "Authors"
msgstr "Autores"

#: src/pages/catchup.jsx:1490
msgid "None"
msgstr "Nenhum"

#: src/pages/catchup.jsx:1506
msgid "Show all authors"
msgstr "Mostrar autores"

#: src/pages/catchup.jsx:1557
msgid "You don't have to read everything."
msgstr "Não é preciso ler tudo."

#: src/pages/catchup.jsx:1558
msgid "That's all."
msgstr "Isso é tudo por agora."

#: src/pages/catchup.jsx:1566
msgid "Back to top"
msgstr "Voltar ao topo"

#: src/pages/catchup.jsx:1597
msgid "Links shared by followings, sorted by shared counts, boosts and likes."
msgstr "Links compartilhados por seguidores, ordenados pela contagem de compartilhamentos, impulsos e curtidas."

#: src/pages/catchup.jsx:1603
msgid "Sort: Density"
msgstr "Ordenar: Densidade"

#: src/pages/catchup.jsx:1606
msgid "Posts are sorted by information density or depth. Shorter posts are \"lighter\" while longer posts are \"heavier\". Posts with photos are \"heavier\" than posts without photos."
msgstr "As publicações são ordenadas por informações de densidade ou profundidade. As curtas são mais \"leves\" enquanto as longas são mais \"pesadas\". Publicações com fotos são mais \"pesadas\" do que elas sem fotos."

#: src/pages/catchup.jsx:1613
msgid "Group: Authors"
msgstr "Grupo: Autores"

#: src/pages/catchup.jsx:1616
msgid "Posts are grouped by authors, sorted by posts count per author."
msgstr "Publicações são agrupadas por autores, ordenado por quantidade de publicações por autor."

#: src/pages/catchup.jsx:1663
msgid "Next author"
msgstr "Próximo autor"

#: src/pages/catchup.jsx:1671
msgid "Previous author"
msgstr "Autor anterior"

#: src/pages/catchup.jsx:1687
msgid "Scroll to top"
msgstr "Subir"

#: src/pages/favourites.jsx:28
msgid "No likes yet. Go like something!"
msgstr "Não há curtidas ainda. Curta algo primeiramente!"

#: src/pages/favourites.jsx:29
msgid "Unable to load likes."
msgstr "Impossível carregar curtidas."

#: src/pages/filters.jsx:23
msgid "Home and lists"
msgstr "Início e listas"

#: src/pages/filters.jsx:25
msgid "Public timelines"
msgstr "Timelines públicas"

#: src/pages/filters.jsx:26
msgid "Conversations"
msgstr "Conversas"

#: src/pages/filters.jsx:27
msgid "Profiles"
msgstr "Perfis"

#: src/pages/filters.jsx:42
msgid "Never"
msgstr "Nunca"

#: src/pages/filters.jsx:104
#: src/pages/filters.jsx:229
msgid "New filter"
msgstr "Novo filtro"

#. placeholder {0}: filters.length
#: src/pages/filters.jsx:152
msgid "{0, plural, one {# filter} other {# filters}}"
msgstr "{0, plural, one {# filtro} other {# filtros}}"

#: src/pages/filters.jsx:167
msgid "Unable to load filters."
msgstr "Impossível carregar filtros."

#: src/pages/filters.jsx:171
msgid "No filters yet."
msgstr "Não há filtros ainda."

#: src/pages/filters.jsx:178
msgid "Add filter"
msgstr "Adicionar filtro"

#: src/pages/filters.jsx:229
msgid "Edit filter"
msgstr "Editar filtro"

#: src/pages/filters.jsx:346
msgid "Unable to edit filter"
msgstr "Impossível editar filtro"

#: src/pages/filters.jsx:347
msgid "Unable to create filter"
msgstr "Impossível criar filtro"

#: src/pages/filters.jsx:356
msgid "Title"
msgstr "Título"

#: src/pages/filters.jsx:397
msgid "Whole word"
msgstr "Palavra completa"

#: src/pages/filters.jsx:423
msgid "No keywords. Add one."
msgstr "Não há palavras-chave. Adicione uma."

#: src/pages/filters.jsx:450
msgid "Add keyword"
msgstr "Adicionar palavra-chave"

#. placeholder {0}: filteredEditKeywords.length
#: src/pages/filters.jsx:454
msgid "{0, plural, one {# keyword} other {# keywords}}"
msgstr "{0, plural, one {# palavra-chave} other {# palavras-chave}}"

#: src/pages/filters.jsx:467
msgid "Filter from…"
msgstr "Filtrar de…"

#: src/pages/filters.jsx:493
msgid "* Not implemented yet"
msgstr "* Não implementado ainda"

#: src/pages/filters.jsx:499
msgid "Status: <0><1/></0>"
msgstr "Estado: <0><1/></0>"

#: src/pages/filters.jsx:508
msgid "Change expiry"
msgstr "Alterar expiração"

#: src/pages/filters.jsx:508
msgid "Expiry"
msgstr "Expiração"

#: src/pages/filters.jsx:527
msgid "Filtered post will be…"
msgstr "A publicação filtrada será…"

#: src/pages/filters.jsx:538
msgid "obscured (media only)"
msgstr "oculto (apenas mídia)"

#: src/pages/filters.jsx:552
msgid "minimized"
msgstr "minimizada"

#: src/pages/filters.jsx:562
msgid "hidden"
msgstr "oculta"

#: src/pages/filters.jsx:579
msgid "Delete this filter?"
msgstr "Excluir filtro?"

#: src/pages/filters.jsx:592
msgid "Unable to delete filter."
msgstr "Impossível excluir filtro."

#: src/pages/filters.jsx:625
msgid "Expired"
msgstr "Expirado"

#: src/pages/filters.jsx:627
msgid "Expiring <0/>"
msgstr "Expirando <0/>"

#: src/pages/filters.jsx:631
msgid "Never expires"
msgstr "Nunca expira"

#. placeholder {0}: followedHashtags.length
#: src/pages/followed-hashtags.jsx:71
msgid "{0, plural, one {# hashtag} other {# hashtags}}"
msgstr "{0, plural, one {# hashtag} other {# hashtags}}"

#: src/pages/followed-hashtags.jsx:86
msgid "Unable to load followed hashtags."
msgstr "Incapaz de carregar hashtags seguidas."

#: src/pages/followed-hashtags.jsx:90
msgid "No hashtags followed yet."
msgstr "Não há hashtags seguidas ainda."

#: src/pages/following.jsx:147
msgid "Nothing to see here."
msgstr "Nada para ver aqui."

#: src/pages/following.jsx:148
#: src/pages/list.jsx:109
msgid "Unable to load posts."
msgstr "Impossível carregar publicações."

#: src/pages/hashtag.jsx:56
msgid "{hashtagTitle} (Media only) on {instance}"
msgstr "{hashtagTitle} (Apenas mídia) em {instance}"

#: src/pages/hashtag.jsx:57
msgid "{hashtagTitle} on {instance}"
msgstr "{hashtagTitle} em {instance}"

#: src/pages/hashtag.jsx:59
msgid "{hashtagTitle} (Media only)"
msgstr "{hashtagTitle} (Apenas mídia)"

#: src/pages/hashtag.jsx:60
msgid "{hashtagTitle}"
msgstr "{hashtagTitle}"

#: src/pages/hashtag.jsx:185
msgid "No one has posted anything with this tag yet."
msgstr "Ninguém publicou nada com esta tag."

#: src/pages/hashtag.jsx:186
msgid "Unable to load posts with this tag"
msgstr "Incapaz de carregar publicações com a tag"

#: src/pages/hashtag.jsx:212
msgid "Unfollow #{hashtag}?"
msgstr "Parar de seguir #{hashtag}?"

#: src/pages/hashtag.jsx:227
msgid "Unfollowed #{hashtag}"
msgstr "Parou de seguir #{hashtag}"

#: src/pages/hashtag.jsx:242
msgid "Followed #{hashtag}"
msgstr "#{hashtag} seguido"

#: src/pages/hashtag.jsx:258
msgid "Following…"
msgstr "Seguindo…"

#: src/pages/hashtag.jsx:287
msgid "Unfeatured on profile"
msgstr "Não destacado no perfil"

#: src/pages/hashtag.jsx:301
msgid "Unable to unfeature on profile"
msgstr "Impossível remover destaque do perfil"

#: src/pages/hashtag.jsx:310
#: src/pages/hashtag.jsx:326
msgid "Featured on profile"
msgstr "Destacado no perfil"

#: src/pages/hashtag.jsx:398
msgid "{TOTAL_TAGS_LIMIT, plural, other {Max # tags}}"
msgstr "{TOTAL_TAGS_LIMIT, plural, other {Máx. # tag(s)}}"

#: src/pages/hashtag.jsx:401
msgid "Add hashtag"
msgstr "Adicionar hashtag"

#: src/pages/hashtag.jsx:433
msgid "Remove hashtag"
msgstr "Excluir hashtag"

#: src/pages/hashtag.jsx:447
msgid "{SHORTCUTS_LIMIT, plural, one {Max # shortcut reached. Unable to add shortcut.} other {Max # shortcuts reached. Unable to add shortcut.}}"
msgstr "{SHORTCUTS_LIMIT, plural, one {Máx. # atalho alcançado. Impossível adicionar atalho.} other {Máx. # atalhos alcançados. Impossível adicionar atalhos.}}"

#: src/pages/hashtag.jsx:476
msgid "This shortcut already exists"
msgstr "Este atalho já existe"

#: src/pages/hashtag.jsx:479
msgid "Hashtag shortcut added"
msgstr "Atalho de hashtag adicionada"

#: src/pages/hashtag.jsx:485
msgid "Add to Shortcuts"
msgstr "Adicionar aos atalhos"

#: src/pages/hashtag.jsx:491
#: src/pages/public.jsx:141
#: src/pages/trending.jsx:481
msgid "Enter a new instance e.g. \"mastodon.social\""
msgstr "Insira uma nova instância ex. \"mastodon.social\""

#: src/pages/hashtag.jsx:494
#: src/pages/public.jsx:144
#: src/pages/trending.jsx:484
msgid "Invalid instance"
msgstr "Instância inválida"

#: src/pages/hashtag.jsx:508
#: src/pages/public.jsx:158
#: src/pages/trending.jsx:496
msgid "Go to another instance…"
msgstr "Alterar instância…"

#: src/pages/hashtag.jsx:521
#: src/pages/public.jsx:171
#: src/pages/trending.jsx:507
msgid "Go to my instance (<0>{currentInstance}</0>)"
msgstr "Ir à minha instância (<0>{currentInstance}</0>)"

#: src/pages/home.jsx:224
msgid "Unable to fetch notifications."
msgstr "Impossível obter notificações."

#: src/pages/home.jsx:245
msgid "<0>New</0> <1>Follow Requests</1>"
msgstr "<0>Novas</0> <1>Solicitações de seguimento</1>"

#: src/pages/home.jsx:251
msgid "See all"
msgstr "Ver tudo"

#: src/pages/http-route.jsx:68
msgid "Resolving…"
msgstr "Resolvendo…"

#: src/pages/http-route.jsx:79
msgid "Unable to resolve URL"
msgstr "Impossível resolver URL"

#: src/pages/list.jsx:108
msgid "Nothing yet."
msgstr "Nada ainda."

#: src/pages/list.jsx:201
#: src/pages/list.jsx:307
msgid "Manage members"
msgstr "Gerenciar membros"

#. placeholder {0}: account.username
#: src/pages/list.jsx:342
msgid "Remove <0>@{0}</0> from list?"
msgstr "Excluir <0>@{0}</0> da lista?"

#: src/pages/list.jsx:388
msgid "Remove…"
msgstr "Excluir…"

#. placeholder {0}: lists.length
#: src/pages/lists.jsx:115
msgid "{0, plural, one {# list} other {# lists}}"
msgstr "{0, plural, one {# lista} other {# listas}}"

#: src/pages/lists.jsx:135
msgid "No lists yet."
msgstr "Não há listas ainda."

#: src/pages/login.jsx:122
#: src/pages/login.jsx:132
msgid "Failed to register application"
msgstr "Houve um erro ao registrar aplicação"

#: src/pages/login.jsx:218
msgid "instance domain"
msgstr "domínio de instância"

#: src/pages/login.jsx:242
msgid "e.g. “mastodon.social”"
msgstr "ex. “mastodon.social”"

#: src/pages/login.jsx:253
msgid "Failed to log in. Please try again or try another instance."
msgstr "Houve um erro ao registrar-se. Tente novamente ou tente outra instância."

#: src/pages/login.jsx:265
msgid "Continue with {selectedInstanceText}"
msgstr "Continuar com {selectedInstanceText}"

#: src/pages/login.jsx:266
msgid "Continue"
msgstr "Continuar"

#: src/pages/login.jsx:274
msgid "Don't have an account? Create one!"
msgstr "Não tem uma conta? Crie uma!"

#: src/pages/mentions.jsx:21
msgid "Private mentions"
msgstr "Menções privadas"

#: src/pages/mentions.jsx:166
msgid "Private"
msgstr "Privado"

#: src/pages/mentions.jsx:176
msgid "No one mentioned you :("
msgstr "Ninguém mencionou você :("

#: src/pages/mentions.jsx:177
msgid "Unable to load mentions."
msgstr "Impossível carregar menções."

#: src/pages/notifications.jsx:108
msgid "You don't follow"
msgstr "Você não segue"

#: src/pages/notifications.jsx:109
msgid "Who don't follow you"
msgstr "Quem não segue você"

#: src/pages/notifications.jsx:110
msgid "With a new account"
msgstr "Com uma conta nova"

#: src/pages/notifications.jsx:111
msgid "Who unsolicitedly private mention you"
msgstr "Quem mencionou você privadamente sem solicitar"

#: src/pages/notifications.jsx:112
msgid "Who are limited by server moderators"
msgstr "Quem está limitado por moderadores do servidor"

#: src/pages/notifications.jsx:605
#: src/pages/notifications.jsx:946
msgid "Notifications settings"
msgstr "Opções de notificação"

#: src/pages/notifications.jsx:623
msgid "New notifications"
msgstr "Novas notificações"

#. placeholder {0}: announcements.length
#: src/pages/notifications.jsx:634
msgid "{0, plural, one {Announcement} other {Announcements}}"
msgstr "{0, plural, one {Anúncio} other {Anúncios}}"

#: src/pages/notifications.jsx:681
#: src/pages/settings.jsx:1206
msgid "Follow requests"
msgstr "Solicitações de seguimento"

#. placeholder {0}: followRequests.length
#: src/pages/notifications.jsx:686
msgid "{0, plural, one {# follow request} other {# follow requests}}"
msgstr "{0, plural, one {# solicitação de seguimento} other {# solicitações de seguimento}}"

#. placeholder {0}: notificationsPolicy.summary.pendingRequestsCount
#: src/pages/notifications.jsx:741
msgid "{0, plural, one {Filtered notifications from # person} other {Filtered notifications from # people}}"
msgstr "{0, plural, one {Notificações filtradas de # pessoa} other {Notificações filtradas de # pessoas}}"

#: src/pages/notifications.jsx:814
msgid "Only mentions"
msgstr "Apenas menções"

#: src/pages/notifications.jsx:818
msgid "Today"
msgstr "Hoje"

#: src/pages/notifications.jsx:823
msgid "You're all caught up."
msgstr "Está tudo em dia."

#: src/pages/notifications.jsx:846
msgid "Yesterday"
msgstr "Ontem"

#: src/pages/notifications.jsx:894
msgid "Unable to load notifications"
msgstr "Impossível carregar notificações"

#: src/pages/notifications.jsx:973
msgid "Notifications settings updated"
msgstr "Opções de notificação atualizada"

#: src/pages/notifications.jsx:981
msgid "Filter out notifications from people:"
msgstr "Filtrar notificações de pessoas:"

#: src/pages/notifications.jsx:995
msgid "Filter"
msgstr "Filtrar"

#: src/pages/notifications.jsx:998
msgid "Ignore"
msgstr "Ignorar"

#. placeholder {0}: niceDateTime(updatedAtDate)
#: src/pages/notifications.jsx:1071
msgid "Updated <0>{0}</0>"
msgstr "Atualizado <0>{0}</0>"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1139
msgid "View notifications from <0>@{0}</0>"
msgstr "Ver notificações de <0>@{0}</0>"

#. placeholder {0}: account.username
#: src/pages/notifications.jsx:1160
msgid "Notifications from <0>@{0}</0>"
msgstr "Notificações de <0>@{0}</0>"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1228
msgid "Notifications from @{0} will not be filtered from now on."
msgstr "As notificações de @{0} não serão mais filtradas."

#: src/pages/notifications.jsx:1233
msgid "Unable to accept notification request"
msgstr "Impossível aceitar pedido de notificação"

#: src/pages/notifications.jsx:1238
msgid "Allow"
msgstr "Permitir"

#. placeholder {0}: request.account.username
#: src/pages/notifications.jsx:1258
msgid "Notifications from @{0} will not show up in Filtered notifications from now on."
msgstr "As notificações de @{0} não aparecerá em notificações filtradas."

#: src/pages/notifications.jsx:1263
msgid "Unable to dismiss notification request"
msgstr "Impossível descartar pedido de notificação"

#: src/pages/notifications.jsx:1268
msgid "Dismiss"
msgstr "Descartar"

#: src/pages/notifications.jsx:1283
msgid "Dismissed"
msgstr "Descartado"

#: src/pages/public.jsx:28
msgid "Local timeline ({instance})"
msgstr "Timeline Local ({instance})"

#: src/pages/public.jsx:29
msgid "Federated timeline ({instance})"
msgstr "Timeline Federada ({instance})"

#: src/pages/public.jsx:92
msgid "Local timeline"
msgstr "Timeline Local"

#: src/pages/public.jsx:92
msgid "Federated timeline"
msgstr "Timeline Federada"

#: src/pages/public.jsx:98
msgid "No one has posted anything yet."
msgstr "Ninguém publicou nada ainda."

#: src/pages/public.jsx:125
msgid "Switch to Federated"
msgstr "Alterar para federado"

#: src/pages/public.jsx:132
msgid "Switch to Local"
msgstr "Alterar para local"

#: src/pages/scheduled-posts.jsx:110
msgid "No scheduled posts."
msgstr "Sem publicações agendadas."

#. Scheduled [in 1 day] ([Thu, Feb 27, 6:30:00 PM])
#. placeholder {0}: niceDateTime(scheduledAt, { formatOpts: { weekday: 'short', second: 'numeric', }, })
#: src/pages/scheduled-posts.jsx:207
msgid "Scheduled <0><1/></0> <2>({0})</2>"
msgstr "Agendado <0><1/></0> <2>({0})</2>"

#. Scheduled [in 1 day]
#: src/pages/scheduled-posts.jsx:263
msgid "Scheduled <0><1/></0>"
msgstr "Agendado <0><1/></0>"

#: src/pages/scheduled-posts.jsx:308
msgid "Scheduled post rescheduled"
msgstr "Publicação agendada reagendada"

#: src/pages/scheduled-posts.jsx:315
msgid "Failed to reschedule post"
msgstr "Falhou ao reagendar publicação"

#: src/pages/scheduled-posts.jsx:338
msgid "Reschedule"
msgstr "Reagendar"

#: src/pages/scheduled-posts.jsx:344
msgid "Delete scheduled post?"
msgstr "Excluir publicação agendada?"

#: src/pages/scheduled-posts.jsx:352
msgid "Scheduled post deleted"
msgstr "Publicação agendada excluída"

#: src/pages/scheduled-posts.jsx:359
msgid "Failed to delete scheduled post"
msgstr "Falhou ao excluir publicação agendada"

#: src/pages/search.jsx:50
msgid "Search: {q} (Posts)"
msgstr "Procurar: {q} (Publicações)"

#: src/pages/search.jsx:53
msgid "Search: {q} (Accounts)"
msgstr "Buscar: {q} (Contas)"

#: src/pages/search.jsx:56
msgid "Search: {q} (Hashtags)"
msgstr "Buscar: {q} (Hashtags)"

#: src/pages/search.jsx:59
msgid "Search: {q}"
msgstr "Buscar: {q}"

#: src/pages/search.jsx:339
#: src/pages/search.jsx:421
msgid "Hashtags"
msgstr "Hashtags"

#: src/pages/search.jsx:371
#: src/pages/search.jsx:425
#: src/pages/search.jsx:495
msgid "See more"
msgstr "Ver mais"

#: src/pages/search.jsx:397
msgid "See more accounts"
msgstr "Ver mais contas"

#: src/pages/search.jsx:411
msgid "No accounts found."
msgstr "Nenhuma conta encontrada."

#: src/pages/search.jsx:467
msgid "See more hashtags"
msgstr "Ver mais hashtags"

#: src/pages/search.jsx:481
msgid "No hashtags found."
msgstr "Nenhuma hashtag encontrada."

#: src/pages/search.jsx:525
msgid "See more posts"
msgstr "Ver mais publicações"

#: src/pages/search.jsx:539
msgid "No posts found."
msgstr "Nenhuma publicação encontrada."

#: src/pages/search.jsx:583
msgid "Enter your search term or paste a URL above to get started."
msgstr "Insira o termo de sua busca ou copie um URL acima para iniciar."

#: src/pages/settings.jsx:95
msgid "Settings"
msgstr "Opções"

#: src/pages/settings.jsx:104
msgid "Appearance"
msgstr "Tema"

#: src/pages/settings.jsx:180
msgid "Light"
msgstr "Claro"

#: src/pages/settings.jsx:191
msgid "Dark"
msgstr "Escuro"

#: src/pages/settings.jsx:204
msgid "Auto"
msgstr "Automático"

#: src/pages/settings.jsx:214
msgid "Text size"
msgstr "Tamanho do texto"

#. Preview of one character, in smallest size
#. Preview of one character, in largest size
#: src/pages/settings.jsx:219
#: src/pages/settings.jsx:244
msgid "A"
msgstr "A"

#: src/pages/settings.jsx:258
msgid "Display language"
msgstr "Idioma de exibição"

#: src/pages/settings.jsx:267
msgid "Volunteer translations"
msgstr "Traduzido por voluntários!"

#: src/pages/settings.jsx:278
msgid "Posting"
msgstr "Publicando"

#: src/pages/settings.jsx:285
msgid "Default visibility"
msgstr "Visibilidade padrão"

#: src/pages/settings.jsx:286
#: src/pages/settings.jsx:332
msgid "Synced"
msgstr "Sincronizado"

#: src/pages/settings.jsx:311
msgid "Failed to update posting privacy"
msgstr "Houve um erro ao atualizar privacidade de publicação"

#: src/pages/settings.jsx:334
msgid "Synced to your instance server's settings. <0>Go to your instance ({instance}) for more settings.</0>"
msgstr "Sincronizado com as opções do servidor de instância. <0>Vá para sua instância ({instance}) para mais opções.</0>"

#: src/pages/settings.jsx:349
msgid "Experiments"
msgstr "Experimentos"

#: src/pages/settings.jsx:362
msgid "Auto refresh timeline posts"
msgstr "Atualizar automaticamente publicações da timeline"

#: src/pages/settings.jsx:374
msgid "Boosts carousel"
msgstr "Carrossel de impulsos"

#: src/pages/settings.jsx:391
msgid "Post translation"
msgstr "Tradução da publicação"

#: src/pages/settings.jsx:402
msgid "Translate to "
msgstr "Traduzir para "

#: src/pages/settings.jsx:413
msgid "System language ({systemTargetLanguageText})"
msgstr "Idioma do sistema ({systemTargetLanguageText})"

#. placeholder {0}: snapStates.settings.contentTranslationHideLanguages .length
#: src/pages/settings.jsx:439
msgid "{0, plural, =0 {Hide \"Translate\" button for:} other {Hide \"Translate\" button for (#):}}"
msgstr "{0, plural, =0 {Ocultar o botão de \"Traduzir\" em:} other {Ocultar o botão de \"Traduzir\" por (#):}}"

#: src/pages/settings.jsx:494
msgid "Note: This feature uses external translation services, powered by <0>{TRANSLATION_API_NAME}</0>."
msgstr "Nota: este recurso usa serviços de tradução externa, feito por <0>{TRANSLATION_API_NAME}</0>."

#: src/pages/settings.jsx:522
msgid "Auto inline translation"
msgstr "Tradução automática"

#: src/pages/settings.jsx:526
msgid "Automatically show translation for posts in timeline. Only works for <0>short</0> posts without content warning, media and poll."
msgstr "Exibe tradução automaticamente para publicações na timeline. Somente funciona para publicações <0>curtas</0>, sem aviso de conteúdo, mídia ou enquete."

#: src/pages/settings.jsx:547
msgid "GIF Picker for composer"
msgstr "Selecionador de GIF para escrita"

#: src/pages/settings.jsx:551
msgid "Note: This feature uses external GIF search service, powered by <0>GIPHY</0>. G-rated (suitable for viewing by all ages), tracking parameters are stripped, referrer information is omitted from requests, but search queries and IP address information will still reach their servers."
msgstr "Nota: Esse recurso usa serviços externos de busca de GIF, desenvolvido por <0>GIPHY</0>. Avaliado bem (adequado para visualização de todas as idades), parâmetros de rastreamento são listrados, informação de referência é omitida de solicitações, porém as consultas de busca e informação de endereço IP ainda poderá alcançar os servidores do GIPHY."

#: src/pages/settings.jsx:580
msgid "Image description generator"
msgstr "Gerador de descrição de imagem"

#: src/pages/settings.jsx:585
msgid "Only for new images while composing new posts."
msgstr "Apenas para novas imagens enquanto escreve novas publicações."

#: src/pages/settings.jsx:592
msgid "Note: This feature uses external AI service, powered by <0>img-alt-api</0>. May not work well. Only for images and in English."
msgstr "Nota: Esse recurso usa serviços externos IA, desenvolvido por <0>img-alt-api</0>. Então ele pode não funcionar bem. Apenas para imagens e em inglês."

#: src/pages/settings.jsx:618
msgid "Server-side grouped notifications"
msgstr "Notificações agrupadas do servidor"

#: src/pages/settings.jsx:622
msgid "Alpha-stage feature. Potentially improved grouping window but basic grouping logic."
msgstr "Recurso em estágio alpha. Janela potencialmente agrupada e melhorada, porém lógica básica de agrupamento."

#: src/pages/settings.jsx:643
msgid "\"Cloud\" import/export for shortcuts settings"
msgstr "Opções de exportação/importação \"nuvem\" para atalhos"

#: src/pages/settings.jsx:648
msgid "⚠️⚠️⚠️ Very experimental.<0/>Stored in your own profile’s notes. Profile (private) notes are mainly used for other profiles, and hidden for own profile."
msgstr "⚠️⚠️⚠️ Muito experimental.<0/>Armazenado nas notas do seu perfil. Notas (privadas) do perfil são mais usadas para outros perfis, e oculto para o próprio perfil."

#: src/pages/settings.jsx:659
msgid "Note: This feature uses currently-logged-in instance server API."
msgstr "Nota: Esse recurso usa a API do servidor de instância atualmente em uso."

#: src/pages/settings.jsx:676
msgid "Cloak mode <0>(<1>Text</1> → <2>████</2>)</0>"
msgstr "Modo oculto <0>(<1>Texto</1> → <2>████</2>)</0>"

#: src/pages/settings.jsx:685
msgid "Replace text as blocks, useful when taking screenshots, for privacy reasons."
msgstr "Substituir os textos por blocos, útil ao fazer capturas de tela, por razões de privacidade."

#: src/pages/settings.jsx:710
msgid "About"
msgstr "Sobre"

#: src/pages/settings.jsx:749
msgid "<0>Built</0> by <1>@cheeaun</1>"
msgstr "<0>Feito</0> por <1>@cheeaun</1>"

#: src/pages/settings.jsx:778
msgid "Sponsor"
msgstr "Patrocinador"

#: src/pages/settings.jsx:786
msgid "Donate"
msgstr "Doar"

#: src/pages/settings.jsx:802
msgid "What's new"
msgstr "Novidades"

#: src/pages/settings.jsx:806
msgid "Privacy Policy"
msgstr "Política de privacidade"

#. placeholder {0}: WEBSITE.replace(/https?:\/\//g, '').replace(/\/$/, '')
#: src/pages/settings.jsx:813
msgid "<0>Site:</0> {0}"
msgstr "<0>Site:</0> {0}"

#. placeholder {0}: !__FAKE_COMMIT_HASH__ && ( <span class="ib insignificant"> ( <a href={`https://github.com/cheeaun/phanpy/commit/${__COMMIT_HASH__}`} target="_blank" rel="noopener" > <RelativeTime datetime={new Date(__BUILD_TIME__)} /> </a> ) </span> )
#: src/pages/settings.jsx:820
msgid "<0>Version:</0> <1/> {0}"
msgstr "<0>Versão:</0> <1/> {0}"

#: src/pages/settings.jsx:835
msgid "Version string copied"
msgstr "Versão copiada"

#: src/pages/settings.jsx:838
msgid "Unable to copy version string"
msgstr "Impossível copiar versão"

#: src/pages/settings.jsx:1103
#: src/pages/settings.jsx:1108
msgid "Failed to update subscription. Please try again."
msgstr "Houve um erro ao atualizar inscrição. Tente novamente."

#: src/pages/settings.jsx:1114
msgid "Failed to remove subscription. Please try again."
msgstr "Houve um erro ao cancelar inscrição. Tente novamente."

#: src/pages/settings.jsx:1121
msgid "Push Notifications (beta)"
msgstr "Notificações de push (beta)"

#: src/pages/settings.jsx:1143
msgid "Push notifications are blocked. Please enable them in your browser settings."
msgstr "As notificações de push estão bloqueadas. Por favor, ative-as nas opções do navegador."

#. placeholder {0}: [ { value: 'all', label: t`anyone`, }, { value: 'followed', label: t`people I follow`, }, { value: 'follower', label: t`followers`, }, ].map((type) => ( <option value={type.value}>{type.label}</option> ))
#: src/pages/settings.jsx:1152
msgid "Allow from <0>{0}</0>"
msgstr "Permitir de <0>{0}</0>"

#: src/pages/settings.jsx:1161
msgid "anyone"
msgstr "qualquer um"

#: src/pages/settings.jsx:1165
msgid "people I follow"
msgstr "pessoas que sigo"

#: src/pages/settings.jsx:1169
msgid "followers"
msgstr "seguidores"

#: src/pages/settings.jsx:1202
msgid "Follows"
msgstr "Segue"

#: src/pages/settings.jsx:1210
msgid "Polls"
msgstr "Enquetes"

#: src/pages/settings.jsx:1214
msgid "Post edits"
msgstr "Publicar edições"

#: src/pages/settings.jsx:1235
msgid "Push permission was not granted since your last login. You'll need to <0><1>log in</1> again to grant push permission</0>."
msgstr "A permissão de push não foi concedida desde a última sessão. Você precisa <0><1>iniciar sessão</1> novamente para conceder a permissão</0>."

#: src/pages/settings.jsx:1251
msgid "NOTE: Push notifications only work for <0>one account</0>."
msgstr "NOTA: Notificações de push só funcionam para <0>uma conta</0>."

#. js-lingui-explicit-id
#: src/pages/status.jsx:657
#: src/pages/status.jsx:1220
msgid "post.title"
msgstr "Publicação"

#: src/pages/status.jsx:911
msgid "You're not logged in. Interactions (reply, boost, etc) are not possible."
msgstr "Você não está conectado. Interações como (respostas, impulsos, etc.) não são possíveis."

#: src/pages/status.jsx:931
msgid "This post is from another instance (<0>{instance}</0>). Interactions (reply, boost, etc) are not possible."
msgstr "Essa publicação é de outra instância (<0>{instance}</0>). Interações como (respostas, impulsos, etc.) não são possíveis."

#: src/pages/status.jsx:959
msgid "Error: {e}"
msgstr "Erro: {e}"

#: src/pages/status.jsx:966
msgid "Switch to my instance to enable interactions"
msgstr "Alterar para minha instância para ativar interações"

#: src/pages/status.jsx:1068
msgid "Unable to load replies."
msgstr "Impossível carregar respostas."

#: src/pages/status.jsx:1180
msgid "Back"
msgstr "Voltar"

#: src/pages/status.jsx:1211
msgid "Go to main post"
msgstr "Ir à publicação principal"

#. placeholder {0}: ancestors.length
#: src/pages/status.jsx:1234
msgid "{0} posts above ‒ Go to top"
msgstr "{0} publicações acima – Voltar ao topo"

#: src/pages/status.jsx:1281
#: src/pages/status.jsx:1344
msgid "Switch to Side Peek view"
msgstr "Alterar para vista lateral"

#: src/pages/status.jsx:1345
msgid "Switch to Full view"
msgstr "Alterar para vista completa"

#: src/pages/status.jsx:1363
msgid "Show all sensitive content"
msgstr "Mostrar conteúdo sensível"

#: src/pages/status.jsx:1368
msgid "Experimental"
msgstr "Experimental"

#: src/pages/status.jsx:1377
msgid "Unable to switch"
msgstr "Impossível alterar"

#. placeholder {0}: punycode.toUnicode( postInstance, )
#: src/pages/status.jsx:1384
msgid "Switch to post's instance ({0})"
msgstr "Alterar para instância da publicação ({0})"

#: src/pages/status.jsx:1387
msgid "Switch to post's instance"
msgstr "Alterar para instância da publicação"

#: src/pages/status.jsx:1445
msgid "Unable to load post"
msgstr "Impossível carregar publicação"

#. placeholder {0}: replies.length
#. placeholder {1}: shortenNumber(replies.length)
#: src/pages/status.jsx:1581
msgid "{0, plural, one {# reply} other {<0>{1}</0> replies}}"
msgstr "{0, plural, one {# resposta} other {<0>{1}</0> respostas}}"

#. placeholder {0}: shortenNumber(totalComments)
#: src/pages/status.jsx:1599
msgid "{totalComments, plural, one {# comment} other {<0>{0}</0> comments}}"
msgstr "{totalComments, plural, one {# comentário} other {<0>{0}</0> comentários}}"

#: src/pages/status.jsx:1621
msgid "View post with its replies"
msgstr "Ver publicação com respostas"

#: src/pages/trending.jsx:77
msgid "Trending ({instance})"
msgstr "Em Alta ({instance})"

#: src/pages/trending.jsx:235
msgid "Trending News"
msgstr "Novidades em alta"

#. By [Author]
#. placeholder {0}: author ? ( <NameText account={author} showAvatar /> ) : authorUrl ? ( <a href={authorUrl} target="_blank" rel="noopener" > {authorName} </a> ) : ( authorName )
#: src/pages/trending.jsx:350
msgid "By {0}"
msgstr "Por {0}"

#: src/pages/trending.jsx:411
msgid "Back to showing trending posts"
msgstr "Retomar às publicações em alta"

#. placeholder {0}: currentLink .replace(/^https?:\/\/(www\.)?/i, '') .replace(/\/$/, '')
#: src/pages/trending.jsx:416
msgid "Showing posts mentioning <0>{0}</0>"
msgstr "Mostrar publicações mencionando <0>{0}</0>"

#: src/pages/trending.jsx:428
msgid "Trending posts"
msgstr "Publicações em alta"

#: src/pages/trending.jsx:451
msgid "No trending posts."
msgstr "Não há publicações em alta."

#: src/pages/welcome.jsx:54
msgid "A minimalistic opinionated Mastodon web client."
msgstr "Um cliente web minimalista e original para Mastodon."

#: src/pages/welcome.jsx:65
msgid "Log in with Mastodon"
msgstr "Registrar com Mastodon"

#: src/pages/welcome.jsx:71
msgid "Sign up"
msgstr "Registrar-se"

#: src/pages/welcome.jsx:78
msgid "Connect your existing Mastodon/Fediverse account.<0/>Your credentials are not stored on this server."
msgstr "Conecte com sua conta existente do Mastodon/Fediverse.<0/>Os seus credenciais não serão armazenados neste servidor."

#: src/pages/welcome.jsx:95
msgid "<0>Built</0> by <1>@cheeaun</1>. <2>Privacy Policy</2>."
msgstr "<0>Feito</0> por <1>@cheeaun</1>. <2>Política de privacidade</2>."

#: src/pages/welcome.jsx:126
msgid "Screenshot of Boosts Carousel"
msgstr "Captura de tela do carrossel de impulsos"

#: src/pages/welcome.jsx:130
msgid "Boosts Carousel"
msgstr "Carrossel de impulsos"

#: src/pages/welcome.jsx:133
msgid "Visually separate original posts and re-shared posts (boosted posts)."
msgstr "Separa visualmente as publicações originais das compartilhadas (publicações impulsionadas)."

#: src/pages/welcome.jsx:142
msgid "Screenshot of nested comments thread"
msgstr "Captura de tela de tópicos de comentários aninhados"

#: src/pages/welcome.jsx:146
msgid "Nested comments thread"
msgstr "Tópico de comentários aninhados"

#: src/pages/welcome.jsx:149
msgid "Effortlessly follow conversations. Semi-collapsible replies."
msgstr "Siga facilmente as conversas. Respostas semi-colapsáveis."

#: src/pages/welcome.jsx:157
msgid "Screenshot of grouped notifications"
msgstr "Captura de tela de notificações agrupadas"

#: src/pages/welcome.jsx:161
msgid "Grouped notifications"
msgstr "Notificações agrupadas"

#: src/pages/welcome.jsx:164
msgid "Similar notifications are grouped and collapsed to reduce clutter."
msgstr "Notificações similares são agrupadas e colapsadas para evitar desordem."

#: src/pages/welcome.jsx:173
msgid "Screenshot of multi-column UI"
msgstr "Captura de tela da interface multi-coluna"

#: src/pages/welcome.jsx:177
msgid "Single or multi-column"
msgstr "Única ou multi-coluna"

#: src/pages/welcome.jsx:180
msgid "By default, single column for zen-mode seekers. Configurable multi-column for power users."
msgstr "Por padrão, uma única coluna para amadores do modo zen. Multi-coluna configurável para usuários avançados."

#: src/pages/welcome.jsx:189
msgid "Screenshot of multi-hashtag timeline with a form to add more hashtags"
msgstr "Captura de tela duma timeline multi-hashtag com uma maneira de adicionar mais"

#: src/pages/welcome.jsx:193
msgid "Multi-hashtag timeline"
msgstr "Timeline multi-hashtag"

#: src/pages/welcome.jsx:196
msgid "Up to 5 hashtags combined into a single timeline."
msgstr "Até 5 hashtags juntas numa timeline única."

#: src/utils/open-compose.js:24
msgid "Looks like your browser is blocking popups."
msgstr "Parece que o seu navegador está bloqueando popups."

#: src/utils/show-compose.js:16
msgid "A draft post is currently minimized. Post or discard it before creating a new one."
msgstr "Uma publicação rascunho está atualmente minimizada. Publique-a ou a descarte antes de criar outra."

#: src/utils/show-compose.js:21
msgid "A post is currently open. Post or discard it before creating a new one."
msgstr "Uma publicação está atualmente aberta. Publique-a ou a descarte antes de criar outra."

